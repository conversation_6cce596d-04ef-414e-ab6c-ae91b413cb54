<template>
  <div class="header-box">
    <div class="system-name" @click="changeSystem">{{ page.systemInfo.title }}</div>
    <menu-first ref="menuFirstRef" @menu-click="menuClick"></menu-first>
  </div>
</template>

<script setup>
import { ElMessage as elMessage } from "element-plus";
import { reactive, inject, onMounted, ref } from "vue";
import menuFirst from "./menuFirst.vue";

const $axios = inject("$axios");

// 定义emit
const emits = defineEmits(["menu-click"]);

// 定义ref
const menuFirstRef = ref(null);

// 定义数据
var page = reactive({
  systemInfo: {}, // 系统信息
});

// 切换系统
function changeSystem() {
  // window.open(window.ipConfig.cqUrl.mainUrl, "_self");
  var systemChangeUrl = JSON.parse(localStorage.getItem("cqcpSystemInfo")).systemChangeUrl;
  window.open(systemChangeUrl, "_self");
}

// 设置一级菜单数据
function setFirstMenuData(menus) {
  menuFirstRef.value.setData(menus);
}

// 一级菜单点击
function menuClick(menuId) {
  emits("menu-click", menuId);
}

var cqcpSystemInfo = JSON.parse(localStorage.getItem("cqcpSystemInfo"));
page.systemInfo.title = cqcpSystemInfo.systemName + "·" + cqcpSystemInfo.functionName;

defineExpose({
  setFirstMenuData,
});
</script>
<style lang="scss" scoped>
// 优设标题黑
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("@/assets/fonts/YouSheBiaoTiHei.ttf");
}

.header-box {
  width: 100%;
  height: auto;
  background-color: #0b8df1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 25px;
  padding-right: 25px;

  .system-name {
    width: auto;
    height: 60px;
    line-height: 60px;
    font-size: 24px;
    color: #ffffff;
    margin-right: 15px;
    cursor: pointer;
    white-space: nowrap;
    // font-family: YouSheBiaoTiHei;
    font-weight: bold;
    font-family: Microsoft YaHei;
  }
}
</style>
