<template>
  <!-- 结果变更 -->
  <div class="list-box">
    <div class="left-box">
      <div class="form-box">
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
            >基本信息</span
          >
          <table
            width="100%"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
            style="margin-top: 15px"
          >
            <tbody style="display: flex; flex-wrap: wrap">
              <tr style="width: 100%">
                <td class="title" width="360">项目名称</td>
                <td style="width: 87.5%">
                  <span class="text1">{{ resultChangeForm.projectName }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">项目编号</td>
                <td style="width: 75%">
                  <span class="text1">{{ resultChangeForm.projectCode }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">标段编号</td>
                <td style="width: 75%">
                  <span class="text1">{{ resultChangeForm.tenderCode }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="form-box2">
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
            >变更信息</span
          >
          <div class="form-box-row">
            <el-form ref="formRef" :model="resultForm" :rules="rules">
              <table>
                <tr>
                  <td class="title require">操作人</td>
                  <td>
                    <el-form-item prop="contactPerson" style="margin-top: 10px">
                      <nd-input
                        width="100%"
                        v-model="resultChangeForm.operator"
                        disabled
                      />
                    </el-form-item>
                  </td>
                  <td class="title require">操作时间</td>
                  <td>
                    <el-form-item prop="contactPhone" style="margin-top: 10px">
                      <nd-input
                        width="100%"
                        v-model="resultChangeForm.operatorTime"
                        disabled
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td class="title require">变更原因</td>
                  <td colspan="3">
                    <el-form-item prop="changeReason" style="margin-top: 10px">
                      <nd-input
                        type="textarea"
                        width="100%"
                        v-model="resultForm.changeReason"
                        placeholder="请输入"
                        maxlength="500"
                      />
                    </el-form-item>
                  </td>
                </tr>
              </table>

              <nd-table
                :data="resData.list"
                class="list-table-box"
                height="100%"
                style="width: 100%; margin-top: 10px"
              >
                <el-table-column
                  align="center"
                  label="变更事项"
                  width="160"
                  border
                >
                  <template #default="{ row }">
                    <el-form-item prop="changeItems">
                      <nd-select
                        :teleported="true"
                        v-model="resultForm.changeItems"
                        placeholder="请选择"
                        style="width: 100%"
                        @change="selectChange($event, $index, row)"
                      >
                        <el-option
                          v-for="item in resData.optionList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </nd-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="变更前信息">
                  <template #default="{ row }">
                    <div
                      v-if="resultForm.changeItems == 1"
                      class="row-box row-box2"
                    >
                      <div class="itemChild">
                        成交方：<br />
                        <nd-select
                          :teleported="true"
                          v-model="
                            resultOldChangeForm.sourceFieldInfoVo.signupId
                          "
                          placeholder="请选择"
                          style="width: 100%"
                          disabled
                        >
                          <el-option
                            v-for="item in resultChangeForm.tradeSignupTradeVoList"
                            :label="item.signupName"
                            :value="item.signupId"
                            :key="item.signupId"
                          />
                        </nd-select>
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.traderPerson
                        }} -->
                      </div>
                      <div class="itemChild">
                        成交日期：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradeTime
                          "
                          disabled
                        />
                      </div>
                      <div class="itemChild">
                        <span
                          >成交价<i
                            v-if="resultOldChangeForm.tradeUnit_dictText"
                            style="font-style: normal"
                            >({{ resultOldChangeForm.tradeUnit_dictText }})</i
                          >：</span
                        >
                        <nd-input
                          width="100%"
                          :formatter="paymentListBlur02"
                          :parser="paymentListBlur02"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradePrice
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.tradePrice
                        }}{{resultOldChangeForm.tradeUnit_dictText}} -->
                      </div>
                      <div class="itemChild">
                        成交总金额（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradeAmount
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.tradeAmount
                        }} -->
                      </div>
                      <div class="itemChild">
                        溢价总金额（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.overflowAmount
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.overflowAmount
                        }} -->
                      </div>
                      <div class="itemChild">
                        溢价率（%）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.overflowScale
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.overflowScale
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费收取规则：<br />
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            width: 68%;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按{{
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            }}年成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >服务费固定金额</span
                          >

                          <nd-input
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            "
                            width="100%"
                            disabled
                          />

                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px; color: #606266"
                            >元</span
                          >
                          <!-- <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.sourceFieldInfoVo.tradeType ==
                                1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:40%;"
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.sourceFieldInfoVo.tradeType !=
                                1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:40%;"
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >履约金固定金额</span
                          ><br/>
                        <nd-input
                            width="50%"
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule
                            "
                           disabled
                          />
                          
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2
                            "
                            style="display: inline-block; width: 10px"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="display: inline-block; width: 10px"
                            >元</span
                          > -->
                        </div>
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.serviceFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费截止缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.serviceFeeTime
                          "
                          disabled
                        />
                        <!--                         
                        {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeTime
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金收取规则：<br />
                        <div
                          style="
                            display: flex;
                            align-items: left;
                            width: 60%;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2 && resultChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 40%;
                            "
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2 && resultChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 40%;
                            "
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 50%;
                            "
                            >履约金固定金额</span
                          >
                          <br />
                          <nd-input
                            width="50%"
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo.perFeeRule
                            "
                            disabled
                          />
                          <!-- {{
                            resultChangeForm.sourceFieldInfoVo.perFeeRule
                          }} -->
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 2
                            "
                            style="display: inline-block; width: 10px"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 3
                            "
                            style="display: inline-block; width: 10px"
                            >元</span
                          >
                        </div>
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.perFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.perFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金截止缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.perFeeTime
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.perFeeTime
                        }} -->
                      </div>
                      <!-- v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .tenderDepositDestination
                        " -->
                      <div
                        class="itemChild"
                        
                      >
                        投标保证金去向：<br />
                        <nd-radio-group
                          v-model="
                            resultOldChangeForm.sourceFieldInfoVo
                              .tenderDepositDestination
                          "
                          @change="handleDestinationChange"
                          disabled
                        >
                          <nd-radio :label="0">退款</nd-radio>
                          <nd-radio :label="1">转为剩余应缴款</nd-radio>
                        </nd-radio-group>
                        <el-checkbox-group v-if=" resultOldChangeForm.sourceFieldInfoVo.tenderDepositDestination === 1"
                          v-model="destinationTypes"
                          @change="handleDestinationTypeChange"
                          style="
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                          "
                        >
                          <nd-checkbox
                            v-for="item in destinationTypeList"
                            :key="item.type"
                            :label="item.type"
                            disabled
                            style="width: 150px; margin-right: 8px; padding: 0"
                          >
                            {{ item.name }}
                          </nd-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.downFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.downFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款截至缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.downFeeTime
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.downFeeTime
                        }} -->
                      </div>
                    </div>
                    <!-- 交易结果成功显示 -->
                    <div
                      v-if="
                        resultForm.changeItems == 2 &&
                        resultOldChangeForm.sourceFieldInfoVo.tradeFlag == 1
                      "
                      class="row-box row-box2"
                    >
                      <div class="itemChild">交易结果：<br />成功</div>
                      <div class="itemChild">
                        成交方：<br />
                        <nd-select
                          :teleported="true"
                          v-model="
                            resultOldChangeForm.sourceFieldInfoVo.signupId
                          "
                          placeholder="请选择"
                          style="width: 100%"
                          disabled
                        >
                          <el-option
                            v-for="item in resultChangeForm.tradeSignupTradeVoList"
                            :label="item.signupName"
                            :value="item.signupId"
                            :key="item.signupId"
                          />
                        </nd-select>
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.traderPerson
                        }} -->
                      </div>
                      <div class="itemChild">
                        成交日期：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradeTime
                          "
                          disabled
                        />
                      </div>
                      <div class="itemChild">
                        <span
                          >成交价<i
                            v-if="resultOldChangeForm.tradeUnit_dictText"
                            style="font-style: normal"
                            >({{ resultOldChangeForm.tradeUnit_dictText }})</i
                          >：</span
                        >
                        <nd-input
                          width="100%"
                          :formatter="paymentListBlur02"
                          :parser="paymentListBlur02"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradePrice
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.tradePrice
                        }}{{resultOldChangeForm.tradeUnit_dictText}} -->
                      </div>
                      <div class="itemChild">
                        成交总金额（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.tradeAmount
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.tradeAmount
                        }} -->
                      </div>
                      <div class="itemChild">
                        溢价总金额（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.overflowAmount
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.overflowAmount
                        }} -->
                      </div>
                      <div class="itemChild">
                        溢价率（%）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.overflowScale
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.overflowScale
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费收取规则：<br />
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            width: 68%;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按{{
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            }}年成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >服务费固定金额</span
                          >

                          <nd-input
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            "
                            width="100%"
                            disabled
                          />

                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px; color: #606266"
                            >元</span
                          >
                          <!-- <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.sourceFieldInfoVo.tradeType ==
                                1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:40%;"
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.sourceFieldInfoVo.tradeType !=
                                1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:40%;"
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >履约金固定金额</span
                          ><br/>
                        <nd-input
                            width="50%"
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule
                            "
                           disabled
                          />
                          
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2
                            "
                            style="display: inline-block; width: 10px"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="display: inline-block; width: 10px"
                            >元</span
                          > -->
                        </div>
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.serviceFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1 &&
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeType != 1 &&
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        服务费截止缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.serviceFeeTime
                          "
                          disabled
                        />
                        <!--                         
                        {{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeTime
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金收取规则：<br />
                        <div
                          style="
                            display: flex;
                            align-items: left;
                            width: 60%;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2 && resultChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 40%;
                            "
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2 && resultChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 40%;
                            "
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              color: #606266;
                              width: 50%;
                            "
                            >履约金固定金额</span
                          >
                          <br />
                          <nd-input
                            width="50%"
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo.perFeeRule
                            "
                            disabled
                          />
                          <!-- {{
                            resultChangeForm.sourceFieldInfoVo.perFeeRule
                          }} -->
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 2
                            "
                            style="display: inline-block; width: 10px"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo.perFeeType == 3
                            "
                            style="display: inline-block; width: 10px"
                            >元</span
                          >
                        </div>
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.perFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.perFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag ==
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType !=
                            1 &&
                          resultOldChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        履约保证金截止缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.perFeeTime
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.perFeeTime
                        }} -->
                      </div>
                       <div
                        class="itemChild"
                        
                      >
                        投标保证金去向：<br />
                        <nd-radio-group
                          v-model="
                            resultOldChangeForm.sourceFieldInfoVo
                              .tenderDepositDestination
                          "
                          @change="handleDestinationChange"
                          disabled
                        >
                          <nd-radio :label="0">退款</nd-radio>
                          <nd-radio :label="1">转为剩余应缴款</nd-radio>
                        </nd-radio-group>
                        <el-checkbox-group v-if=" resultOldChangeForm.sourceFieldInfoVo.tenderDepositDestination === 1"
                          v-model="destinationTypes"
                          @change="handleDestinationTypeChange"
                          style="
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                          "
                        >
                          <nd-checkbox
                            v-for="item in destinationTypeList"
                            :key="item.type"
                            :label="item.type"
                            disabled
                            style="width: 150px; margin-right: 8px; padding: 0"
                          >
                            {{ item.name }}
                          </nd-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款（元）：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.downFee
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.downFee
                        }} -->
                      </div>
                      <div
                        class="itemChild"
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款截至缴纳时间：<br />
                        <nd-input
                          width="100%"
                          v-model.trim="
                            resultOldChangeForm.sourceFieldInfoVo.downFeeTime
                          "
                          disabled
                        />
                        <!-- {{
                          resultOldChangeForm.sourceFieldInfoVo.downFeeTime
                        }} -->
                      </div>
                      <!-- <div>
                        成交方：{{
                          resultOldChangeForm.sourceFieldInfoVo.traderPerson
                        }}
                      </div>
                      <div>
                        成交日期：{{
                          resultOldChangeForm.sourceFieldInfoVo.tradeTime
                        }}
                      </div>
                      <div>
                        成交价：{{
                          resultOldChangeForm.sourceFieldInfoVo.tradePrice
                        }}{{resultOldChangeForm.tradeUnit_dictText}}
                      </div>
                      <div>
                        成交总金额（元）：{{
                          resultOldChangeForm.sourceFieldInfoVo.tradeAmount
                        }}
                      </div>
                      <div>
                        溢价总金额（元）：{{
                          resultOldChangeForm.sourceFieldInfoVo.overflowAmount
                        }}
                      </div>
                      <div>
                        溢价率（%）：{{
                          resultOldChangeForm.sourceFieldInfoVo.overflowScale
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1
                        "
                      >
                        服务费收取规则：
                        <span v-if="resultOldChangeForm.sourceFieldInfoVo.serviceFeeType == 2 && resultOldChangeForm.sourceFieldInfoVo.tradeType == 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">按{{resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule}}年成交价收取</span>
                          <span v-else-if="resultOldChangeForm.sourceFieldInfoVo.serviceFeeType == 2 && resultOldChangeForm.sourceFieldInfoVo.tradeType != 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">按成交价收取</span>
                          <span v-else-if="resultOldChangeForm.sourceFieldInfoVo.serviceFeeType == 3" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">服务费固定金额</span>

                          <nd-input v-if="resultOldChangeForm.sourceFieldInfoVo.serviceFeeType == 3" v-model.trim="resultOldChangeForm.sourceFieldInfoVo.serviceFeeRule" width="100%" disabled />
                          
                          <span v-if="resultOldChangeForm.sourceFieldInfoVo.serviceFeeType == 3" style="margin: 0 0 0 10px;color: #606266;">元</span>
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1
                        "
                      >
                        服务费（元）：{{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFee
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .serviceFeeFlag == 1
                        "
                      >
                        服务费截止缴纳时间：{{
                          resultOldChangeForm.sourceFieldInfoVo.serviceFeeTime
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag == 1
                        "
                      >
                        履约保证金收取规则：{{
                          resultOldChangeForm.sourceFieldInfoVo.perFeeRule
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag == 1
                        "
                      >
                        履约保证金（元）：{{
                          resultOldChangeForm.sourceFieldInfoVo.perFee
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.perFeeFlag == 1
                        "
                      >
                        履约保证金截止缴纳时间：{{
                          resultOldChangeForm.sourceFieldInfoVo.perFeeTime
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款（元）：{{
                          resultOldChangeForm.sourceFieldInfoVo.downFee
                        }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        首期款截至缴纳时间：{{
                          resultOldChangeForm.sourceFieldInfoVo.downFeeTime
                        }}
                      </div> -->
                    </div>
                    <!-- 交易结果失败显示 -->
                    <div
                      v-if="
                        resultForm.changeItems == 2 &&
                        resultOldChangeForm.sourceFieldInfoVo.tradeFlag == 2
                      "
                      class="row-box"
                    >
                      <div>交易结果：失败</div>
                      <div>
                        未成功原因：
                        <!-- <el-form-item prop="reason">
                          <el-checkbox-group disabled
                            v-model="resultOldChangeForm.sourceFieldInfoVo.reason"
                            @change="getDefaultEnum"
                          >
                            <el-checkbox
                              v-for="item in radioGrounp"
                              :label="item.id"
                              :key="item.id"
                            >
                              {{ item.name }}
                            </el-checkbox>
                          </el-checkbox-group>
                        </el-form-item> -->
                        {{ resultOldChangeForm.sourceFieldInfoVo.reasonVal }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .tradeResultDefaultVosLabel
                        "
                      >
                        违约人：{{
                          resultOldChangeForm.sourceFieldInfoVo
                            .tradeResultDefaultVosLabel
                        }}
                      </div>
                      <div>
                        备注：{{ resultOldChangeForm.sourceFieldInfoVo.remark }}
                      </div>
                    </div>

                    <div
                      v-if="
                        resultForm.changeItems == 3 &&
                        resultOldChangeForm.sourceFieldInfoVo.tradeFlag == 2
                      "
                      class="row-box"
                    >
                      <!-- <div>交易结果：失败</div> -->
                      <div>
                        未成功原因：
                        {{ resultOldChangeForm.sourceFieldInfoVo.reasonVal }}
                      </div>
                      <div
                        v-if="
                          resultOldChangeForm.sourceFieldInfoVo
                            .tradeResultDefaultVosLabel
                        "
                      >
                        违约人：{{
                          resultOldChangeForm.sourceFieldInfoVo
                            .tradeResultDefaultVosLabel
                        }}
                      </div>
                      <div>
                        备注：{{ resultOldChangeForm.sourceFieldInfoVo.remark }}
                      </div>
                    </div>
                    <div v-if="!resultForm.changeItems">
                      选择变更事项后自动填入
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="变更后信息">
                  <template #default="{ row }">
                    <div v-if="resultForm.changeItems == 1" class="row-box">
                      <div>
                        <span class="require"> 成交方：</span>
                        <el-form-item prop="signupId">
                          <nd-select
                            :teleported="true"
                            v-model="resultForm.signupId"
                            placeholder="请选择"
                            style="width: 100%"
                            @change="inflowChange"
                          >
                            <el-option
                              v-for="item in resultChangeForm.tradeSignupTradeVoList"
                              :label="item.signupName"
                              :value="item.signupId"
                              :key="item.signupId"
                            />
                          </nd-select>
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require"> 成交日期：</span>
                        <el-form-item prop="tradeTime">
                          <nd-date-picker
                            style="width: 100%"
                            placeholder="请选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            type="date"
                            v-model="resultForm.tradeTime"
                          ></nd-date-picker>
                        </el-form-item>
                      </div>
                      <div>
                        <!-- ("交易模式 1线上 2线下")tradeMode; -->
                        <span class="require">
                          成交价<i
                            v-if="resultOldChangeForm.tradeUnit_dictText"
                            style="font-style: normal"
                            >({{ resultOldChangeForm.tradeUnit_dictText }})</i
                          >：</span
                        >
                        <el-form-item prop="tradePrice">
                          <nd-input
                            @blur="inflowChange02($event)"
                            style="width: 100%"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                            v-model.trim="resultForm.tradePrice"
                            :disabled="
                              resultChangeForm.tradeMode == 2 ? false : true
                            "
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require">成交总金额（元）：</span>
                        <el-form-item prop="tradeAmount">
                          <nd-input
                            style="width: 100%"
                            v-model.trim="resultForm.tradeAmount"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                            maxlength="50"
                            @blur="getServieFee"
                            @change="changePercent"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span>溢价总金额（元）：</span>
                        <el-form-item prop="overflowAmount">
                          <nd-input
                            style="width: 100%"
                            v-model.trim="resultForm.overflowAmount"
                            @change="changeAmount"
                            :formatter="paymentListBlur03"
                            :parser="paymentListBlur03"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span>溢价率（%）：</span>
                        <el-form-item prop="overflowScale">
                          <nd-input
                            style="width: 100%"
                            v-model.trim="resultForm.overflowScale"
                            disabled
                          />
                        </el-form-item>
                        <div class="list-text" style="color: red">
                          已为您自动计算当前项目溢价率，溢价率=溢价总金额/挂牌价总金额*100%
                        </div>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span>服务费收取规则：</span>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按{{
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            }}年成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >服务费固定金额</span
                          >

                          <nd-input
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            "
                            width="100%"
                            disabled
                          />

                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px; color: #606266"
                            >元</span
                          >
                          <!-- <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultChangeForm.sourceFieldInfoVo.tradeType == 1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultChangeForm.sourceFieldInfoVo.tradeType != 1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >履约金固定金额</span
                          >
                          <nd-input
                            v-model.trim="resultForm.serviceFeeRule"
                            width="50%"
                            disabled
                          />
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2
                            "
                            style="margin: 0 0 0 10px"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px;width:10px;"
                            >元</span
                          > -->
                        </div>
                        <!-- <el-form-item prop="name">
                          <nd-input width="100%" maxlength="50" />
                        </el-form-item> -->
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span class="require"> 服务费（元）：</span>
                        <el-form-item prop="serviceFee">
                          <nd-input
                            v-model.trim="resultForm.serviceFee"
                            style="width: 100%"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02" @change="checkDestinationOptions"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span> 服务费截止缴纳时间：</span>
                        <el-form-item prop="serviceFeeTime">
                          <nd-date-picker
                            style="width: 100%"
                            type="datetime"
                            :teleported="true"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.serviceFeeTime"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span> 履约保证金收取规则：</span>
                        <el-form-item prop="name">
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              flex-direction: row;
                            "
                          >
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                  2 && resultChangeForm.tradeType == 1
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >收取首年租金的</span
                            >
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                  2 && resultChangeForm.tradeType != 1
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >收取成交价的</span
                            >
                            <span
                              v-else-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                3
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >履约金固定金额</span
                            >
                            <nd-input
                              v-model.trim="
                                resultChangeForm.sourceFieldInfoVo.perFeeRule
                              "
                              width="50%"
                              disabled
                            />
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2
                              "
                              style="margin: 0 0 0 10px"
                              >%</span
                            >
                            <span
                              v-else-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                3
                              "
                              style="margin: 0 0 0 10px; width: 10px"
                              >元</span
                            >
                          </div>
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span class="require"> 履约保证金（元）：</span>
                        <el-form-item prop="perFee">
                          <nd-input @change="checkDestinationOptions"
                            v-model.trim="resultForm.perFee"
                            style="width: 100%"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span>履约保证金截止缴纳时间：</span>
                        <el-form-item prop="perFeeTime">
                          <nd-date-picker
                            :teleported="true"
                            style="width: 100%"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.perFeeTime"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span
                          :class="{
                            require:
                              resultOldChangeForm.sourceFieldInfoVo.tradeFlag ==
                                1 &&
                              marginFee &&
                              marginFee > 0,
                          }"
                        >
                          投标保证金去向：</span
                        >
                        <el-form-item
                          prop="tenderDepositDestination"
                        >
                          <nd-radio-group
                            v-model="resultForm.tenderDepositDestination"
                            @change="handleDestinationChange"
                            :disabled="
                              Number(marginFee ?? 0) <= 0
                            "
                          >
                            <nd-radio :label="0">退款</nd-radio>
                            <nd-radio :label="1">转为剩余应缴款</nd-radio>
                          </nd-radio-group>
                        </el-form-item>
                        <div
                          v-if="Number(marginFee ?? 0) <= 0"
                          style="color: red"
                          class="list-text"
                        >
                          投标保证金为0元，不可设置去向！
                        </div>
                        <el-checkbox-group
                          v-if="resultForm.tenderDepositDestination === 1"
                          v-model="resultForm.destinationTypes"
                          @change="handleDestinationTypeChange"
                          style="
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                          "
                        >
                          <nd-checkbox
                            v-for="item in destinationTypeList"
                            :key="item.type"
                            :label="item.type"
                            :disabled="item.type !== '4' ? isDestinationDisabled(item.type) : false"
                            style="width: 150px; margin-right: 8px; padding: 0"
                          >
                            {{ item.name }}
                            <!-- <span v-if="item.type === '4' && isContractTailDisabled" class="list-text" style="margin-left: 5px;color:red">没有多余的钱转合同尾款了!</span> -->
                          </nd-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        <span> 首期款（元）：</span>
                        <el-form-item prop="downFee">
                          <nd-input @change="checkDestinationOptions"
                            v-model.trim="resultForm.downFee"
                            style="width: 100%"
                            :formatter="paymentListBlur022"
                            :parser="paymentListBlur022"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        <span>首期款截至缴纳时间：</span>
                        <el-form-item prop="downFeeTime">
                          <nd-date-picker
                            :teleported="true"
                            style="width: 100%"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.downFeeTime"
                          />
                        </el-form-item>
                      </div>
                    </div>
                    <div
                      v-if="
                        resultForm.changeItems == 2 &&
                        resultChangeForm.sourceFieldInfoVo.tradeFlag == 1
                      "
                      class="row-box"
                    >
                      <div>
                        <span class="require">交易结果：</span>
                        <el-radio-group
                          style="width: 10%"
                          label="2"
                          v-model="radioFlag"
                          disabled
                        >
                          <el-radio label="2">失败</el-radio>
                        </el-radio-group>
                      </div>
                      <div>
                        <span class="require">未成功原因:</span>
                        <el-form-item prop="reason">
                         <el-radio-group  style="align-items: flex-start;" v-model="resultForm.reason" @change="getDefaultEnum">
                                <el-radio v-for="item in radioGrounp" :label="item.id" :key="item.id">
                                    {{ item.name }}
                                </el-radio>
                            </el-radio-group>
                          <!-- <el-checkbox-group
                            v-model="resultForm.reason"
                            @change="getDefaultEnum"
                          >
                            <el-checkbox
                              v-for="item in radioGrounp"
                              :label="item.id"
                              :key="item.id"
                            >
                              {{ item.name }}
                            </el-checkbox>
                          </el-checkbox-group> -->
                        </el-form-item>
                      </div>
                      <div v-if="emumFlag">
                        <span class="require">违约人:</span>
                        <el-form-item prop="emumkey">
                          <nd-select
                            v-model="resultForm.emumkey"
                            width="100%"
                            @change="changeEnum"
                            :teleported="true"
                            collapse-tags
                           :multiple="isMultipleSelect"
                            :max-collapse-tags="1"
                          >
                            <el-option
                              v-for="item in enumList"
                              :label="item.label"
                              :value="item.defaultId"
                              :key="item.defaultId"
                            />
                          </nd-select>
                        </el-form-item>
                      </div>
                      <div>
                        <span>备注:</span>
                        <el-form-item prop="priorityFlag">
                          <nd-input
                            type="textarea"
                            width="100%"
                            v-model="resultForm.remark"
                            maxlength="500"
                            :autosize="{ minRows: 2, maxRows: 4 }"
                          />
                        </el-form-item>
                      </div>
                    </div>
                    <div
                      v-if="
                        resultForm.changeItems == 2 &&
                        resultChangeForm.sourceFieldInfoVo.tradeFlag == 2
                      "
                      class="row-box"
                    >
                      <div>
                        <span>交易结果：</span>
                        <el-form-item prop="priorityFlag">
                          <el-radio-group
                            style="width: 10%"
                            label="1"
                            v-model="radioFlag2"
                            disabled
                          >
                            <el-radio label="1">成功</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require"> 成交方：</span>
                        <el-form-item prop="signupId">
                          <nd-select
                            :teleported="true"
                            v-model="resultForm.signupId"
                            placeholder="请选择"
                            style="width: 100%"
                            @change="inflowChange"
                          >
                            <el-option
                              v-for="item in resultChangeForm.tradeSignupTradeVoList"
                              :label="item.signupName"
                              :value="item.signupId"
                              :key="item.signupId"
                            />
                          </nd-select>
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require"> 成交日期：</span>
                        <el-form-item prop="tradeTime">
                          <nd-date-picker
                            style="width: 100%"
                            width="100%"
                            placeholder="请选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            type="date"
                            v-model="resultForm.tradeTime"
                          ></nd-date-picker>
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require">
                          成交价<i
                            v-if="resultOldChangeForm.tradeUnit_dictText"
                            style="font-style: normal"
                            >({{ resultOldChangeForm.tradeUnit_dictText }})</i
                          >：</span
                        >
                        <el-form-item prop="tradePrice">
                          <nd-input
                            width="100%"
                            @blur="inflowChange02($event)"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                            v-model.trim="resultForm.tradePrice"
                            :disabled="
                              resultChangeForm.tradeMode == 2 ? false : true
                            "
                          />
                          <!-- {{resultOldChangeForm.tradeUnit_dictText}} -->
                        </el-form-item>
                      </div>
                      <div>
                        <span class="require">成交总金额（元）：</span>
                        <el-form-item prop="tradeAmount">
                          <nd-input
                            width="100%"
                            v-model.trim="resultForm.tradeAmount"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                            maxlength="50"
                            @blur="getServieFee"
                            @change="changePercent"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span>溢价总金额（元）：</span>
                        <el-form-item prop="overflowAmount">
                          <nd-input
                            width="100%"
                            v-model.trim="resultForm.overflowAmount"
                            :formatter="paymentListBlur03"
                            @change="changeAmount"
                            :parser="paymentListBlur03"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span>溢价率（%）：</span>
                        <el-form-item prop="overflowScale">
                          <nd-input
                            width="100%"
                            v-model.trim="resultForm.overflowScale"
                            disabled
                          />
                        </el-form-item>
                        <div class="list-text" style="color: red">
                          已为您自动计算当前项目溢价率，溢价率=溢价总金额/挂牌价总金额*100%
                        </div>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span>服务费收取规则：</span>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            flex-direction: row;
                          "
                        >
                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType == 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按{{
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            }}年成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultOldChangeForm.tradeType != 1
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >按成交价收取</span
                          >
                          <span
                            v-else-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="
                              margin: 0 10px 0 0;
                              white-space: nowrap;
                              color: #606266;
                            "
                            >服务费固定金额</span
                          >

                          <nd-input
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            v-model.trim="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeRule
                            "
                            width="100%"
                            disabled
                          />

                          <span
                            v-if="
                              resultOldChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px; color: #606266"
                            >元</span
                          >
                          <!-- <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultChangeForm.sourceFieldInfoVo.tradeType == 1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >收取首年租金的</span
                          >
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2 &&
                              resultChangeForm.sourceFieldInfoVo.tradeType != 1
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >收取成交价的</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 10px 0 0; color: #606266;width:50%;"
                            >履约金固定金额</span
                          >
                          <nd-input
                            v-model.trim="resultForm.serviceFeeRule"
                            width="50%"
                            disabled
                          />
                          <span
                            v-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 2
                            "
                            style="margin: 0 0 0 10px;width:10px;"
                            >%</span
                          >
                          <span
                            v-else-if="
                              resultChangeForm.sourceFieldInfoVo
                                .serviceFeeType == 3
                            "
                            style="margin: 0 0 0 10px;width:10px;"
                            >元</span
                          > -->
                        </div>
                        <!-- <el-form-item prop="name">
                          <nd-input width="100%" maxlength="50" />
                        </el-form-item> -->
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span class="require"> 服务费（元）：</span>
                        <el-form-item prop="serviceFee">
                          <nd-input
                            v-model.trim="resultForm.serviceFee"
                            width="100%"
                            :formatter="paymentListBlur02" @change="checkDestinationOptions"
                            :parser="paymentListBlur02"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.serviceFeeFlag ==
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType !=
                            1 &&
                          resultChangeForm.sourceFieldInfoVo.serviceFeeType
                        "
                      >
                        <span> 服务费截止缴纳时间：</span>
                        <el-form-item prop="serviceFeeTime">
                          <nd-date-picker
                            style="width: 100%"
                            type="datetime"
                            :teleported="true"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.serviceFeeTime"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span> 履约保证金收取规则：</span>
                        <el-form-item prop="name">
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              flex-direction: row;
                            "
                          >
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                  2 && resultChangeForm.tradeType == 1
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >收取首年租金的</span
                            >
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                  2 && resultChangeForm.tradeType != 1
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >收取成交价的</span
                            >
                            <span
                              v-else-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                3
                              "
                              style="
                                margin: 0 10px 0 0;
                                color: #606266;
                                width: 50%;
                              "
                              >履约金固定金额</span
                            >
                            <nd-input
                              v-model.trim="
                                resultChangeForm.sourceFieldInfoVo.perFeeRule
                              "
                              width="50%"
                              disabled
                            />
                            <span
                              v-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                2
                              "
                              style="margin: 0 0 0 10px; width: 10px"
                              >%</span
                            >
                            <span
                              v-else-if="
                                resultChangeForm.sourceFieldInfoVo.perFeeType ==
                                3
                              "
                              style="margin: 0 0 0 10px; width: 10px"
                              >元</span
                            >
                          </div>
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span class="require"> 履约保证金（元）：</span>
                        <el-form-item prop="perFee">
                          <nd-input @change="checkDestinationOptions"
                            v-model.trim="resultForm.perFee"
                            width="100%"
                            :formatter="paymentListBlur02"
                            :parser="paymentListBlur02"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.perFeeFlag == 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType != 1 &&
                          resultChangeForm.sourceFieldInfoVo.perFeeType
                        "
                      >
                        <span>履约保证金截止缴纳时间：</span>
                        <el-form-item prop="perFeeTime">
                          <nd-date-picker
                            :teleported="true"
                            style="width: 100%"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.perFeeTime"
                          />
                        </el-form-item>
                      </div>
                      <div>
                        <span
                          :class="{
                            require:
                              resultOldChangeForm.sourceFieldInfoVo.tradeFlag ==
                                1 &&
                              marginFee &&
                              marginFee > 0,
                          }"
                        >
                          投标保证金去向：</span
                        >
                        <el-form-item
                          prop="tenderDepositDestination"
                        >
                          <nd-radio-group
                            v-model="resultForm.tenderDepositDestination"
                            @change="handleDestinationChange"
                            :disabled="
                              Number(marginFee ?? 0) <= 0
                            "
                          >
                            <nd-radio :label="0">退款</nd-radio>
                            <nd-radio :label="1">转为剩余应缴款</nd-radio>
                          </nd-radio-group>
                        </el-form-item>
                        <div
                          v-if="Number(marginFee ?? 0) <= 0"
                          style="color: red"
                          class="list-text"
                        >
                          投标保证金为0元，不可设置去向！
                        </div>
                        <el-checkbox-group
                          v-if="resultForm.tenderDepositDestination === 1"
                          v-model="resultForm.destinationTypes"
                          @change="handleDestinationTypeChange"
                          style="
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                          "
                        >
                          <nd-checkbox
                            v-for="item in destinationTypeList"
                            :key="item.type"
                            :label="item.type"
                            :disabled="item.type !== '4' ? isDestinationDisabled(item.type) : false"
                            style="width: 150px; margin-right: 8px; padding: 0"
                          >
                            {{ item.name }}
                            <!-- <span v-if="item.type === '4' && isContractTailDisabled" class="list-text" style="margin-left: 5px;color:red">没有多余的钱转合同尾款了!</span> -->
                          </nd-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        <span> 首期款（元）：</span>
                        <el-form-item prop="downFee">
                          <nd-input @change="checkDestinationOptions"
                            v-model.trim="resultForm.downFee"
                            width="100%"
                            :formatter="paymentListBlur022"
                            :parser="paymentListBlur022"
                          />
                        </el-form-item>
                      </div>
                      <div
                        v-if="
                          resultChangeForm.sourceFieldInfoVo.downFeeFlag == 1
                        "
                      >
                        <span>首期款截至缴纳时间：</span>
                        <el-form-item prop="downFeeTime">
                          <nd-date-picker
                            :teleported="true"
                            style="width: 100%"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="resultForm.downFeeTime"
                          />
                        </el-form-item>
                      </div>
                    </div>

                    <div
                      v-if="
                        resultForm.changeItems == 3 &&
                        resultChangeForm.sourceFieldInfoVo.tradeFlag == 2
                      "
                      class="row-box"
                    >
                      <!-- <div>
                        <span class="require">交易结果：</span>
                        <el-radio-group
                          style="width: 10%"
                          label="2"
                          v-model="radioFlag"
                          disabled
                        >
                          <el-radio label="2">失败</el-radio>
                        </el-radio-group>
                      </div> -->
                      <div>
                        <span class="require">未成功原因:</span>
                        <el-form-item prop="reason">
                           <el-radio-group style="align-items: flex-start;" v-model="resultForm.reason" @change="getDefaultEnum">
                                <el-radio v-for="item in radioGrounp" :label="item.id" :key="item.id">
                                    {{ item.name }}
                                </el-radio>
                            </el-radio-group>
                          <!-- <el-checkbox-group
                            v-model="resultForm.reason"
                            @change="getDefaultEnum"
                          >
                            <el-checkbox
                              v-for="item in radioGrounp"
                              :label="item.id"
                              :key="item.id"
                            >
                              {{ item.name }}
                            </el-checkbox>
                          </el-checkbox-group> -->
                        </el-form-item>
                      </div>
                      <div v-if="emumFlag">
                        <span class="require">违约人:</span>
                        <el-form-item prop="emumkey">
                          <nd-select
                            v-model="resultForm.emumkey"
                            width="100%"
                            @change="changeEnum"
                            :teleported="true"
                            collapse-tags
                            :multiple="isMultipleSelect"
                            :max-collapse-tags="1"
                          >
                            <el-option
                              v-for="item in enumList"
                              :label="item.label"
                              :value="item.defaultId"
                              :key="item.defaultId"
                            />
                          </nd-select>
                        </el-form-item>
                      </div>
                      <div>
                        <span>备注:</span>
                        <el-form-item prop="priorityFlag">
                          <nd-input
                            type="textarea"
                            width="100%"
                            v-model="resultForm.remark"
                            maxlength="500"
                            :autosize="{ minRows: 2, maxRows: 4 }"
                          />
                        </el-form-item>
                      </div>
                    </div>
                    <div v-if="!resultForm.changeItems">
                      选择变更事项后可编辑
                    </div>
                  </template>
                </el-table-column>
              </nd-table>
            </el-form>
          </div>
        </div>
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
            >文书信息</span
          >
          <div style="margin-top: 15px">
            <nd-table style="height: 100%" :data="getDocList.arry">
              <el-table-column
                align="center"
                type="index"
                min-width="40"
                label="序号"
              />
              <el-table-column align="center" prop="modelName" label="文书名称">
                <template #default="{ row }">
                  <span
                    style="
                      cursor: pointer;
                      color: #0098ff;
                      font-size: 14px;
                      margin-right: 15px;
                    "
                    class="operateBtn"
                    @click.stop="pushFlag(2, row, '预览文书')"
                    >{{ row.modelName }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                align="center"
                prop="genFlag"
                label="文书状态"
              >
                <template #default="{ row }">
                  <span class="operateBtn">{{
                    row.dtbId == null || !row.dtbId
                      ? "未生成"
                      : row.dtbId
                      ? "已生成"
                      : "-"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                align="center"
                prop="genFlag"
                label="是否必须生成文书"
              >
                <template #default="{ row }">
                  <span class="operateBtn">{{
                    row.genFlag == 0 ? "否" : row.genFlag == 1 ? "是" : "-"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                align="center"
                prop="pushStatus"
                label="推送状态"
              >
                <template #default="{ row }">
                  <span v-if="row.pushFlag == 1 && row.pushStatus == 1"
                    >已推送</span
                  >
                  <span v-else-if="row.pushFlag == 1">未推送</span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                align="center"
                prop="status"
                label="操作"
              >
                <template #default="{ row }">
                  <span
                    style="
                      cursor: pointer;
                      color: #0098ff;
                      font-size: 14px;
                      margin-right: 15px;
                    "
                    class="operateBtn"
                    v-if="row.dtbId == null || row.dtbId == ''"
                    @click.stop="pushFlag(1, row, '生成文书')"
                    >生成文书</span
                  >
                  <span
                    style="
                      cursor: pointer;
                      color: #0098ff;
                      font-size: 14px;
                      margin-right: 15px;
                    "
                    class="operateBtn"
                    v-else
                    @click.stop="pushFlag(1, row, '重新生成')"
                    >重新生成</span
                  >
                  <span
                    style="cursor: pointer; color: #0098ff; font-size: 14px"
                    class="operateBtn"
                    v-if="row.pushFlag == 1"
                    @click.stop="pushFlag(2, row)"
                    >推送文书</span
                  >
                </template>
              </el-table-column>
            </nd-table>
          </div>
        </div>
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
            >附件信息</span
          >
          <div style="margin-top: 15px">
            <div
              class="upload-box"
              v-for="(item, index) in formRespons.uploadList2"
              :key="index"
            >
              <div
                class="upload-box-title"
                :class="item.needChoose == 1 ? 'set-class' : ''"
              >
                {{ item.fileName }}
              </div>
              <div class="upload-box-img">
                <div class="upload-box-img-box">
                  <ndb-upload
                    v-model="item.fileList"
                    :uploadParams="{
                      busId: resultChangeForm.id,
                      configFileId: item.id,
                    }"
                  ></ndb-upload>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="btn-box"
        style="display: flex; justify-content: center; padding: 10px 0px"
      >
        <nd-button
          :disabled="btnDisabled"
          type="primary"
          color="#0b8df1"
          icon="Finished"
          @click="saveFn()"
          >提&nbsp;交</nd-button
        >
        <nd-button icon="Back" @click="close">取&nbsp;消</nd-button>
      </div>
    </div>

    <div class="right-box">
      <div class="title">
        <el-icon><DocumentCopy /></el-icon>
        <span>审核流程</span>
      </div>
      <div style="width: 100%; height: 15px"></div>
      <div class="el-steps-div">
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :icon="activity.icon"
            :color="activity.color"
            :timestamp="activity.nodeName"
            placement="top"
          >
            <p>
              {{ activity.operator }}
            </p>
            <template v-if="activity.type === 1">
              <p>
                {{
                  activity.flowTime
                    ? "提交时间：" + activity.flowTime
                    : "提交时间："
                }}
              </p>
            </template>
            <template v-else>
              <p>
                {{
                  activity.flowTime
                    ? "审核时间：" + activity.flowTime
                    : "审核时间："
                }}
              </p>
              <p>
                {{
                  activity.status == 1 || activity.status == "通过"
                    ? "审核结果：通过"
                    : "审核结果：不通过"
                }}
              </p>
              <p>
                {{
                  activity.opinion
                    ? "审核意见：" + activity.opinion
                    : "审核意见："
                }}
              </p>
            </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <!-- 文书弹框 -->
    <documentDialog ref="documentModifyView" @refresh="getDocListFun" />
  </div>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndTag from "@/components/ndTag.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbUpload from "@/components/business/ndbUpload2/index.vue";
import documentDialog from "@/components/business/ndbWordDocument/index.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndCheckbox from "@/components/ndCheckbox.vue";
// el
import { ElMessage, ElMessageBox } from "element-plus";
// 子组件
import underApplyPeople from "./underApplyPeople.vue";
// vue
import {
  inject,
  onMounted,
  ref,
  nextTick,
  reactive,
  shallowRef,
  watch,computed
} from "vue";
import { Check, Back } from "@element-plus/icons-vue";

//mock
import { useMock } from "./mockData.js";
const { mock, page, tagsData } = useMock();
// axios
const $axios = inject("$axios");
// emit
let emit = defineEmits(["closeRefresh", "close"]);
// ref
const underApplyPeopleRef = ref(null);
// inject
const $detailPageRef = inject("$detailPageRef");

const form = inject("form");
const activities = ref([]); //审核记录
let resultChangeForm = ref({});
const resultOldChangeForm = ref({}); //原始值
const formRef = ref(null);
const radioGrounp = [
  { name: "转出方违约", id: "1" },
  { name: "转入方违约", id: "2" },
  { name: "其他", id: "3" },
];
// let reason = ref([]); //未成功原因 中间量
let enumList = ref([]);
let tenderId = ref("");
let resultForm = reactive({
  signupId: "",
  changeReason: "",
  changeItems: "",
  tradeTime: "",
  tradeAmount: "",
  serviceFee: "",
  serviceFeeTime: "",
  perFee: "",
  perFeeTime: "",
  reason: "",
  emumkey: [],
});
function validateCHangeReason(rule, value, callback) {
  if (value === "") {
    callback(new Error("请输入变更原因"));
  }
  if (value === "") {
    callback(new Error("请输入变更原因"));
  } else if (value.length > 500) {
    callback(new Error("输入不得超过500个字"));
  } else {
    callback();
  }
}
function validateDownFee(rule, value, callback) {
  if (!resultForm.downFee && resultForm.downFeeTime) {
    callback(new Error("填写了首期款截止时间必须填写首期款"));
  } else {
    callback();
  }
}
watch(
  () => resultForm.tradeAmount,
  () => {
    computedFn();
  }
);
// 处理费用类型选择变更
const handleDestinationTypeChange = (val) => {

    // 防御性检查
    if (!val) return
    if (marginFee.value == null) return

    // 至少选择一个
    if (val.length === 0 && resultForm.tenderDepositDestination === 1) {
        msgWarning("请至少选择一个费用类型")
        return
    }

    try {

       const totalSelectedFee = calculateSelectedFeeTotal(val)
        const marginFeeNumber = Number(marginFee.value ?? 0)

        // 检查总金额是否超过投标保证金
        if (totalSelectedFee > marginFeeNumber) {
            ElMessage.warning("投标保证金小于剩余应缴款，不可选中，请重新选择！")
            // resultForm.destinationTypes = []
            resultForm.destinationTypes.pop()
            return
        }

        // 检查是否选中了合同尾款
        if (val.includes("4") && totalSelectedFee >= marginFeeNumber) {
            ElMessage.warning("投标保证金已经用完了，不可以再转合同尾款了！")
            resultForm.destinationTypes =resultForm.destinationTypes.filter(type => type !== "4")
            return
        }
    } catch (error) {
        // 错误处理
    }
}
function computedFn() {
  // 服务费（元
  if (resultOldChangeForm.value.sourceFieldInfoVo.serviceFeeFlag == 1) {
    //serviceFeeFlag 是否显示
    if (resultOldChangeForm.value.sourceFieldInfoVo.serviceFeeType == 3) {
      if (!resultForm.serviceFee) {
        resultForm.serviceFee = resultForm.serviceFeeRule;
      }
    }
  }
  // 履约保证金（元）
  if (resultOldChangeForm.value.sourceFieldInfoVo.perFeeFlag == 1) {
    //perFeeFlag 是否显示
    if (resultOldChangeForm.value.sourceFieldInfoVo.perFeeType == 3) {
      if (!resultForm.perFee) {
        resultForm.perFee = resultForm.perFeeRule;
      }
    }
  }
}
const rules = {
  signupId: [{ required: true, message: "请选择成交方", trigger: "change" }],
  changeReason: [
    { validator: validateCHangeReason, trigger: ["blur", "change"] },
  ],
  tradePrice: [{ required: true, message: "请输入成交价", trigger: "blur" }],
  changeItems: [
    { required: true, message: "请选择变更事项", trigger: ["blur", "change"] },
  ],
  tradeTime: [{ required: true, message: "请选择成交日期", trigger: "blur" }],
  tradeAmount: [
    { required: true, message: "请输入成交总金额", trigger: "blur" },
  ],
  serviceFee: [{ required: true, message: "请输入服务费", trigger: "blur" }],
  perFee: [{ required: true, message: "请输入履约保证金", trigger: "blur" }],
  reason: [
    {
      required: true,
      message: "请选择未成功原因",
      trigger: ["blur", "change"],
    },
  ],
  emumkey: [
    { required: true, message: "请选择违约人", trigger: ["blur", "change"] },
  ],
  downFee: [{ validator: validateDownFee, trigger: ["blur", "change"] }],
  downFeeTime: [{ validator: validateDownFee, trigger: ["blur", "change"] }],
  tenderDepositDestination: [
        { validator: validateTenderDepositDestination, trigger: ['change'] }
    ],
};
// 验证投标保证金去向
function validateTenderDepositDestination(rule, value, callback) {
    if (Number(marginFee.value ?? 0) > 0 && (value === ''||value ==null)) {
        callback(new Error('请选择投标保证金去向'))
    } else {
        callback()
    }
}
let changeId = ref(null);
let radioFlag = ref("2");
let radioFlag2 = ref("1");
const listForm = ref({
  pageNo: 1,
  pageSize: 10,
});
const resData = reactive({
  list: [
    {
      changeItems: "",
    },
  ],
  total: 0,
  optionList: [
    {
      label: "成交方信息",
      value: "1",
    },
    {
      label: "交易失败信息", //tradeFlag为2失败才能变更
      value: "3",
    },
    {
      label: "交易结果",
      value: "2",
    },
  ],
});
let btnDisabled = ref(false);
onMounted(() => {
  if (!$detailPageRef.value.tenderId) {
    tenderId.value = $detailPageRef.value.tradeTendersList[0].tenderId;
  } else {
    tenderId.value = $detailPageRef.value.tenderId;
  }
  initData();
  getTenderDepositDestinationEnum();
});

function getData(id) {
  activities.value = [];
  $axios({
    method: "get",
    url: `/tradeResultChange/getAllFlowNode/` + id,
  }).then((res) => {
    let obj = res.data.data;
    let arr = [];
    for (let i = 0; i < obj.length; i++) {
      if (obj[i].type === 2) {
        arr.push(obj[i]);
      }
    }
    activities.value = obj;
    // 审核结果
    // auditResut.value = arr.slice(-1)[0];
    // 审核记录
    activities.value.forEach((item, index) => {
      item.type === 1
        ? (item.operator = "提交人：" + item.operator)
        : (item.operator = "审核人：" + item.operator);
      item.icon = shallowRef(Check);
      item.color = "#0B8DF1";
    });
  });
}
function paymentListBlur02(value, int = 13) {
  value = value.toString();
  // 先把非数字的都替换掉，除了数字和小数点
  value = value.replace(/[^\d.-]/g, "");
  value = value.replace(!/^-?\d+(\.\d+)?$/, "");
  // 必须保证第一个为数字而不是小数点
  value = value.replace(/^\./g, "");
  // 负号只能出现在首位
  value = value.replace(/(\d+|\.)-/g, "$1");
  // 保证只有出现一个小数点而没有多个小数点
  value = value.replace(/\.{2,}/g, ".");
  // 保证小数点只出现一次，而不能出现两次以上
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  value = value.replace("-", "$#$").replace(/\-/g, "").replace("$#$", "-");
  // 保证只能输入2个小数
  value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
  // 只能（int）位整数，自定义
  let index = value.indexOf(".");
  if (index > -1) {
    value = value.slice(0, index < int ? index : int) + value.slice(index);
  } else {
    value = value.slice(0, int);
  }
  return value;
}
function paymentListBlur022(value, int = 12) {
  value = value.toString();
  value = value.replace(/-/g, "");
  // 先把非数字的都替换掉，除了数字和小数点
  value = value.replace(/[^\d.-]/g, "");
  value = value.replace(!/^-?\d+(\.\d+)?$/, "");
  // 必须保证第一个为数字而不是小数点
  value = value.replace(/^\./g, "");
  // 负号只能出现在首位
  value = value.replace(/(\d+|\.)-/g, "$1");
  // 保证只有出现一个小数点而没有多个小数点
  value = value.replace(/\.{2,}/g, ".");
  // 保证小数点只出现一次，而不能出现两次以上
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  value = value.replace("-", "$#$").replace(/\-/g, "").replace("$#$", "-");
  // 保证只能输入2个小数
  value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
  // 只能（int）位整数，自定义
  let index = value.indexOf(".");
  if (index > -1) {
    value = value.slice(0, index < int ? index : int) + value.slice(index);
  } else {
    value = value.slice(0, int);
  }
  return value;
}
function paymentListBlur03(value, int = 12) {
  let val = value
    .replace(/^0[0-9]+/, (val) => val[1])
    .replace(/^(\.)+/, "")
    .replace(/[^\d.]/g, "")
    .replace(/^(\d{12})\d*(\.\d{0,2})?$/, "$1$2")
    .replace(/\.+/, ".")
    .replace(".", "$#$")
    .replace(/\./g, "")
    .replace("$#$", ".")
    .replace(/^(\d+)\.(\d\d).*$/, "$1.$2");
  // 保证只能输入2个小数
  val = val.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
  // 只能（int）位整数，自定义
  let index = val.indexOf(".");
  if (index > -1) {
    val = val.slice(0, index < int ? index : int) + val.slice(index);
  } else {
    val = val.slice(0, int);
  }
  return val;
}
// table-API
function indexMethod(index) {
  let current = 1;
  let size = 10;
  return (current - 1) * size + index + 1;
}

const selectChange = (e, i, row) => {
  console.log(e);
  if (resultOldChangeForm.value.sourceFieldInfoVo.tradeFlag == 2 && e == 1) {
    resultForm.changeItems = null;
    return msgWarning("原交易结果为失败，无可变更成交方信息！");
  }
  if (resultOldChangeForm.value.sourceFieldInfoVo.tradeFlag == 1 && e == 3) {
    resultForm.changeItems = null;
    return msgWarning("原交易结果为成功，无可变更成交方信息！");
  }
  resultForm.changeItems = e;
};
let inflowRes = ref({});
let marginFee = ref("");

function getSuccessBidderBzj() {
  if (!resultForm.signupId) return;
  let arr = resultChangeForm.value.tradeSignupTradeVoList.filter((item) => {
    return item.signupId === resultForm.signupId;
  });
  $axios({
    method: "get",
    url: `/tradeResultChange/getSuccessBidderBzj`,
    data: {
      signId: arr[0].signupId,
      tenderId: tenderId.value,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      marginFee.value = res.data.data.marginFee;
      if(Number(marginFee.value ?? 0) <= 0){
        resultForm.tenderDepositDestination=null
      }
    } else {
      msgWarning(res.data.msg);
    }
  });
}
function inflowChange() {
  resultForm.destinationTypes = []
  if (!resultForm.signupId) return;
  let arr = resultChangeForm.value.tradeSignupTradeVoList.filter((item) => {
    return item.signupId === resultForm.signupId;
  });
  resultForm.tradePrice = arr[0].bidPrice; // tradePrice 赋值
  resultForm.traderPerson = arr[0].signupName;
  $axios({
    method: "get",
    url: `/result/chooseTrade?signupId=${arr[0].signupId}&bidPrice=${arr[0].bidPrice}&tenderId=${tenderId.value}`,
  }).then((res) => {
    if (res.data.code === 200) {
      inflowRes.value = res.data.data;
      resultForm.serviceFee = inflowRes.value.serviceFee;
      resultForm.perFee = inflowRes.value.perFee;
      resultForm.floorTradeAmount = inflowRes.value.floorTradeAmount;
      resultForm.overflowAmount = inflowRes.value.overflowAmount;
      resultForm.tradeAmount = inflowRes.value.tradeAmount;
      changeAmount();
    } else {
      msgWarning(res.data.msg);
    }
  });
  getSuccessBidderBzj()
}
function inflowChange02(e) {
  console.log(e.target.value);
  if (!resultForm.signupId) return;
  console.log(resultForm.tradePrice);
  resultForm.tradePrice = e.target.value;
  // 与成交方相关联
  // if(val != signupId) return
  $axios({
    method: "get",
    url: `/result/chooseTrade?signupId=${resultForm.signupId}&bidPrice=${e.target.value}&tenderId=${tenderId.value}`,
  }).then((res) => {
    inflowRes.value = res.data.data;
    resultForm.serviceFee = inflowRes.value.serviceFee;
    resultForm.perFee = inflowRes.value.perFee;
    resultForm.floorTradeAmount = inflowRes.value.floorTradeAmount;
    resultForm.tradeAmount = inflowRes.value.tradeAmount;
    resultForm.overflowAmount = inflowRes.value.overflowAmount;
    resultForm.overflowScale = inflowRes.value.overflowScale;
    changeAmount();
  });
}
const changePercent = () => {
  if (resultForm.tradeAmount !== null && resultForm.tradeAmount !== "") {
    // 自动计算溢价总金额
    console.log(resultChangeForm.value.floorTradeAmount);
    console.log(resultForm.tradeAmount);
    if (resultChangeForm.value.tradeDirection === 0) {
      // 正向
      resultForm.overflowAmount =
        (Number(resultForm.tradeAmount) * 100 -
          Number(resultChangeForm.value.floorTradeAmount) * 100) /
        100;
    } else {
      // 反向
      resultForm.overflowAmount =
        (Number(resultChangeForm.value.floorTradeAmount) * 100 -
          Number(resultForm.tradeAmount) * 100) /
        100;
    }
  } else {
    resultForm.overflowAmount = "0";
  }
  changeAmount();
};
const changeAmount = () => {
  if (resultForm.tradeAmount !== null && resultForm.tradeAmount !== "") {
    // 自动计算溢价率
    if (resultChangeForm.value.floorTradeAmount) {
      console.log(resultForm.overflowAmount);
      console.log(resultChangeForm.value.floorTradeAmount);
      resultForm.overflowScale = (
        ((resultForm.overflowAmount * 100) /
          (resultChangeForm.value.floorTradeAmount * 100)) *
        100
      ).toFixed(2);
    } else {
      resultForm.overflowScale = "0.00";
    }
  } else {
    resultForm.overflowScale = "";
  }
};
// // 计算合同尾款是否应该置灰
// const isContractTailDisabled = computed(() => {
//     // 如果没有选择转为剩余应缴款，不置灰
//     if (resultForm.tenderDepositDestination !== 1) return false

//     // 如果没有投标保证金数据，不置灰
//     if (marginFee.value == null) return false

//     // 计算已选中的剩余缴款金额总和（不包括合同尾款）
//     let totalSelectedFee = 0
//     destinationTypes.value.forEach(type => {
//         if (type === "1" && resultForm.serviceFee) {
//             totalSelectedFee += Number(resultForm.serviceFee) || 0
//         } else if (type === "2" && resultForm.downFee) {
//             totalSelectedFee += Number(resultForm.downFee) || 0
//         } else if (type === "3" && resultForm.perFee) {
//             totalSelectedFee += Number(resultForm.perFee) || 0
//         }
//     })

//     // 如果投标保证金余额等于勾选的剩余缴款金额，说明投标保证金已经用完了
//     return totalSelectedFee >= Number(marginFee.value ?? 0)
// })
// / 成交总金额  获取 服务费
function getServieFee() {
  if (!resultForm.signupId) return;
  let arr = resultChangeForm.value.tradeSignupTradeVoList.filter((item) => {
    return item.signupId === resultForm.signupId;
  });
  $axios({
    method: "get",
    url: `/result/getServiceFee`,
    data: {
      bidPrice: resultForm.tradePrice,
      // bidPrice: Number(arr[0].bidPrice),
      tenderId: tenderId.value,
      tradeAmount: Number(resultForm.tradeAmount),
    },
  }).then((res) => {
    const { data } = res.data;
    resultForm.perFee = data.perFee;
    resultForm.serviceFee = data.serviceFee;
    console.log(resultForm.perFee, resultForm.serviceFee);
  });
}
let emumFlag = ref("");
// 判断违约人下拉框是否为多选：转入方违约(id=2)时为单选，其他为多选
const isMultipleSelect = computed(() => {
    return resultForm.reason !== "2"
})
// 计算选中费用类型的总金额（不包括合同尾款）
const calculateSelectedFeeTotal = (types = resultForm.destinationTypes) => {
    let total = 0
    types.forEach(type => {
        if (type === "1" && resultForm.serviceFee) {
            total += Number(resultForm.serviceFee) || 0
        } else if (type === "2" && resultForm.downFee) {
            total += Number(resultForm.downFee) || 0
        } else if (type === "3" && resultForm.perFee) {
            total += Number(resultForm.perFee) || 0
        }
    })
    return total
}
// 检查投标保证金去向的费用类型可选状态
  const checkDestinationOptions = () => {
    // 防御性检查
    if (!resultForm || resultForm.tenderDepositDestination !== 1) return
    if (!resultForm.destinationTypes) {
        resultForm.destinationTypes = []
        return
    }
    
    try {
        // 重新检查已选中的费用类型是否还符合条件
        const validTypes = resultForm.destinationTypes.filter(type => !isDestinationDisabled(type))
        if (validTypes.length !== resultForm.destinationTypes.length) {
            resultForm.destinationTypes = validTypes
            if (validTypes.length === 0) {
                msgWarning("由于金额变更，已清空不符合条件的选项")
            }
        }
// 检查总金额是否超过投标保证金
        if (marginFee.value != null && resultForm.destinationTypes.length > 0) {
            const totalSelectedFee = calculateSelectedFeeTotal()
            // 如果总金额大于投标保证金金额，取消所有选项
            if (totalSelectedFee > Number(marginFee.value ?? 0)) {
                resultForm.destinationTypes = []
                msgWarning("投标保证金小于剩余应缴款，不可选中，请重新选择！")
            }
              // 如果总金额等于投标保证金金额并且此时 转为合同尾款选项是被勾选状态，取消所有选项
            console.log(destinationTypes.value,'destinationTypes.value')
            if (totalSelectedFee == Number(marginFee.value ?? 0)&&resultForm.destinationTypes.includes("4")) {
                resultForm.destinationTypes = []
                ElMessage.warning("投标保证金已经用完了，不可以再转合同尾款了")
            }
        }
    } catch (error) {
        resultForm.destinationTypes = []
    }
}
// 投标保证金去向变更处理
const handleDestinationChange = (val) => {
    if (val !== 1) {
        // 如果不是转为剩余应交款，清空费用类型选择
        resultForm.destinationTypes = []
    } else {
        // 如果是转为剩余应交款，检查是否有可选的费用类型
        checkDestinationOptions()
    }
}
function getDefaultEnum(val) {
  // let defaultFlag = Array.isArray(val) ? val.join(",") : "";
  let defaultFlag = val || ""
  console.log(defaultFlag);
if(defaultFlag == "2") {
        resultForm.emumkey = "" // 转入方违约：单选，使用字符串
    } else {
        resultForm.emumkey = [] // 转出方违约或其他：多选，使用数组
    }
     // 单选其他就不出现违约人下拉框
    if(defaultFlag == "3"){
        enumList.value = []
        resultForm.resultdefaultList = []
        emumFlag.value = false
        return
    }else{
        emumFlag.value = true
    }
  // // 切换未成功原因必须清空违约人
  // resultForm.emumkey = [];
  // // 单选其他就不出现违约人下拉框
  // if (defaultFlag == 3) {
  //   enumList.value = [];
  //   resultForm.resultdefaultList = [];
  //   emumFlag.value = false;
  //   return;
  // } else if (defaultFlag == "") {
  //   enumList.value = [];
  //   resultForm.resultdefaultList = [];
  //   emumFlag.value = false;
  //   return;
  // } else {
  //   emumFlag.value = true;
  // }
  $axios({
    url: "/result/getDefaultEnum",
    method: "GET",
    data: {
      defaultFlag,
      tenderId: tenderId.value,
    },
  }).then((res) => {
    if (res.data.code !== 200) return;
    enumList.value = res.data.data;
    enumList.value.forEach((item) => {
      if (item.certNo) {
        item.label = item.name + "（" + item.certNo.slice(-4) + "）";
      } else {
        item.label = item.name;
      }
    });
  });
}
const changeEnum = (val) => {
   resultForm.resultdefaultList = []

    // 转入方违约时为单选，其他为多选
    if(resultForm.reason === "2") {
        // 单选情况：val是字符串
        if(val) {
            const selectedItem = enumList.value.find(item => item.defaultId === val)
            if(selectedItem) {
                resultForm.resultdefaultList = [selectedItem]
            }
        }
    } else {
        // 多选情况：val是数组
        if(Array.isArray(val) && val.length > 0) {
            let temp = enumList.value.map(item=>{
                if(val.find(element => item.defaultId === element)) {
                    return item
                }
            })
            temp = temp.filter((item)=>{
                if(item) return item
            })
            resultForm.resultdefaultList = temp
        }
    }
  // resultForm.resultdefaultList = [];
  // let temp = enumList.value.map((item) => {
  //   if (val.find((element) => item.defaultId === element)) {
  //     return item;
  //   }
  // });
  // temp = temp.filter((item, index) => {
  //   if (item) return item;
  // });
  // resultForm.resultdefaultList = temp;
};
// 验证投标保证金去向
const validateDestination = () => {
    if (Number(marginFee.value ?? 0) > 0) {
        if (!resultForm.tenderDepositDestination && resultForm.tenderDepositDestination !== 0) {
            msgWarning("请选择投标保证金去向!")
            return false
        }

        // 如果选择了转为剩余应交款，但没有选择费用类型
        if (resultForm.tenderDepositDestination === 1 && (!resultForm.destinationTypes || resultForm.destinationTypes.length === 0)) {
            msgWarning("请至少选择一个费用类型!")
            return false
        }
    }
    return true
}
function saveFn() {
  let tradeFlag;
  if (resultForm.changeItems == 1) {
    tradeFlag = 1;
  } else {
    tradeFlag =
      resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 1
        ? 2
        : resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 2
        ? 1
        : 1;
    if (resultForm.changeItems == 3) {
      tradeFlag = 2;
    }
  }
  if (tradeFlag == 1) {
    if (Number(resultForm.overflowScale) < 0)
      return msgWarning("溢价率不能为负数");
  }

   // 处理投标保证金去向费用类型
          if (resultForm.tenderDepositDestination === 0) {
              // 用户选择退款，提交时设置depositDestinationType为字符串"0"
              resultForm.depositDestinationType = "0"
              // resultForm.tenderDepositDestination = null // 清空临时字段
          } else if (resultForm.tenderDepositDestination === 1) {
              // 用户选择转为剩余应交款，将多选框的值转换为逗号分隔的字符串
              resultForm.depositDestinationType = resultForm.destinationTypes.join(',')
              // resultForm.tenderDepositDestination = null // 清空临时字段
          } else {
              // 未设置时
              resultForm.depositDestinationType = ''
              // resultForm.tenderDepositDestination = null
          }
          // 投标保证金去向校验
        if (!validateDestination()) {
            return false
        }
  formRef.value.validate((valid, fields) => {
    if (valid) {
      for (let i = 0; i <= formRespons.uploadList2.length - 1; i++) {
        if (
          formRespons.uploadList2[i].needChoose === "1" &&
          formRespons.uploadList2[i].fileList.length === 0
        ) {
          return msgWarning("请上传附件!");
        }
      }

      resultForm.tradeSignupTradeDtoList =
        resultOldChangeForm.value.tradeSignupTradeVoList;

      let reason;
      // if (resultForm.reason) {
      //   reason = resultForm.reason.join(",");
      // }
      reason = resultForm.reason||"";
      $axios({
        url: `/tradeResultChange/saveResultChange`,
        method: "post",
        data: {
          saveFlag: 1,
          changeFileDto: {
            downFee: resultForm.downFee,
            downFeeFlag: resultForm.downFeeFlag,
            downFeeTime: resultForm.downFeeTime,
            id: resultChangeForm.value.id,
            overflowAmount: resultForm.overflowAmount,
            overflowScale: resultForm.overflowScale,
            perFee: resultForm.perFee,
            perFeeFlag: resultChangeForm.value.sourceFieldInfoVo.perFeeFlag,
            perFeeRule: resultChangeForm.value.sourceFieldInfoVo.perFeeRule,
            perFeeTime: resultForm.perFeeTime,
            perFeeType: resultChangeForm.value.sourceFieldInfoVo.perFeeType,
            projectId: resultChangeForm.value.projectId,
            reason: reason,
            remark: resultForm.remark,
            resultdefaultList: resultForm.resultdefaultList,
            tradeResultDefaultVos: resultForm.tradeResultDefaultVos,
            serviceFee: resultForm.serviceFee,
            serviceFeeFlag:
              resultChangeForm.value.sourceFieldInfoVo.serviceFeeFlag,
            serviceFeeRule:
              resultChangeForm.value.sourceFieldInfoVo.serviceFeeRule,
            serviceFeeTime: resultForm.serviceFeeTime,
            serviceFeeType:
              resultChangeForm.value.sourceFieldInfoVo.serviceFeeType,
            signupId: resultForm.signupId,
            tenderId: tenderId.value,
            tradeAmount: resultForm.tradeAmount,
            // tradeFailReason: resultForm.remark,
            tradeFlag: tradeFlag,
            tradePrice: resultForm.tradePrice,
            tradeTime: resultForm.tradeTime,
            tradeUnit: resultChangeForm.value.tradeUnit,
            tradeUnit_dictText: resultChangeForm.value.tradeUnit_dictText,
            traderPerson: resultForm.traderPerson,
              depositDestinationType: resultForm.depositDestinationType,
          },
          changeItems: resultForm.changeItems,
          changeReason: resultForm.changeReason,
          id: resultChangeForm.value.id,
          projectId: resultChangeForm.value.projectId,
          tenderId: tenderId.value,
        },
      }).then((res) => {
        if (res.data.code === 201) {
          btnDisabled.value = true;
        }
        if (res.data.code === 200) {
          // params == 1 ? msgSuccess("保存成功") :  msgSuccess("提交成功")
          msgSuccess("提交成功");
          close();
          emit("closeRefresh");
        } else {
            btnDisabled.value = false;

          msgWarning(res.data.msg);
        }
        console.log(res, "提交结果登记");
      });
    }
  });
}
function close() {
  for (const key in resultForm) {
    resultForm[key] = "";
  }
  emit("close");
}
// 投标保证金去向费用类型列表
const destinationTypeList = ref([]);
// 选中的投标保证金去向费用类型
const destinationTypes = ref([]);
// init-data
function initData() {
  $axios({
    method: "get",
    url: `/tradeResultChange/getSourceResultInfo/${tenderId.value}`,
  }).then((res) => {
    resultOldChangeForm.value = JSON.parse(JSON.stringify(res.data.data));
    if (
      resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos !== null
    ) {
      let tradeResultDefaultVosLabel =
        resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos.map(
          (item) => {
            if (item.certNo) {
              return item.name + "（" + item.certNo.slice(-4) + "）";
            } else {
              return item.name;
            }
          }
        );
      resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVosLabel =
        tradeResultDefaultVosLabel.join("，");
      console.log(resultOldChangeForm.value);
    }
    // 回显投标保证金去向
    if (
      resultOldChangeForm.value.sourceFieldInfoVo.depositDestinationType !==
      undefined
    ) {
      const destinationValue =
        resultOldChangeForm.value.sourceFieldInfoVo.depositDestinationType;

      if (destinationValue === "0") {
        // 接口返回"0"表示退款，设置单选框为数字0
        resultOldChangeForm.value.sourceFieldInfoVo.tenderDepositDestination = 0;
        destinationTypes.value = [];
      } else if (destinationValue) {
        // 接口返回非"0"值（如"1,2,3"），设置单选框为数字1（转为剩余应交款）
        resultOldChangeForm.value.sourceFieldInfoVo.tenderDepositDestination = 1;
        // 将具体的费用类型字符串分割后回显到多选框
        destinationTypes.value = destinationValue
          .split(",")
          .filter((item) => item.trim() !== "");
      }
    }
    if (resultOldChangeForm.value.sourceFieldInfoVo.reason) {
      let arry = resultOldChangeForm.value.sourceFieldInfoVo.reason.split(",");
      const reasonMap = new Map(
        radioGrounp.map((item) => [item.id, item.name])
      );
      const reason = arry.map((id) => reasonMap.get(id)).filter(Boolean);
      resultOldChangeForm.value.sourceFieldInfoVo.reasonVal = reason.join(",");
    }

    resultChangeForm.value = JSON.parse(JSON.stringify(res.data.data));

    if (resultOldChangeForm.value.changeFieldInfoVo) {
      resultForm.changeReason = resultOldChangeForm.value.changeReason;
      resultForm.changeItems = resultOldChangeForm.value.changeItems + "";
      resultForm.signupId =
        resultOldChangeForm.value.changeFieldInfoVo.signupId;
      resultForm.tradeTime =
        resultOldChangeForm.value.changeFieldInfoVo.tradeTime;
      resultForm.tradePrice =
        resultOldChangeForm.value.changeFieldInfoVo.tradePrice;
      resultForm.tradeAmount =
        resultOldChangeForm.value.changeFieldInfoVo.tradeAmount;
      resultForm.overflowAmount =
        resultOldChangeForm.value.changeFieldInfoVo.overflowAmount;
      resultForm.overflowScale =
        resultOldChangeForm.value.changeFieldInfoVo.overflowScale;
      resultForm.serviceFee =
        resultOldChangeForm.value.changeFieldInfoVo.serviceFee;
      resultForm.serviceFeeTime =
        resultOldChangeForm.value.changeFieldInfoVo.serviceFeeTime;
      resultForm.perFeeRule =
        resultOldChangeForm.value.changeFieldInfoVo.perFeeRule;
      resultForm.perFee = resultOldChangeForm.value.changeFieldInfoVo.perFee;
      resultForm.perFeeTime =
        resultOldChangeForm.value.changeFieldInfoVo.perFeeTime;
      resultForm.downFee = resultOldChangeForm.value.changeFieldInfoVo.downFee;
      resultForm.downFeeTime =
        resultOldChangeForm.value.changeFieldInfoVo.downFeeTime;
      if (resultOldChangeForm.value.changeFieldInfoVo.reason) {
        resultForm.reason =
          resultOldChangeForm.value.changeFieldInfoVo.reason;
        // resultForm.reason =
        //   resultOldChangeForm.value.changeFieldInfoVo.reason.split(",");
        getDefaultEnum(resultForm.reason);
      }
      if (resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos) {
        emumFlag.value = true;
      }
      if (resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos) {
         if(resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos[0].defaultId){
                // 根据未成功原因类型设置违约人数据
                if(resultForm.reason === "2") {
                    // 转入方违约：单选，取第一个
                    resultForm.emumkey = resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos[0].defaultId
                } else {
                    // 转出方违约或其他：多选，取所有
                    resultForm.emumkey= resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos.map(item=> item.defaultId)
                }
                resultForm.resultdefaultList = resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos
            }
        // resultForm.emumkey =
        //   resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos.map(
        //     (item) => item.defaultId
        //   );
        // resultForm.resultdefaultList =
        //   resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos;
      }
      resultForm.remark = resultOldChangeForm.value.changeFieldInfoVo.remark;

      // 回显投标保证金去向
      if (
        resultOldChangeForm.value.changeFieldInfoVo.depositDestinationType !==
        undefined
      ) {
        const destinationValue =
          resultOldChangeForm.value.changeFieldInfoVo.depositDestinationType;

        if (destinationValue === "0") {
          // 接口返回"0"表示退款，设置单选框为数字0
          resultForm.tenderDepositDestination = 0;
          resultForm.destinationTypes = [];
        } else if (destinationValue) {
          // 接口返回非"0"值（如"1,2,3"），设置单选框为数字1（转为剩余应交款）
          resultForm.tenderDepositDestination = 1;
          // 将具体的费用类型字符串分割后回显到多选框
          resultForm.destinationTypes = destinationValue
            .split(",")
            .filter((item) => item.trim() !== "");
        }
      }
    } else {
      resultForm.remark = resultOldChangeForm.value.sourceFieldInfoVo.remark;

      if (resultOldChangeForm.value.sourceFieldInfoVo.reason) {
        resultOldChangeForm.value.sourceFieldInfoVo.reason =
          resultOldChangeForm.value.sourceFieldInfoVo.reason;
        // resultOldChangeForm.value.sourceFieldInfoVo.reason =
        //   resultOldChangeForm.value.sourceFieldInfoVo.reason.split(",");
      }
      resultForm.traderPerson =
        resultChangeForm.value.sourceFieldInfoVo.traderPerson;
      resultForm.signupId = resultChangeForm.value.sourceFieldInfoVo.signupId;
      resultForm.tradeTime = resultChangeForm.value.sourceFieldInfoVo.tradeTime;
      resultForm.tradePrice =
        resultChangeForm.value.sourceFieldInfoVo.tradePrice;
      resultForm.tradeAmount =
        resultChangeForm.value.sourceFieldInfoVo.tradeAmount;
      resultForm.overflowAmount =
        resultChangeForm.value.sourceFieldInfoVo.overflowAmount;
      resultForm.overflowScale =
        resultChangeForm.value.sourceFieldInfoVo.overflowScale;
      resultForm.serviceFeeRule =
        resultChangeForm.value.sourceFieldInfoVo.serviceFeeRule;
      resultForm.serviceFee =
        resultChangeForm.value.sourceFieldInfoVo.serviceFee;
      resultForm.serviceFeeTime =
        resultChangeForm.value.sourceFieldInfoVo.serviceFeeTime;
      resultForm.perFeeRule =
        resultChangeForm.value.sourceFieldInfoVo.perFeeRule;
      resultForm.perFee = resultChangeForm.value.sourceFieldInfoVo.perFee;
      resultForm.perFeeTime =
        resultChangeForm.value.sourceFieldInfoVo.perFeeTime;
      resultForm.downFee = resultChangeForm.value.sourceFieldInfoVo.downFee;
      resultForm.downFeeTime =
        resultChangeForm.value.sourceFieldInfoVo.downFeeTime;
      // resultForm.reason =
      //   resultChangeForm.value.sourceFieldInfoVo.reason;
        if (resultOldChangeForm.value.sourceFieldInfoVo.reason) {
        resultForm.reason =
          resultOldChangeForm.value.sourceFieldInfoVo.reason;
        // resultForm.reason =
        //   resultOldChangeForm.value.changeFieldInfoVo.reason.split(",");
        getDefaultEnum(resultForm.reason);
      }
      //   if (resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos) {
      //   emumFlag.value = true;
      // }
        if (resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos) {
         if(resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos[0].defaultId){
                // 根据未成功原因类型设置违约人数据
                if(resultForm.reason === "2") {
                    // 转入方违约：单选，取第一个
                    resultForm.emumkey = resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos[0].defaultId
                } else {
                    // 转出方违约或其他：多选，取所有
                    resultForm.emumkey= resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos.map(item=> item.defaultId)
                }
                resultForm.resultdefaultList = resultOldChangeForm.value.sourceFieldInfoVo.tradeResultDefaultVos
            }
        // resultForm.emumkey =
        //   resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos.map(
        //     (item) => item.defaultId
        //   );
        // resultForm.resultdefaultList =
        //   resultOldChangeForm.value.changeFieldInfoVo.tradeResultDefaultVos;
      }
      // 回显投标保证金去向
      if (
        resultOldChangeForm.value.sourceFieldInfoVo.depositDestinationType !==
        undefined
      ) {
        const destinationValue =
          resultOldChangeForm.value.sourceFieldInfoVo.depositDestinationType;
        if (destinationValue === "0") {
          // 接口返回"0"表示退款，设置单选框为数字0
          resultForm.tenderDepositDestination = 0;
          resultForm.destinationTypes = [];
        } else if (destinationValue) {
          // 接口返回非"0"值（如"1,2,3"），设置单选框为数字1（转为剩余应交款）
          resultForm.tenderDepositDestination = 1;
          // 将具体的费用类型字符串分割后回显到多选框
          resultForm.destinationTypes = destinationValue
            .split(",")
            .filter((item) => item.trim() !== "");
        }

      }
    }
    computedFn();
    changeId.value = resultChangeForm.value.id;
    getUploadList2();
    getData(resultChangeForm.value.id);
    getDocListFun();
    getSuccessBidderBzj();
  // inflowChange()
  // checkDestinationOptions()

  });
}
// 获取投标保证金去向费用类型枚举值
const getTenderDepositDestinationEnum = () => {
  // 防御性检查，确保tenderId有值
  if (!tenderId.value) {
    return;
  }
  $axios({
    url: "/result/getTenderDepositDestinationEnum",
    method: "get",
    data: {
      tenderId: tenderId.value,
    },
  })
    .then((res) => {
      if (res.data.code === 200) {
        destinationTypeList.value = res.data.data || [];
      } else {
        msgWarning(res.data.msg);
      }
    })
    .catch(() => {
      destinationTypeList.value = [];
    });
};
// 判断费用类型是否禁用
const isDestinationDisabled = (type) => {
  // 服务费
  if (type === "1") {
    if (!resultForm.serviceFee || Number(resultForm.serviceFee) <= 0) {
      return true;
    }
    return false;
  }
  // 首期款
  else if (type === "2") {
    if (!resultForm.downFee || Number(resultForm.downFee) <= 0) {
      return true;
    }
    return false;
  }
  // 履约保证金
  else if (type === "3" && resultForm.perFee) {
     if (!resultForm.perFee || Number(resultForm.perFee) <= 0) {
            return true
        }
    return false;
  }
  // 合同尾款不做金额校验
  else if (type === "4") {
    return false;
  }

  return false;
};
// 文书
const getDocList = reactive({
  arry: [],
});
const documentModifyView = ref();
const pushFlag = (th, item) => {
  //文书操作
  if (th == 1) {
    beforePushFlag(1).then((res) => {
      if (res) {
        documentModifyView.value.open(
          // tenderId.value,
          resultChangeForm.value.id,
          item.id,
          "16",
          1,
          "生成文书",
          resultChangeForm.value.projectId
        );
      }
    });
  } else {
    // documentModifyView.value.open(resData.value.projectId, item.dtbId, "0", 2, '生成文书');
    if (item.dtbId) {
       documentModifyView.value.open(
          resultChangeForm.value.id,
          item.dtbId,
          "16",
          2,
          "预览文书",
          resultChangeForm.value.projectId
        );
      // beforePushFlag(1).then((res) => {
      //   documentModifyView.value.open(
      //     resultChangeForm.value.id,
      //     item.dtbId,
      //     "16",
      //     2,
      //     "生成文书",
      //     resultChangeForm.value.projectId
      //   );
      // });
    } else {
      ElMessage.error("文书未生成,无法预览");
    }
  }
};
function msgSuccess(params) {
  ElMessage({
    type: "success",
    message: params,
  });
}
function msgWarning(params) {
  ElMessage({
    type: "warning",
    message: params,
  });
}
// 生成文书之前，调用保存接口
function beforePushFlag(params) {
  return new Promise((reslove, reject) => {
    formRef.value.validate((valid, fields) => {
      if (valid) {
        resultForm.tradeSignupTradeDtoList =
          resultOldChangeForm.value.tradeSignupTradeVoList;
        let reason;
        // let reason = resultForm.reason.join(",");
        reason = resultForm.reason||"";
        console.log(reason)
        let tradeFlag;
        if (resultForm.changeItems == 1) {
          tradeFlag = 1;
        } else {
          tradeFlag =
            resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 1
              ? 2
              : resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 2
              ? 1
              : 1;
          if (resultForm.changeItems == 3) {
            tradeFlag = 2;
          }
        }
        if (tradeFlag == 1) {
          if (Number(resultForm.overflowScale) < 0)
            return msgWarning("溢价率不能为负数");
        }

         // 处理投标保证金去向费用类型
          if (resultForm.tenderDepositDestination === 0) {
              // 用户选择退款，提交时设置depositDestinationType为字符串"0"
              resultForm.depositDestinationType = "0"
              // resultForm.tenderDepositDestination = null // 清空临时字段
          } else if (resultForm.tenderDepositDestination === 1) {
              // 用户选择转为剩余应交款，将多选框的值转换为逗号分隔的字符串
              resultForm.depositDestinationType = resultForm.destinationTypes.join(',')
              // resultForm.tenderDepositDestination = null // 清空临时字段
          } else {
              // 未设置时
              resultForm.depositDestinationType = ''
              // resultForm.tenderDepositDestination = null
          }
        // let tradeFlag;
        // if (resultForm.changeItems == 1) {
        //   tradeFlag = 1;
        // } else {
        //   tradeFlag =
        //     resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 1
        //       ? 2
        //       : resultChangeForm.value.sourceFieldInfoVo.tradeFlag == 2
        //       ? 1
        //       : 1;
        // }
        // let beforeSubmitFlag = beforeSubmit(2)

        // if(!beforeSubmitFlag) return
        $axios({
          url: `/tradeResultChange/saveResultChange`,
          method: "post",
          data: {
            saveFlag: 0,
            changeFileDto: {
              downFee: resultForm.downFee,
              downFeeFlag: resultForm.downFeeFlag,
              downFeeTime: resultForm.downFeeTime,
              id: resultChangeForm.value.id,
              overflowAmount: resultForm.overflowAmount,
              overflowScale: resultForm.overflowScale,
              perFee: resultForm.perFee,
              perFeeFlag: resultChangeForm.value.sourceFieldInfoVo.perFeeFlag,
              perFeeRule: resultChangeForm.value.sourceFieldInfoVo.perFeeRule,
              perFeeTime: resultForm.perFeeTime,
              perFeeType: resultChangeForm.value.sourceFieldInfoVo.perFeeType,
              projectId: resultChangeForm.value.projectId,
              reason: reason,
              remark: resultForm.remark,
              resultdefaultList: resultForm.resultdefaultList,
              tradeResultDefaultVos: resultForm.tradeResultDefaultVos,
              serviceFee: resultForm.serviceFee,
              serviceFeeFlag:
                resultChangeForm.value.sourceFieldInfoVo.serviceFeeFlag,
              serviceFeeRule:
                resultChangeForm.value.sourceFieldInfoVo.serviceFeeRule,
              serviceFeeTime: resultForm.serviceFeeTime,
              serviceFeeType:
                resultChangeForm.value.sourceFieldInfoVo.serviceFeeType,
              signupId: resultForm.signupId,
              tenderId: tenderId.value,
              tradeAmount: resultForm.tradeAmount,
              // tradeFailReason: resultForm.remark,
              tradeFlag: tradeFlag,
              tradePrice: resultForm.tradePrice,
              tradeTime: resultForm.tradeTime,
              tradeUnit: resultChangeForm.value.tradeUnit,
              tradeUnit_dictText: resultChangeForm.value.tradeUnit_dictText,
              depositDestinationType: resultForm.depositDestinationType,
            },
            changeItems: resultForm.changeItems,
            changeReason: resultForm.changeReason,
            id: resultChangeForm.value.id,
            projectId: resultChangeForm.value.projectId,
            tenderId: tenderId.value,
          },
        }).then((res) => {
          if (res.data.code === 201) {
            btnDisabled.value = true;
          }
          if (res.data.code === 200) {
            // params == 1 ? msgSuccess("保存成功") :  msgSuccess("提交成功")
            msgSuccess("已为您自动保存当前页面信息！");
            reslove(true);
          } else {
            btnDisabled.value = false;
            msgWarning(res.data.msg);
          }
          console.log(res, "提交结果登记");
        });
      } else {
            btnDisabled.value = false;
        console.log("error submit!", fields);
        formRef.value.scrollToField(Object.keys(fields)[0] || "");
      }
    });
  });
}
let getDocListFun = () => {
  // 初始文书列表 ===
  $axios({
    url: "/docInfo/getDocList",
    method: "post",
    data: {
      projectId: resultChangeForm.value.projectId,
      bizId: changeId.value,
      unitId: $detailPageRef.value.villageId
        ? $detailPageRef.value.villageId
        : $detailPageRef.value.townId
        ? $detailPageRef.value.townId
        : $detailPageRef.value.countyId,
      varietyKey: $detailPageRef.value.childTradeVariety,
      flowBaseCode: "FLOW_JGBG",
      type: "16",
    },
  }).then((res) => {
    if (res.data.code === 200) {
      getDocList.arry = res.data.data;
    }
  });
};
// 附件
const formRespons = reactive({
  uploadList2: [],
});
const getUploadList2 = () => {
  // 初始附件信息列表 ===
  $axios({
    url: "/file/getTheAttachmentTypeList",
    method: "post",
    data: {
      busId: resultChangeForm.value.id,
      flowBasecode: "FLOW_JGBG",
      projectId: resultChangeForm.value.projectId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      res.data.data[0].fileList.forEach((item) => {
        let obj = {};
        obj.name = item.fileName;
        obj.url = item.fileUrl;
        obj.id = item.id;
      });
      formRespons.uploadList2 = res.data.data;
    }
  });
};
function handleCurrentChange(val) {
  listForm.pageNo = val;
  resData.list = [];
  initData();
}
function handleSizeChange(val) {
  listForm.pageSize = val;
  resData.list = [];
  initData();
}
</script>

<style lang="scss" scoped>
.list-box {
  display: flex;
  flex-direction: column;
  height: auto;
  padding: 15px;
  border: #eee 1px solid;
  border-radius: 5px;
  flex-direction: row;
  .set-class::before {
    content: "* ";
    color: red;
  }

  .left-box {
    height: 100%;
    width: calc(78% - 10px);
    background: #fff;
    margin-right: 15px;
  }
  .right-box {
    position: sticky;
    top: 0;
    padding: 15px 15px 100px;
    height: 100%;
    width: 22%;
    margin-right: 10px;
    background-color: #fff;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 700;
      color: #0b8df1;
      span {
        margin-left: 8px;
      }
    }
    .el-steps-div {
      padding: 2px;
      :deep(.el-timeline-item__tail) {
        border-left: 2px solid #0b8df1;
      }

      :deep(.el-timeline-item__timestamp) {
        color: #444;
        font-size: 14px;
      }

      :deep(.el-timeline-item__content) {
        color: #909399;
        font-size: 13px;
      }
    }
  }
  .form-box {
    display: flex;
    flex-direction: column;
    .detailsbd-tc-s {
      border-left: 1px solid #eeeeee;
      border-top: 1px solid #eeeeee;

      td {
        border-right: 1px solid #eeeeee;
        border-bottom: 1px solid #eeeeee;
        padding: 7px 8px;
        text-align: left;
        font-size: 14px;
        color: #444444;
        background: #fff;

        .redStar {
          color: red;
        }
      }

      .title {
        color: #606266;
        text-align: right;
        background-color: #f9f9f9;
        font-size: 14px;
      }
    }
  }
  .form-box2 {
    display: flex;
    flex-direction: column;
    color: #606266;
    background: #fff;
    font-size: 14px;
    :deep(.el-form-item) {
      margin: 0;
    }
    :deep(.el-form-item) {
      margin: 0px 0px 14px 0px;
    }
    .form-box-row {
      .list-table-box {
        .row-box {
          div {
            width: 80%;
            padding: 0px 0px;
            display: flex;
            flex-direction: column;
            align-items: left;
            span {
              display: inline-block;
              text-align: left;
              width: 60%;
              // white-space: nowrap;
            }
          }
        }
        .row-box2 {
          .itemChild {
            width: 80%;
            padding: 0px 0px;
            display: flex;
            flex-direction: column;
            align-items: left;
            margin-top: 20px;
            span {
              display: inline-block;
              text-align: left;
              width: 60%;
              // white-space: nowrap;
            }
          }
        }
      }
      table {
        width: 100%;
      }

      tr {
        height: 56px;
        // height: 46px;

        td:nth-child(2n-1) {
          width: 120px;
          text-align: right;
        }

        td:nth-child(2n) {
          text-align: left;
          padding-left: 10px;
        }
      }
      .require::before {
        content: "* ";
        color: red;
      }
    }
  }
}
.cipher-style {
  // margin-left: 5px;
  cursor: pointer;
}
</style>