<template>
  <!-- 结果变更公告 -->
  <div class="list-box">
    <div class="left-box">
      <div class="form-box">
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
          >
            变更公告</span
          >
          <div class="row-class">
            <span>变更公告标题</span>
            <nd-input v-model="submitForm.title" width="80%" maxlength="50" />
          </div>
        </div>
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
          >
            变更公告</span
          >
          <div class="row-class">
            <span
              >变更公告内容 <br />
              <i
                style="font-style: normal; color: #0b8df1; cursor: pointer"
                @click="changeAnnouncement"
                >生成变更公告</i
              >
            </span>
            <div class="editor-container" style="width: 80%">
              <QuillEditor
                :options="editorOption"
                theme="snow"
                contentType="html"
                v-model:content="submitForm.announcementContent"
              />
              <!-- <QuillEditor :options="editorOption" theme="snow" v-model:content="item.text"
                  contentType="html" /> -->
            </div>
          </div>
        </div>
      </div>
      <div class="form-box">
        <div style="padding: 15px 15px 0px 15px">
          <span
            style="
              margin-left: 0;
              color: #606266;
              font-size: 14px;
              font-weight: bold;
            "
          >
            附件信息</span
          >
          <div style="margin-top: 15px">
            <div
              class="upload-box"
              v-for="(item, index) in form.fitsList"
              :key="index"
            >
              <div
                class="upload-box-title"
                :class="item.needChoose == 1 ? 'set-class' : ''"
              >
                {{ item.fileName }}
              </div>
              <div class="upload-box-img">
                <div class="upload-box-img-box">
                  <ndb-upload
                    v-model="item.fileList"
                    :uploadParams="{
                      busId: submitForm.id,
                      configFileId: item.id,
                    }"
                  ></ndb-upload>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="btn-box"
        style="display: flex; justify-content: center; padding: 10px 0px"
      >
        <nd-button
          type="primary"
          color="#0b8df1"
          icon="Finished"
          @click="saveFn()"
          >提&nbsp;交</nd-button
        >
        <nd-button icon="Back" @click="close">取&nbsp;消</nd-button>
      </div>
    </div>

    <div class="right-box">
      <div class="title">
        <el-icon><DocumentCopy /></el-icon>
        <span>审核流程</span>
      </div>
      <div style="width: 100%; height: 15px"></div>
      <div class="el-steps-div">
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :icon="activity.icon"
            :color="activity.color"
            :timestamp="activity.nodeName"
            placement="top"
          >
            <p>
              {{ activity.operator }}
            </p>
            <template v-if="activity.type === 1">
              <p>
                {{
                  activity.flowTime
                    ? "提交时间：" + activity.flowTime
                    : "提交时间："
                }}
              </p>
            </template>
            <template v-else>
              <p>
                {{
                  activity.flowTime
                    ? "审核时间：" + activity.flowTime
                    : "审核时间："
                }}
              </p>
              <p>
                {{
                  activity.status == 1 || activity.status == "通过"
                    ? "审核结果：通过"
                    : "审核结果：不通过"
                }}
              </p>
              <p>
                {{
                  activity.opinion
                    ? "审核意见：" + activity.opinion
                    : "审核意见："
                }}
              </p>
            </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup>
import ndButton from "@/components/ndButton.vue";
import ndTag from "@/components/ndTag.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
import ndbUpload from "@/components/business/ndbUpload2/index.vue";

// el
import { ElMessage, ElMessageBox } from "element-plus";
// 子组件
import underApplyPeople from "./underApplyPeople.vue";
// vue
import { inject, onMounted, ref, nextTick, reactive, shallowRef } from "vue";
//mock
import { useMock } from "./mockData.js";
const { mock, page, tagsData } = useMock();
const $detailPageRef = inject("$detailPageRef");
const $detailPageRef2 = inject("$detailPageRef2");
import { Check, Back } from "@element-plus/icons-vue";

// axios
const $axios = inject("$axios");
// emit
let emit = defineEmits(["editRegister", "closeRefresh", "close"]);
// inject
const activities = ref([]); //审核记录
let form = reactive({
  fitsList: [],
});
let submitForm = ref({});
let tenderId = ref("");

onMounted(() => {
  if (!$detailPageRef.value.tenderId) {
    tenderId.value = $detailPageRef.value.tradeTendersList[0].tenderId;
  } else {
    tenderId.value = $detailPageRef.value.tenderId;
  }
  initData();
  getTenderDepositDestinationEnum();
});
function isSameNumberSet(str1, str2) {
  // 处理 null 或 undefined 的情况
  if (str1 == null || str2 == null) {
    return false; // 或者根据需求返回其他值，如 str1 == null && str2 == null
  }

  // 将字符串按逗号分割成数组，先转换为字符串再分割以避免空值问题
  const arr1 =
    str1.toString().trim() === ""
      ? []
      : str1
          .toString()
          .split(",")
          .map((item) => item.trim())
          .sort();
  const arr2 =
    str2.toString().trim() === ""
      ? []
      : str2
          .toString()
          .split(",")
          .map((item) => item.trim())
          .sort();
  // 比较数组长度
  if (arr1.length !== arr2.length) {
    return false;
  }
  // 逐元素比较数组
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      return false;
    }
  }

  return true;
}
const editorOption = ref({
  modules: {
    toolbar: [
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
      ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
      //['blockquote', 'code-block'], // 引用  代码块
      [{ header: 1 }, { header: 2 }], // 1、2 级标题
      [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
      [{ script: "sub" }, { script: "super" }], // 上标/下标
      [{ indent: "-1" }, { indent: "+1" }], // 缩进
      [{ direction: "rtl" }], // 文本方向
      // [{ size: ['12px', false, '16px', '18px', '20px', '30px'] }], // 字体大小
      [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题

      // [{ font: [false, 'SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial'] }], // 字体种类
      [{ align: [] }], // 对齐方式
      ["clean"], // 清除文本格式
      //['link', 'image', 'video'] // 链接、图片、视频
      // ["image"], // 链接、图片、视频
    ],
  },
  placeholder: "请输入内容",
});
const radioGrounp = [
  { name: "转出方违约", id: "1" },
  { name: "转入方违约", id: "2" },
  { name: "其他", id: "3" },
];
// 投标保证金去向费用类型列表
const destinationTypeList = ref([]);
// 获取投标保证金去向费用类型枚举值
const getTenderDepositDestinationEnum = () => {
  // 防御性检查，确保tenderId有值
  if (!tenderId.value) {
    return;
  }
  $axios({
    url: "/result/getTenderDepositDestinationEnum",
    method: "get",
    data: {
      tenderId: tenderId.value,
    },
  })
    .then((res) => {
      if (res.data.code === 200) {
        destinationTypeList.value = res.data.data || [];
      } else {
        msgWarning(res.data.msg);
      }
    })
    .catch(() => {
      destinationTypeList.value = [];
    });
};
function changeAnnouncement() {
  $axios({
    method: "get",
    url: `/tradeResultChange/getChangeInfo/${submitForm.value.resultChangeId}`,
  }).then((res) => {
    //  ${
    //          res.data.data.sourceFieldInfoVo.serviceFeeFlag == 1 &&
    //          res.data.data.sourceFieldInfoVo.serviceFeeType != 1 &&
    //          res.data.data.sourceFieldInfoVo.serviceFeeType
    //            ? `<span>服务费收取规则:${
    //                res.data.data.sourceFieldInfoVo
    //                           .serviceFeeType == 2 &&
    //                         res.data.data.sourceFieldInfoVo.tradeType == 1?`收取首年租金的:${res.data.data.serviceFeeRule}%`:resultOldChangeForm.sourceFieldInfoVo
    //                           .serviceFeeType == 2 &&
    //                         resultOldChangeForm.sourceFieldInfoVo.tradeType != 1?`收取成交价的:${res.data.data.serviceFeeRule}%`: res.data.data.sourceFieldInfoVo
    //                           .serviceFeeType == 3?`履约金固定金额:${res.data.data.serviceFeeRule}元`:''
    //              }
    //                         </span>`
    //            : ""
    //        }
    if (res.data.code === 200) {
      let arry = res.data.data.changeFieldInfoVo.reason?.split(",") || [];
      let arry2 = res.data.data.sourceFieldInfoVo.reason?.split(",") || [];
      let reasonVal;
      const reasonMap = new Map(
        radioGrounp.map((item) => [item.id, item.name])
      );
      const reason = arry.map((id) => reasonMap.get(id)).filter(Boolean);
      reasonVal = reason.join(",");

      let reasonVal2;
      const reasonMap2 = new Map(
        radioGrounp.map((item) => [item.id, item.name])
      );
      const reason2 = arry2.map((id) => reasonMap2.get(id)).filter(Boolean);
      reasonVal2 = reason2.join(",");
      if (res.data.data.changeFieldInfoVo.tradeResultDefaultVos !== null) {
        let tradeResultDefaultVosLabel =
          res.data.data.changeFieldInfoVo.tradeResultDefaultVos.map((item) => {
            if (item.certNo) {
              return item.name + "（" + item.certNo.slice(-4) + "）";
            } else {
              return item.name;
            }
          });
        res.data.data.changeFieldInfoVo.tradeResultDefaultVosLabel =
          tradeResultDefaultVosLabel.join("，");
      }
      if (res.data.data.sourceFieldInfoVo.tradeResultDefaultVos !== null) {
        let tradeResultDefaultVosLabel =
          res.data.data.sourceFieldInfoVo.tradeResultDefaultVos.map((item) => {
            if (item.certNo) {
              return item.name + "（" + item.certNo.slice(-4) + "）";
            } else {
              return item.name;
            }
          });
        res.data.data.sourceFieldInfoVo.tradeResultDefaultVosLabel =
          tradeResultDefaultVosLabel.join("，");
      }
      console.log(res.data.data.changeFieldInfoVo.tradeResultDefaultVosLabel);
      let html = `<span>原交易结果中</span>`;
      let html2 = `<span>现变更为</span>`;
      if (res.data.data.changeItems == 1) {
        if (
          res.data.data.sourceFieldInfoVo.traderPerson !=
          res.data.data.changeFieldInfoVo.traderPerson
        ) {
          html += `<span>成交方：${
            res.data.data.sourceFieldInfoVo.traderPerson || ""
          }</span>，`;
          html2 += `<span>成交方：${
            res.data.data.changeFieldInfoVo.traderPerson || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.tradeTime !=
          res.data.data.changeFieldInfoVo.tradeTime
        ) {
          html += `<span>成交日期：${
            res.data.data.sourceFieldInfoVo.tradeTime || ""
          }</span>，`;
          html2 += `<span>成交日期：${
            res.data.data.changeFieldInfoVo.tradeTime || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.tradePrice !=
          res.data.data.changeFieldInfoVo.tradePrice
        ) {
          html += `<span>成交价(${res.data.data.tradeUnit_dictText || ""})：${
            res.data.data.sourceFieldInfoVo.tradePrice || ""
          }</span>，`;
          html2 += `<span>成交价(${res.data.data.tradeUnit_dictText || ""})：${
            res.data.data.changeFieldInfoVo.tradePrice || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.tradeAmount !=
          res.data.data.changeFieldInfoVo.tradeAmount
        ) {
          html += `<span>成交总金额（元）：${
            res.data.data.sourceFieldInfoVo.tradeAmount || ""
          }</span>，`;
          html2 += `<span>成交总金额（元）：${
            res.data.data.changeFieldInfoVo.tradeAmount || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.overflowAmount !=
          res.data.data.changeFieldInfoVo.overflowAmount
        ) {
          html += `<span>溢价总金额（元）：${
            res.data.data.sourceFieldInfoVo.overflowAmount || ""
          }</span>，`;
          html2 += `<span>溢价总金额（元）：${
            res.data.data.changeFieldInfoVo.overflowAmount || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.overflowScale !=
          res.data.data.changeFieldInfoVo.overflowScale
        ) {
          html += `<span>溢价率（%）：${
            res.data.data.sourceFieldInfoVo.overflowScale || ""
          }</span>，`;
          html2 += `<span>溢价率（%）：${
            res.data.data.changeFieldInfoVo.overflowScale || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.serviceFee !=
          res.data.data.changeFieldInfoVo.serviceFee
        ) {
          html += `服务费（元）：${
            res.data.data.sourceFieldInfoVo.serviceFee || ""
          }</span>，`;
          html2 += `服务费（元）：${
            res.data.data.changeFieldInfoVo.serviceFee || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.serviceFeeTime !=
          res.data.data.changeFieldInfoVo.serviceFeeTime
        ) {
          html += `服务费截止缴纳时间：${
            res.data.data.sourceFieldInfoVo.serviceFeeTime || ""
          }</span>，`;
          html2 += `服务费截止缴纳时间：${
            res.data.data.changeFieldInfoVo.serviceFeeTime || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.perFee !=
          res.data.data.changeFieldInfoVo.perFee
        ) {
          html += `履约保证金（元）：${
            res.data.data.sourceFieldInfoVo.perFee || ""
          }</span>，`;
          html2 += `履约保证金（元）：${
            res.data.data.changeFieldInfoVo.perFee || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.perFeeTime !=
          res.data.data.changeFieldInfoVo.perFeeTime
        ) {
          html += `履约保证金截止缴纳时间：${
            res.data.data.sourceFieldInfoVo.perFeeTime || ""
          }</span>，`;
          html2 += `履约保证金截止缴纳时间：${
            res.data.data.changeFieldInfoVo.perFeeTime || ""
          }</span>，`;
        }
        const areSame = isSameNumberSet(
          res.data.data.changeFieldInfoVo.depositDestinationType || [],
          res.data.data.sourceFieldInfoVo.depositDestinationType || []
        );
        // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
        const arr1 = new Set(
          (res.data.data.sourceFieldInfoVo.depositDestinationType || "")
            .split(",")
            .filter(Boolean)
        );
        // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
        const arrVal = destinationTypeList.value.flatMap((item) =>
          arr1.has(String(item.type)) ? [item.name] : []
        );
        // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
        const arr2 = new Set(
          (res.data.data.changeFieldInfoVo.depositDestinationType || "")
            .split(",")
            .filter(Boolean)
        );
        // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
        const arrVal2 = destinationTypeList.value.flatMap((item) =>
          arr2.has(String(item.type)) ? [item.name] : []
        );

        if (!areSame) {
          html += `投标保证金去向：${
            // 增加对空值的判断
            res.data.data.sourceFieldInfoVo.depositDestinationType === null ||
            res.data.data.sourceFieldInfoVo.depositDestinationType ===
              undefined ||
            res.data.data.sourceFieldInfoVo.depositDestinationType === ""
              ? ""
              : res.data.data.sourceFieldInfoVo.depositDestinationType === "0"
              ? "退款"
              : "转为剩余应缴款"
          }</span>,`;
          html2 += `投标保证金去向：${
            res.data.data.changeFieldInfoVo.depositDestinationType === null ||
            res.data.data.changeFieldInfoVo.depositDestinationType ===
              undefined ||
            res.data.data.changeFieldInfoVo.depositDestinationType === ""
              ? ""
              : res.data.data.changeFieldInfoVo.depositDestinationType === "0"
              ? "退款"
              : "转为剩余应缴款"
          }</span>,`;
          //  html += `投标保证金去向：${
          //     res.data.data.sourceFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
          //   }</span>，`;
          //   html2 += `投标保证金去向：${
          //     res.data.data.changeFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
          //   }</span>，`;
          html += `${arrVal.join(",")}</span>，`;
          html2 += `${arrVal2.join(",")}</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.downFee !=
          res.data.data.changeFieldInfoVo.downFee
        ) {
          html += `首期款（元）：${
            res.data.data.sourceFieldInfoVo.downFee || ""
          }</span>，`;
          html2 += `首期款（元）：${
            res.data.data.changeFieldInfoVo.downFee || ""
          }</span>，`;
        }
        if (
          res.data.data.sourceFieldInfoVo.downFeeTime !=
          res.data.data.changeFieldInfoVo.downFeeTime
        ) {
          html += `首期款截至缴纳时间：${
            res.data.data.sourceFieldInfoVo.downFeeTime || ""
          }</span>，`;
          html2 += `首期款截至缴纳时间：${
            res.data.data.changeFieldInfoVo.downFeeTime || ""
          }</span>，`;
        }
      }
      if (res.data.data.changeItems == 3) {
        if (res.data.data.sourceFieldInfoVo.tradeFlag == 2) {
          html += `<span>未成功原因：${reasonVal2 || ""}</span>，`;
          if (reasonVal2 != "其他") {
            html += `<span>违约人：${
              res.data.data.sourceFieldInfoVo.tradeResultDefaultVosLabel || ""
            }</span>，`;
          }

          html += `<span>备注：${
            res.data.data.sourceFieldInfoVo.remark || ""
          }</span>`;
          html2 += `<span>未成功原因：${reasonVal || ""}</span>，`;
          if (reasonVal != "其他") {
            html2 += `<span>违约人：${
              res.data.data.changeFieldInfoVo.tradeResultDefaultVosLabel || ""
            }</span>，`;
          }

          html2 += `<span>备注：${
            res.data.data.changeFieldInfoVo.remark || ""
          }</span>`;
        }
      }
      if (res.data.data.changeItems == 2) {
        if (res.data.data.sourceFieldInfoVo.tradeFlag == 1) {
          html += `<span>交易结果：成功</span>，`;
          html += `<span>成交方：${
            res.data.data.sourceFieldInfoVo.traderPerson || ""
          }</span>，`;
          html += `<span>成交日期：${
            res.data.data.sourceFieldInfoVo.tradeTime || ""
          }</span>，`;
          html += `<span>成交价(${res.data.data.tradeUnit_dictText || ""})：${
            res.data.data.sourceFieldInfoVo.tradePrice || ""
          }</span>，`;
          html += `<span>成交总金额（元）：${
            res.data.data.sourceFieldInfoVo.tradeAmount || ""
          }</span>，`;
          html += `<span>溢价总金额（元）：${
            res.data.data.sourceFieldInfoVo.overflowAmount || ""
          }</span>，`;
          html += `<span>溢价率（%）：${
            res.data.data.sourceFieldInfoVo.overflowScale || ""
          }</span>，`;
          html += `服务费（元）：${
            res.data.data.sourceFieldInfoVo.serviceFee || ""
          }</span>，`;
          html += `服务费截止缴纳时间：${
            res.data.data.sourceFieldInfoVo.serviceFeeTime || ""
          }</span>，`;
          html += `履约保证金（元）：${
            res.data.data.sourceFieldInfoVo.perFee || ""
          }</span>，`;
          html += `履约保证金截止缴纳时间：${
            res.data.data.sourceFieldInfoVo.perFeeTime || ""
          }</span>，`;
          const areSame = isSameNumberSet(
            res.data.data.changeFieldInfoVo.depositDestinationType || [],
            res.data.data.sourceFieldInfoVo.depositDestinationType || []
          );
          // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
          const arr1 = new Set(
            (res.data.data.sourceFieldInfoVo.depositDestinationType || "")
              .split(",")
              .filter(Boolean)
          );
          // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
          const arrVal = destinationTypeList.value.flatMap((item) =>
            arr1.has(String(item.type)) ? [item.name] : []
          );
          // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
          const arr2 = new Set(
            (res.data.data.changeFieldInfoVo.depositDestinationType || "")
              .split(",")
              .filter(Boolean)
          );
          // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
          const arrVal2 = destinationTypeList.value.flatMap((item) =>
            arr2.has(String(item.type)) ? [item.name] : []
          );
          if (!areSame) {
            html += `投标保证金去向：${
              // 增加对空值的判断
              res.data.data.sourceFieldInfoVo.depositDestinationType === null ||
              res.data.data.sourceFieldInfoVo.depositDestinationType ===
                undefined ||
              res.data.data.sourceFieldInfoVo.depositDestinationType === ""
                ? ""
                : res.data.data.sourceFieldInfoVo.depositDestinationType === "0"
                ? "退款"
                : "转为剩余应缴款"
            }</span>,`;
            // html2 += `投标保证金去向：${
            //   res.data.data.changeFieldInfoVo.depositDestinationType === null || res.data.data.changeFieldInfoVo.depositDestinationType === undefined || res.data.data.changeFieldInfoVo.depositDestinationType === ''
            //     ? ''
            //     : res.data.data.changeFieldInfoVo.depositDestinationType === '0'
            //       ? '退款'
            //       : '转为剩余应缴款'
            // }</span>，`;
            //  html += `投标保证金去向：${
            //     res.data.data.sourceFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
            //   }</span>，`;
            //   html2 += `投标保证金去向：${
            //     res.data.data.changeFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
            //   }</span>，`;
            html += `${arrVal.join(",")}</span>，`;
            // html2 += `${
            //   arrVal2.join(",")
            // }</span>，`;
          }
          html += `首期款（元）：${
            res.data.data.sourceFieldInfoVo.downFee || ""
          }</span>，`;
          html += `首期款截至缴纳时间：${
            res.data.data.sourceFieldInfoVo.downFeeTime || ""
          }</span>，`;

          html2 += `<span>交易结果：失败</span>，`;
          html2 += `<span>未成功原因：${reasonVal}</span>，`;
          if (reasonVal != "其他") {
            html2 += `<span>违约人：${
              res.data.data.changeFieldInfoVo.tradeResultDefaultVosLabel || ""
            }</span>，`;
          }
          html2 += `<span>备注：${
            res.data.data.changeFieldInfoVo.remark || ""
          }</span>`;
        }
        if (res.data.data.sourceFieldInfoVo.tradeFlag == 2) {
          html += `<span>交易结果：失败</span>，`;
          html2 += `<span>交易结果：成功</span>，`;
          html += `<span>未成功原因：${reasonVal2 || ""}</span>，`;
          html2 += `<span>成交方：${
            res.data.data.changeFieldInfoVo.traderPerson || ""
          }</span>，`;
          html += `<span>违约人：${
            res.data.data.sourceFieldInfoVo.tradeResultDefaultVosLabel || ""
          }</span>，`;
          html += `<span>备注：${
            res.data.data.sourceFieldInfoVo.remark || ""
          }</span>`;
          html2 += `<span>成交日期：${
            res.data.data.changeFieldInfoVo.tradeTime || ""
          }</span>，`;
          html2 += `<span>成交价(${res.data.data.tradeUnit_dictText || ""})：${
            res.data.data.changeFieldInfoVo.tradePrice || ""
          }</span>，`;
          html2 += `<span>成交总金额（元）：${
            res.data.data.changeFieldInfoVo.tradeAmount || ""
          }</span>，`;
          html2 += `<span>溢价总金额（元）：${
            res.data.data.changeFieldInfoVo.overflowAmount || ""
          }</span>，`;
          html2 += `<span>溢价率（%）：${
            res.data.data.changeFieldInfoVo.overflowScale || ""
          }</span>，`;
          html2 += `服务费（元）：${
            res.data.data.changeFieldInfoVo.serviceFee || ""
          }</span>，`;
          html2 += `服务费截止缴纳时间：${
            res.data.data.changeFieldInfoVo.serviceFeeTime || ""
          }</span>，`;
          html2 += `履约保证金（元）：${
            res.data.data.changeFieldInfoVo.perFee || ""
          }</span>，`;
          html2 += `履约保证金截止缴纳时间：${
            res.data.data.changeFieldInfoVo.perFeeTime || ""
          }</span>，`;
          const areSame = isSameNumberSet(
            res.data.data.changeFieldInfoVo.depositDestinationType || [],
            res.data.data.sourceFieldInfoVo.depositDestinationType || []
          );
          // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
          const arr1 = new Set(
            (res.data.data.sourceFieldInfoVo.depositDestinationType || "")
              .split(",")
              .filter(Boolean)
          );
          // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
          const arrVal = destinationTypeList.value.flatMap((item) =>
            arr1.has(String(item.type)) ? [item.name] : []
          );
          // 获取源数据中投标保证金去向费用类型，按逗号分割成数组
          const arr2 = new Set(
            (res.data.data.changeFieldInfoVo.depositDestinationType || "")
              .split(",")
              .filter(Boolean)
          );
          // 使用 flatMap 遍历 destinationTypeList，筛选出符合条件的名称
          const arrVal2 = destinationTypeList.value.flatMap((item) =>
            arr2.has(String(item.type)) ? [item.name] : []
          );
          if (!areSame) {
            // html += `投标保证金去向：${
            //   // 增加对空值的判断
            //   res.data.data.sourceFieldInfoVo.depositDestinationType === null ||
            //   res.data.data.sourceFieldInfoVo.depositDestinationType ===
            //     undefined ||
            //   res.data.data.sourceFieldInfoVo.depositDestinationType === ""
            //     ? ""
            //     : res.data.data.sourceFieldInfoVo.depositDestinationType === "0"
            //     ? "退款"
            //     : "转为剩余应缴款"
            // }</span>，`;
            html2 += `投标保证金去向：${
              res.data.data.changeFieldInfoVo.depositDestinationType === null ||
              res.data.data.changeFieldInfoVo.depositDestinationType ===
                undefined ||
              res.data.data.changeFieldInfoVo.depositDestinationType === ""
                ? ""
                : res.data.data.changeFieldInfoVo.depositDestinationType === "0"
                ? "退款"
                : "转为剩余应缴款"
            }</span>,`;
            //  html += `投标保证金去向：${
            //     res.data.data.sourceFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
            //   }</span>，`;
            //   html2 += `投标保证金去向：${
            //     res.data.data.changeFieldInfoVo.depositDestinationType===0?'退款':'转为剩余应缴款'
            //   }</span>，`;
            html += `${arrVal.join(",")}</span>，`;
            html2 += `${arrVal2.join(",")}</span>，`;
          }
          html2 += `首期款（元）：${
            res.data.data.changeFieldInfoVo.downFee || ""
          }</span>，`;
          html2 += `首期款截至缴纳时间：${
            res.data.data.changeFieldInfoVo.downFeeTime || ""
          }</span>，`;

          // if (
          //   res.data.data.sourceFieldInfoVo.traderPerson !=
          //   res.data.data.changeFieldInfoVo.traderPerson
          // ) {
          //   // html += `<span>成交方：${res.data.data.sourceFieldInfoVo.traderPerson||''}</span>，`;
          //   html2 += `<span>成交方：${res.data.data.changeFieldInfoVo.traderPerson||''}</span>，`;
          // }

          // if (
          //   res.data.data.sourceFieldInfoVo.tradeTime !=
          //   res.data.data.changeFieldInfoVo.tradeTime
          // ) {
          //   // html += `<span>成交日期：${res.data.data.sourceFieldInfoVo.tradeTime||''}</span>，`;
          //   html2 += `<span>成交日期：${res.data.data.changeFieldInfoVo.tradeTime||''}</span>，`;
          // }
          // if (
          //   res.data.data.sourceFieldInfoVo.tradePrice !=
          //   res.data.data.changeFieldInfoVo.tradePrice
          // ) {
          //   // html += `<span>成交价：${res.data.data.sourceFieldInfoVo.tradePrice||''}</span>，`;
          //   html2 += `<span>成交价：${res.data.data.changeFieldInfoVo.tradePrice||''}</span>，`;
          // }
          // if (
          //   res.data.data.sourceFieldInfoVo.tradeAmount !=
          //   res.data.data.changeFieldInfoVo.tradeAmount
          // ) {
          //   // html += `<span>成交总金额（元）：${res.data.data.sourceFieldInfoVo.tradeAmount||''}</span>，`;
          //   html2 += `<span>成交总金额（元）：${res.data.data.changeFieldInfoVo.tradeAmount||''}</span>，`;
          // }
          // if (
          //   res.data.data.sourceFieldInfoVo.overflowAmount !=
          //   res.data.data.changeFieldInfoVo.overflowAmount
          // ) {
          //   // html += `<span>溢价总金额（元）：${res.data.data.sourceFieldInfoVo.overflowAmount||''}</span>，`;
          //   html2 += `<span>溢价总金额（元）：${res.data.data.changeFieldInfoVo.overflowAmount||''}</span>，`;
          // }
          // if (
          //   res.data.data.sourceFieldInfoVo.overflowScale !=
          //   res.data.data.changeFieldInfoVo.overflowScale
          // ) {
          //   // html += `<span>溢价率（%）：${res.data.data.sourceFieldInfoVo.overflowScale||''}</span>，`;
          //   html2 += `<span>溢价率（%）：${res.data.data.changeFieldInfoVo.overflowScale||''}</span>，`;
          // }
        }
      }
      // console.log(html)
      let content = `<p>一、项目名称：${submitForm.value.projectName}</p>`;
      content += `<p>二、项目编号：${submitForm.value.projectCode}</p>`;
      content += `<p>三、标段编号：${res.data.data.tenderCode}</p>`;
      content += `<p>四、变更原因：${res.data.data.changeReason}</p>`;
      content += `<p>五、变更内容：${submitForm.value.projectName}${html}${html2}</p>`;
      // if(res.data.data.changeItems == 2){
      //   if(res.data.data.sourceFieldInfoVo.traderPerson!=res.data.data.changeFieldInfoVo.traderPerson){
      //     content+=`<p>成交方：${res.data.data.sourceFieldInfoVo.traderPerson}</p>`
      //   }
      //   if(res.data.data.sourceFieldInfoVo.tradeTime!=res.data.data.changeFieldInfoVo.tradeTime){
      //     content+=`<p>成交日期：${res.data.data.sourceFieldInfoVo.tradeTime}</p>`
      //   }
      // }
      // content += `<p>四、变更内容:${submitForm.value.projectName}原交易结果中"成交方：${res.data.data.sourceFieldInfoVo.traderPerson},成交日期：${res.data.data.sourceFieldInfoVo.tradeTime},成交价：${res.data.data.sourceFieldInfoVo.tradePrice},成交总金额（元）：${res.data.data.sourceFieldInfoVo.tradeAmount},溢价总金额（元）：${res.data.data.sourceFieldInfoVo.overflowAmount},溢价率（%）：${res.data.data.sourceFieldInfoVo.overflowScale},现变更为"成交方：${res.data.data.changeFieldInfoVo.traderPerson},成交日期：${res.data.data.changeFieldInfoVo.tradeTime},成交价：${res.data.data.changeFieldInfoVo.tradePrice},成交总金额（元）：${res.data.data.changeFieldInfoVo.tradeAmount}, 溢价总金额（元）：${res.data.data.changeFieldInfoVo.overflowAmount},溢价率（%）：${res.data.data.changeFieldInfoVo.overflowScale}
      //     "</p>`;
      submitForm.value.announcementContent = content;
    }
  });
}
function close() {
  // for (const key in resultForm) {
  //   resultForm[key] = "";
  // }
  emit("close");
}
function saveFn() {
  if (!submitForm.value.projectName) {
    return msgWarning("变更公告标题不能为空!");
  }
  if (!submitForm.value.announcementContent) {
    return msgWarning("变更公告内容不能为空!");
  }
  for (let i = 0; i <= form.fitsList.length - 1; i++) {
    if (
      form.fitsList[i].needChoose === "1" &&
      form.fitsList[i].fileList.length === 0
    ) {
      return msgWarning("请上传附件!");
    }
  }
  $axios({
    url: `/resultChangeAn/changeAnSubmit`,
    method: "post",
    data: submitForm.value,
  }).then((res) => {
    if (res.data.code === 200) {
      msgSuccess("提交成功");
      close();
      emit("closeRefresh");
    } else {
      msgWarning(res.data.msg);
    }
    console.log(res, "提交结果登记");
  });
}
function msgSuccess(params) {
  ElMessage({
    type: "success",
    message: params,
  });
}
function msgWarning(params) {
  ElMessage({
    type: "warning",
    message: params,
  });
}
function initData() {
  $axios({
    method: "get",
    url: `/resultChangeAn/getChangeAnApply/${tenderId.value}`,
  }).then((res) => {
    submitForm.value = res.data.data;
    getData(res.data.data.id);
    getTheAttachmentTypeList(res.data.data);
  });
}
function getData(id) {
  activities.value = [];
  $axios({
    method: "get",
    url: `/resultChangeAn/getAllFlowNode/${id}`,
  }).then((res) => {
    let obj = res.data.data;
    let arr = [];
    for (let i = 0; i < obj.length; i++) {
      if (obj[i].type === 2) {
        arr.push(obj[i]);
      }
    }
    activities.value = obj;
    // 审核结果
    // auditResut.value = arr.slice(-1)[0];
    // 审核记录
    activities.value.forEach((item, index) => {
      item.type === 1
        ? (item.operator = "提交人：" + item.operator)
        : (item.operator = "审核人：" + item.operator);
      item.icon = shallowRef(Check);
      item.color = "#0B8DF1";
    });
  });
}
// 获取 查询附件类型列表
const getTheAttachmentTypeList = (row) => {
  $axios({
    url: "/file/getTheAttachmentTypeList",
    method: "post",
    data: {
      busId: row.id,
      flowBasecode: "FLOW_JGBGGG",
      projectId: row.projectId,
    },
  }).then((res) => {
    // console.log('查询附件类型列表', res);
    if (res.data.code == 200) {
      form.fitsList = res.data.data;
    }
  });
};
</script>

<style lang="scss" scoped>
.list-box {
  display: flex;
  flex-direction: column;
  height: auto;
  padding: 15px;
  border: #eee 1px solid;
  border-radius: 5px;
  flex-direction: row;
  .left-box {
    height: 100%;
    width: calc(78% - 10px);
    background: #fff;
    margin-right: 15px;
  }
  .right-box {
    position: sticky;
    top: 0;
    padding: 15px 15px 100px;
    height: 100%;
    width: 22%;
    margin-right: 10px;
    background-color: #fff;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 700;
      color: #0b8df1;
      span {
        margin-left: 8px;
      }
    }
    .el-steps-div {
      padding: 2px;
      :deep(.el-timeline-item__tail) {
        border-left: 2px solid #0b8df1;
      }

      :deep(.el-timeline-item__timestamp) {
        color: #444;
        font-size: 14px;
      }

      :deep(.el-timeline-item__content) {
        color: #909399;
        font-size: 13px;
      }
    }
  }
  .form-box {
    display: flex;
    flex-direction: column;
    .row-class {
      width: 90%;
      margin: 0 auto;
      display: flex;
      align-items: center;
      span {
        font-size: 14px;
        color: #444444;
        margin-right: 10px;
      }
      span::before {
        content: "*";
        color: red;
      }
      display: flex;
      flex-direction: row;
    }
  }
}
.cipher-style {
  // margin-left: 5px;
  cursor: pointer;
}
</style>