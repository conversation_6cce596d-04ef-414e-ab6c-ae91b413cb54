<template>
  <!-- 产权交易主页面 -->
  <ndb-page ref="ndbPageRef" backgroundColor="#f7f8fa">
    <div class="main-box">
      <div class="second-box">
        <div class="search-box">
          <ndb-page-list>
            <template #tag>
              <template></template>
              <el-badge
                :value="item.checked && tagCountFlag ? resData.total : ''"
                v-for="(item, index) in tagsData"
                :key="index"
                type="primary"
                class="small-view"
              >
                <nd-tag
                  @click="tagChange(index, item)"
                  style="margin-right: 10px"
                  :checked="item.checked"
                  >{{ item.name }}</nd-tag
                >
              </el-badge>
              <el-dropdown
              v-if="tagsDrop.length > 0"
                trigger="click"
                @command="dropItemClick"
                class="dropdown-menu small-view"
              >
                <nd-tag>
                  更多&nbsp;<el-icon><ArrowDown /></el-icon>
                </nd-tag>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="(item, index) in tagsDrop"
                      :key="index"
                      :command="item"
                      :style="
                        tagIndex === item.status
                          ? { backgroundColor: '#ecf5ff', color: '#409eff' }
                          : ''
                      "
                    >
                      {{ item.name }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- el-badge-all -->
              <el-badge
                :value="item.checked && tagCountFlag ? resData.total : ''"
                v-for="(item, index) in tagsDataAll"
                :key="index"
                type="primary"
                class="big-view"
              >
                <nd-tag
                  @click="tagAllChange(index, item)"
                  style="margin-right: 10px"
                  :checked="item.checked"
                  >{{ item.name }}</nd-tag
                >
              </el-badge>
            </template>
            <template #search>
              <com-search
                @searchFn="searchFn"
                @paginSearch="paginSearch"
                ref="comSearchRef"
              />
            </template>
            <template #button>
              <nd-button
                type="primary"
                icon="FolderAdd"
                @click="projectClick"
                authKey="trade:project:add"
                >项目登记</nd-button
              >
              <nd-button
                icon="Upload"
                @click="exportExcel"
                authKey="trade:project:export"
                >项目导出</nd-button
              >
            </template>
            <template #table>
              <nd-table
                :data="resData.table"
                :row-key="(row) => row.id"
                v-loading="resData.loading"
                :tree-props="{
                  children: 'tradeTendersList',
                  hasChildren: 'hasChildren',
                }"
                height="100%"
                class="table-box"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  prop="parentIndex"
                  width="55"
                  fixed
                  type=""
                />
                <el-table-column
                  header-align="center"
                  align=""
                  label="项目编号"
                  prop="code"
                  fixed
                  :min-width="pageIpConfig.proCodeWidth"
                  show-overflow-tooltip
                />
                <el-table-column
                  header-align="center"
                  label="项目名称"
                  prop="name"
                  :min-width="pageIpConfig.proNameWidth"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">{{
                      row.name
                    }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  align="center"
                  label="交易品种"
                  prop="childTradeVarietyName"
                 :min-width="pageIpConfig.childTradeVarietyWidth"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      v-if="row.tradeMode == 0"
                      class="point"
                      @click="projectDetail(row)"
                      >{{ row.childTradeVarietyName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                v-if="!pageIpConfig.busTypeName"
                  align="center"
                  label="业务类型"
                  prop="busTypeName"
                  min-width="130"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      v-if="row.busType == 1"
                      class="point"
                      @click="projectDetail(row)"
                      >标准流程</span
                    >
                    <span
                      v-else-if="row.busType == 2"
                      class="point"
                      @click="projectDetail(row)"
                      >绿色通道</span
                    >
                    <span
                      v-else-if="row.busType == 3"
                      class="point"
                      @click="projectDetail(row)"
                      >专场项目</span
                    >
                    <span
                      v-else-if="row.busType == 4"
                      class="point"
                      @click="projectDetail(row)"
                      >项目补录</span
                    >
                    <span
                      v-else-if="row.busType == 5"
                      class="point"
                      @click="projectDetail(row)"
                      >续租项目</span
                    >
                  </template>
                </el-table-column>
                <el-table-column v-if="!pageIpConfig.tradeMode" align="center" label="交易模式" width="120">
                  <template #default="{ row }">
                    <span
                      v-if="row.tradeMode == 0"
                      class="point"
                      @click="projectDetail(row)"
                      >委托交易</span
                    >
                    <span
                      v-else-if="row.tradeMode == 1"
                      class="point"
                      @click="projectDetail(row)"
                      >自主交易</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="状态"
                  prop="statusStr"
                  width="180"
                >
                  <template #default="{ row }">
                    <div class="status-circle" @click="projectDetail(row)">
                      <span
                        v-if="row.statusStr == '--' || row.statusStr == null"
                        style="display: none"
                      ></span>
                      <span
                        v-else-if="
                          row.statusStr.includes('待') ||
                          row.statusStr.includes('未')
                        "
                        style="background: #0b8df1"
                      ></span>
                      <span
                        v-else-if="
                          row.statusStr.includes('结束') ||
                          row.statusStr.includes('流标') ||
                          row.statusStr.includes('终止') ||
                          row.statusStr.includes('撤回')
                        "
                        style="background: #909399"
                      ></span>
                      <span
                        v-else-if="
                          row.statusStr.includes('已') ||
                          row.statusStr.includes('完成') || 
                          row.statusStr.includes('成功') 
                        "
                        style="background: #67c23a"
                      ></span>
                      <span
                        v-else-if="row.statusStr.includes('中')"
                        style="background: #ff8a00"
                      ></span>
                      <span
                        v-else-if="
                          row.statusStr.includes('不') ||
                         row.statusStr.includes('失败')
                        "
                        style="background: #f56c6c"
                      ></span>
                      <span v-else style="background: #0b8df1"></span>
                      {{ row.statusStr ? row.statusStr : "--" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                v-if="!pageIpConfig.countyName"
                  align="center"
                  label="县（市、区）"
                  min-width="150"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.cityName + row.countyName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                v-if="!pageIpConfig.townName"
                  align="center"
                  label="乡镇(街道)"
                  prop="townName"
                  min-width="150"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.townName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                v-if="!pageIpConfig.villageName"
                  align="center"
                  label="村(社区)"
                  prop="villageName"
                  min-width="150"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.villageName ? row.villageName : "--" }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                v-if="!pageIpConfig.createName"
                  align="center"
                  label="创建人"
                  prop="createName"
                  min-width="150"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.createName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="创建日期"
                  prop="createTime"
                  min-width="150"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.createTime }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="经办人"
                  prop="handlerName"
                  min-width="120"
                >
                  <template #default="{ row }">
                    <span class="point" @click="projectDetail(row)">
                      {{ row.handlerName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="操作"
                  fixed="right"
                  width="150"
                >
                  <template #default="{ row }">
                    <!-- 已登记，flowStatuses，flowStatus -->
                    <!-- 编辑 -->
                    <nd-button
                      authKey="trade:project:edit"
                      @click.stop="projectEdit(row)"
                      type="edit"
                      v-if="
                        (row.flowStatuses == 11 ||
                          row.flowStatuses == 12 ||
                          row.flowStatuses == 13) &&
                        !row.tenderId
                      "
                    >
                    </nd-button>
                     <!-- 编辑2 -->
                    <nd-button
                      authKey="trade:project:edit"
                      @click.stop="projectEdit2(row)"
                      type="edit"
                      v-if="
                        (row.flowStatuses == 11 ||
                          row.flowStatuses == 12 ||
                          row.flowStatuses == 13) &&
                        !row.tenderId
                      "
                    >
                    编辑2
                    </nd-button>
                    <!-- 删除 -->
                    <nd-button
                      authKey="trade:project:remove"
                      @click.stop="deleteFn(row)"
                      type="delete"
                      v-if="row.flowStatuses == 11 && !row.tenderId"
                      :disabled="btnDisabled.disDelete"
                    >
                    </nd-button>

                    <!-- 审核中 -->
                    <!-- 撤回 -->
                    <nd-button
                      authKey="trade:project:withdraw"
                      @click.stop="withdraw(row)"
                      type="reset"
                      v-if="
                        (row.flowStatuses == 21 || row.flowStatuses == 22) &&
                        !row.tenderId
                      "
                    >
                    </nd-button>

                    <!-- 待发布 -->
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(3) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>
                    <!-- 项目状态为公告待提交，【编辑公告】同时显示在项目行和标段行 -->
                    <!-- 编辑公告 -->
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-if="row.projectAndTender"
                    >
                    </nd-button>

                    <!-- 公告待提交 -->
                    <!-- 编辑公告 -->
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-else-if="row.flowStatus == 31 && row.annoFlag"
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-else-if="row.flowStatuses == 31 && row.singleFlag"
                    >
                    </nd-button>

                    <!-- 公告审核不通过，项目，标段 -->
                    <!-- 编辑公告 -->
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-if="row.flowStatus == 34 && row.annoFlag"
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-else-if="row.flowStatuses == 34 && row.singleFlag"
                    >
                    </nd-button>

                    <!-- 公告已撤回 -->
                    <!-- 编辑公告 -->
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-if="row.flowStatus == 35 && row.annoFlag"
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:tradeDetail"
                      @click.stop="announcementEdit(row)"
                      type="editNotice"
                      v-else-if="row.flowStatuses == 35 && row.singleFlag"
                    >
                    </nd-button>

                    <!-- 公告待审核,公告审核中-->
                    <!-- announcement:all:revoke  撤回公告 -->
                    <!-- 撤回 -->
                    <nd-button
                      authKey="announcement:tradeList:revoke"
                      @click.stop="annoWithdraw(row)"
                      type="noticeWithdraw"
                      v-if="
                        (row.flowStatus == 32 || row.flowStatus == 33) &&
                        row.annoFlag
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:revoke"
                      @click.stop="annoWithdraw(row)"
                      type="noticeWithdraw"
                      v-else-if="
                        (row.flowStatuses == 32 || row.flowStatuses == 33) &&
                        row.singleFlag
                      "
                    >
                    </nd-button>

                    <!-- announcement:all:repeat announcement:tradeList:repeat -->
                    <!-- 公告重发 -->
                    <nd-button
                      authKey="announcement:tradeList:repeat"
                      @click.stop="openRetransmissionInformation(row)"
                      type="noticeRepeat"
                      v-if="
                        (row.flowStatus == 36) &&
                        row.annoFlag
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:repeat"
                      @click.stop="openRetransmissionInformation(row)"
                      type="noticeRepeat"
                      v-else-if="
                        (row.flowStatuses == 36 ) &&
                        row.singleFlag
                      "
                    >
                    </nd-button>
                    <!-- 新增：公告变更 (自主模式，不显示公告变更)-->
                    <nd-button
                      authKey="announcement:tradeList:repeat"
                      @click.stop="announcementEdit(row, false)"
                      type="noticeRetry"
                      v-if="
                        (row.flowStatus == 41 ||
                          row.flowStatus == 42 ||
                          row.flowStatus == 43 || 
                          row.flowStatus == 51 ||
                          row.flowStatus == 52 ||
                          row.flowStatus == 53) &&
                        row.annoFlag &&
                        row.tradeMode != 1
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:repeat"
                      @click.stop="announcementEdit(row, false)"
                      type="noticeRetry"
                      v-else-if="
                        (row.flowStatus == 41 ||
                          row.flowStatus == 42 ||
                          row.flowStatus == 43 ||
                          row.flowStatuses == 51 ||
                          row.flowStatuses == 52 ||
                          row.flowStatuses == 53) &&
                        row.singleFlag &&
                        row.tradeMode != 1
                      "
                    >
                    </nd-button>

                    <!-- 已发布 -->
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(4) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>

                    <!-- 报名中 -->
                    <!--专场，项目行，row.busType == 3 -->
                    <!-- trade:signup:add -->
                    <!-- 报名登记 -->
                    <nd-button
                      authKey="trade:signup:add"
                      @click.stop="underApplyClick(row)"
                      type="register"
                      v-if="
                        (row.flowStatus == 51 ||
                          row.flowStatus == 52 ||
                          row.flowStatus == 53) &&
                        row.busType == 3 &&
                        !row.tenderId
                      "
                    >
                    </nd-button>
                    <!--标准流，标段行，row.busType == 1 -->
                    <nd-button
                      authKey="trade:signup:add"
                      @click.stop="underApplyClick(row)"
                      type="register"
                      v-else-if="
                        (row.flowStatus == 51 ||
                          row.flowStatus == 52 ||
                          row.flowStatus == 53) &&
                        row.busType == 1 &&
                        row.tenderId
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="trade:signup:add"
                      @click.stop="underApplyClick(row)"
                      type="register"
                      v-else-if="
                        (row.flowStatuses == 51 ||
                          row.flowStatuses == 52 ||
                          row.flowStatuses == 53) &&
                        row.busType == 1 &&
                        row.singleFlag02
                      "
                    >
                    </nd-button>
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(5) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>

                    <!-- 待竞价 -->
                    <!-- trade:ruleInfo:save -->
                    <!-- 设置规则 -->
                    <nd-button
                      authKey="trade:ruleInfo:setRuleInfo"
                      @click.stop="biddingFn(row)"
                      type="ruleInfo"
                      v-if="
                        (row.flowStatuses == 61 || row.flowStatuses == 63) &&
                        row.tenderFlag &&
                        row.bidTypeFlag
                      "
                    >
                    </nd-button>
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(6) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>

                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="row.endFlag == 0 && (row.flowStatus==110||row.flowStatus==120||row.flowStatus==130||row.flowStatus==132||row.flowStatus==133||row.flowStatus==140)"
                    >
                    </nd-button>

                    <!-- 竞价中 71，75 -->
                    <!-- 针对在线竞价的项目，点击【结果登记】时需要判断当前标段交易是否结束，否则弹出框提示“当前标段交易暂未结束”。在线竞价项目是否交易结束由竞价中心返回。 -->
                    <!-- trade:result:check -->
                    <!-- 结果登记 -->
                    <nd-button
                      authKey="trade:result:add"
                      @click.stop="underBidResultRegisterClick(row)"
                      type="resultRegis"
                      v-if="
                        (row.flowStatuses == 71 ||
                          row.flowStatuses == 75 ||
                          row.flowStatuses == 83 ||
                          row.flowStatuses == 84) &&
                        row.tenderFlag
                      "
                    >
                    </nd-button>
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(7) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>
                    <!-- 撤回 结果登记 -->
                    <nd-button
                      authkey="result:audit:withdraw"
                      type="resultWithdraw"
                      @click.stop="withdrawByResultId(row)"
                      v-if="
                        (row.flowStatus == 81 || row.flowStatus == 82) &&
                        row.tenderId
                      "
                    ></nd-button>
                    <nd-button
                      authkey="result:audit:withdraw"
                      type="resultWithdraw"
                      @click.stop="withdrawByResultId(row)"
                      v-else-if="
                        (row.flowStatuses == 81 || row.flowStatuses == 82) &&
                        row.singleFlag02
                      "
                    ></nd-button>

                    <!-- 已结束 -->
                    <!-- result:audit:withdraw -->
                    <!-- 撤回 结果公告 -->
                    <nd-button
                      authKey="tradeList:result:revoke"
                      @click.stop="finishWithdraw(row)"
                      type="revokeWithdraw"
                      v-if="
                        (row.flowStatus == 86 || row.flowStatus == 87) &&
                        row.annoFlag
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="tradeList:result:revoke"
                      @click.stop="finishWithdraw(row)"
                      type="revokeWithdraw"
                      v-else-if="
                        (row.flowStatuses == 86 || row.flowStatuses == 87) &&
                        row.singleFlag
                      "
                    >
                    </nd-button>
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(8) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>
                    <!-- 以标段维度发布公告的记录，【结果公示】显示在标段行。反之亦然 -->
                    <!-- 结果公示 -->
                    <nd-button
                      authKey="announcement:tradeList:resultDetail"
                      @click.stop="finishedResultPublicityClick(row)"
                      type="resultPublic"
                      v-if="
                        (row.flowStatus == 89 ||
                          row.flowStatus == 88 ||
                          row.flowStatus == 85 ||
                          row.flowStatus == 73) &&
                        row.annoFlag &&row.resultChangeState!=2&&row.resultChangeState!=21
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:tradeList:resultDetail"
                      @click.stop="finishedResultPublicityClick(row)"
                      type="resultPublic"
                      v-else-if="
                        (row.flowStatuses == 89 ||
                          row.flowStatuses == 88 ||
                          row.flowStatuses == 85 ||
                          row.flowStatuses == 73) &&
                        row.singleFlag &&row.resultChangeState!=2&&row.resultChangeState!=21
                      "
                    >
                    </nd-button>

                    <!-- 已公示 -->
                    <!-- 公示质疑 -->
                     <nd-button
                      authKey="announcement:question:add"
                      @click.stop="publishedQueryClick(row)"
                      type="publicQuery"
                      v-if="(row.flowStatus == 91 && row.annoFlag) ||(row.resultChangeState==3||row.resultChangeState == 6||row.resultChangeState == 7)||(row.resultChangeState == 2||row.resultChangeState == 21)"
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:question:add"
                      @click.stop="publishedQueryClick(row)"
                      type="publicQuery"
                      v-else-if="(row.flowStatuses == 91 && row.singleFlag) ||(row.resultChangeState==3||row.resultChangeState == 6||row.resultChangeState == 7)||(row.resultChangeState == 2||row.resultChangeState == 21)"
                    >
                    </nd-button>
                    <!-- <nd-button
                      authKey="announcement:question:add"
                      @click.stop="publishedQueryClick(row)"
                      type="publicQuery"
                      v-if="row.flowStatus == 91 && row.annoFlag"
                    >
                    </nd-button>
                    <nd-button
                      authKey="announcement:question:add"
                      @click.stop="publishedQueryClick(row)"
                      type="publicQuery"
                      v-else-if="row.flowStatuses == 91 && row.singleFlag"
                    >
                    </nd-button> -->
                    <!-- trade:contract:save  trade:project:contractSave -->
                    <!-- 合同登记 -->
                    <nd-button
                      link
                      type="contractRegister"
                      @click.stop="contractRegist(row)"
                      v-if="row.flowStatuses == 92 && row.contractTenderFlag&& row.resultChangeState!=3&&row.resultChangeState != 6&&row.resultChangeState != 7"
                    />
                    <!-- 终止 -->
                    <nd-button
                      authKey="trade:project:abort"
                      @click.stop="toBeReleasedClick(row)"
                      type="terminate"
                      v-if="
                        row.flowStatuses?.toString().indexOf(9) === 0 &&
                        row.endFlag == 0
                      "
                    >
                    </nd-button>

                    <!-- 已终止 -->
                    <!-- 重发申请 -->
                    <nd-button
                      authKey="trade:project:retry"
                      @click.stop="repeatApply(row)"
                      type="applyRepeat"
                      v-if="
                        !row.tenderId &&
                        (row.flowStatuses == 73 ||
                          (row.publissStatus != 102 && row.tradeFlag == 2) ||
                          row.flowStatuses == 101) &&row.resultChangeState!=3
                      "
                    >
                    </nd-button>
                    <nd-button
                      authKey="trade:project:retry"
                      @click.stop="repeatApply(row)"
                      type="applyRepeat"
                      v-else-if="
                        row.tenderId &&
                        (row.flowStatuses == 73 ||
                          (row.flowStatuses != 102 && row.tradeFlag == 2) ||
                          row.flowStatuses == 101) &&row.resultChangeState!=3
                      "
                    >
                    </nd-button>

                    <!-- 交易准备 -->
                    <!-- v-if="row.tenderId || row.tenderCount === 1" -->
                    <nd-button
                      authKey="trade:signup:Transaction"
                      @click.stop="goDealPrepare(row)"
                      type="dealPrepare"
                      v-if="dealPrepareBtn(row)"
                    >
                    </nd-button>
                    <!-- 结果变更 -->
                     <!-- 85结果公告待提交;86结果公告待审核;87结果公告审核中;88结果公告审核不通过;89结果公告撤回;90结果待公示;91结果公示中;92结果公示结束 -->
                      <!-- 85~92  包含 85 和 92 -->
                    <nd-button authKey="result:change:submit"
                      @click.stop="resultChange(row)"
                      v-if="row.flowStatus >= 85&&row.flowStatus <= 92 &&(row.resultChangeState==0||row.resultChangeState==1) &&row.busType!=2&&row.busType!=4&&row.tradeType!=5&&row.busType!=5&&(row.flowStatus<100)"
                      type="resultChangeType1"
                    >
                    </nd-button>
                    <!-- 结果变更公告 -->
                      <!-- 项目列表 加了个标识   resultChangeFlag;   结果变更标识 0 未走结果变更 1 走结果变更 -->
                       <!-- resultChangeState  0无变更 1 变更不通过  2变更提交待审核状态 3.审核通过 -->
                    <nd-button  authKey="result:changeAn:submit"
                      @click.stop="announcementChange(row)"
                      type="resultChangeType2"
                      v-if="(row.resultChangeState==3||row.resultChangeState == 6||row.resultChangeState == 7)&&(row.flowStatus<100)"
                    >
                    结果变更公告
                    </nd-button>
                    <!-- 结果变更公告撤回 -->
                    <nd-button
                      @click.stop="finishWithdraw3(row)"
                      type="resultChangeType4"  authKey="result:changeAn:withdraw"
                      v-if="(row.resultChangeState == 4||row.resultChangeState == 5)&&(row.flowStatus<100)"
                    >
                    撤回
                    </nd-button>
                    <!-- 结果变更撤回 -->
                    <nd-button  authKey="result:change:withdraw"
                      @click.stop="finishWithdraw2(row)"
                      type="resultChangeType3"
                      v-if="(row.resultChangeState == 2||row.resultChangeState == 21)&&(row.flowStatus<100)"
                    >
                    撤回
                    </nd-button>
                  </template>
                </el-table-column>
              </nd-table>
            </template>
            <template #page>
              <nd-pagination
                v-model:current-page="proForm.pageNo"
                v-model:page-size="proForm.pageSize"
                v-model:total="page.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              >
              </nd-pagination>
            </template>
          </ndb-page-list>
        </div>

        <!-- <div class="pagination-box">
                </div> -->
        <!-- 待发布 中止 -->
        <to-be-released-dialog
          ref="toBeReleasedDialogRef"
          class="to-be-released-dialog"
          @update="getList"
        />
        <!-- 报名中 报名登记 -->
        <under-apply-dialog
          ref="underApplyDialogRef"
          class="under-apply-dialog"
          @refresh="getList"
        />
        <!-- 竞价中 交易结果登记 -->
        <under-bid-result-register
          @update="getList()"
          ref="underBidResultRegisterRef"
          class="under-bid-result-register"
        />
        <!-- 已公示 公示质疑 -->
        <published-query ref="publishedQueryRef" class="published-query" />

        <!-- 项目登记-前置表单 -->
        <proRecord
          ref="proRecordRef"
          @edit="projectEdit02"
          @registration="projectRegistration"
          @registration2="projectRegistration2"
        />
        <!-- 项目登记-登记页面 -->
        <project-registration-view
          ref="projectAdd"
          @back="back"
          v-show="show.proRegist"
          :proForm="proRegForm.proForm"
          class="project-registration-detailView"
        />
        <!-- 项目登记-编辑页面  -->
        <project-registration-editor-view
          ref="projectEditor"
          @back="back"
          v-show="show.proEdit"
          :id="proRegForm.editId"
          :proCode="proRegForm.proCode"
          class="project-registration-detailView"
        />
        <!-- 项目登记-详情页面  -->
        <project-registration-detail-view
          ref="projectResg"
          @back="back"
          v-show="show.proDetail"
          :id="proRegForm.detailId"
          :tradeMode="proRegForm.tradeMode"
          class="project-registration-detailView"
          :locationShow="true"
        />
        <project-registration-detail-view
          ref="projectResg"
          @back="back"
          v-show="show.proDetail"
          :id="proRegForm.detailId"
          :tradeMode="proRegForm.tradeMode"
          class="project-registration-detailView"
          :locationShow="true"
        />

        <!-- 编辑公告 -->
        <open-page
          v-if="jump.editAffiche"
          @showNewEdit="showNewEdit"
          @getTableData="getList"
        />
        <!-- 变更公告 -->
        <open-page2
          v-if="jump.editAffiche2"
          @showNewEdit="showNewEdit"
          @getTableData="getList"
        />
        <!-- 结果公示 -->
        <result-announcement-view
          v-if="jump.resultAnnouncementView"
          @showNewEdit="changCheck"
        />
        <!-- 合同登记 -->
        <contract-management-view
          v-if="jump.contractManagementView"
          @showNewEdit="contractShowFn"
        />
        <!-- 设置规则 -->
        <bidding-detail
          ref="biddingDetailRef"
          v-if="jump.biddingDetail"
          @back="biddingClose"
          class="bidding-detail-class"
        />
        <!-- 重发公告 前置弹窗 -->
        <again-send-dialog
          ref="againSendDialogRef"
          @showNewEdit="beforeShowNewEdit"
        />
        <!-- 重发公告 -->
        <open-page-edit
          v-if="jump.openPageEdit"
          @showNewEdit="showNewEdit"
          @getTableData="getList"
        />
        <!-- 交易准备 -->
        <detailPage v-if="isDealPre" :dataObj="dataObj" @goBack="goBack" />
        <!-- 结果变更 -->
        <open-result-page
          ref="resultRef"  class="under-apply-dialog"
          @refresh="getList"
        />
        <!-- 结果变更公告 -->
        <result-announcement-change
          ref="resultChangeRef"  class="under-apply-dialog"
          @refresh="getList"
        />
      </div>
    </div>
  </ndb-page>
</template>

<script setup>
import ndbPage from "@/components/business/ndbPage/index.vue";
import ndbPageList from "@/components/business/ndbPageList/index.vue";
import ndButton from "@/components/ndButton.vue";
import ndTag from "@/components/ndTag.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndTable from "@/components/ndTable.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndbImport from "@/components/business/ndbImport/index.vue";
// 项目登记组件
import projectRegistrationView from "../projectRegistrationView/index.vue";
import projectRegistrationEditorView from "../projectRegistrationEditorView/index.vue";
import projectRegistrationDetailView from "../projectRegistrationDetailView/components/detail.vue";
// 导入子组件
import comSearch from "./components/comSearch.vue"; //搜索 components
import toBeReleasedDialog from "./components/toBeReleasedDialog.vue"; //终止 dialog
import underApplyDialog from "./components/underApplyDialog.vue"; //报名中 报名登记
import underBidResultRegister from "./components/underBidResultRegister.vue"; //竞价中 交易结果登记
import publishedQuery from "./components/publishedQuery.vue"; //已公示 公示质疑 dialog
// 项目登记 前置 dialog
import proRecord from "./components/proRecord.vue";
// 编辑公告/跳转
import openPage from "@/views/tradingAnnouncementView/components/openPage.vue";
// 变更公告/跳转
import openPage2 from "@/views/tradingAnnouncementView/components2/openPage.vue";
// 结果公示/跳转
import resultAnnouncementView from "@/views/resultAnnouncementView/components/openPage.vue";
// 合同登记/跳转
import contractManagementView from "@/views/contractManagementView/components/openPage.vue";
// 设置规则/跳转
import biddingDetail from "@/views/biddingView/components/biddingDetail.vue";
// 重发公告/前置弹窗
import againSendDialog from "@/views/tradingAnnouncementView/components/againSendDialog.vue";
// 重发公告/跳转
import openPageEdit from "@/views/tradingAnnouncementView/components/openPageEdit.vue";

// 结果变更
import openResultPage from "@/views/transactionManageView/components/openPage.vue";
// 结果变更公告
import resultAnnouncementChange from "@/views/transactionManageView/components/openPage2.vue";

import detailPage from "./components/detailPage.vue";
 
// 导入element-plus方法
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";
// 导入vue
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  shallowRef,
  computed,
  provide,
} from "vue";
// route
import { useRouter } from "vue-router";
// 导入mock数据 utils
import { useMock, getRequestParams } from "./components/mockData.js";
const processFlag=ref('')
// mounted
onMounted(() => {
  //开发环境
  processFlag.value=process.env.NODE_ENV
  // 地址栏参数
  let data = getRequestParams();
  proForm.projectType = data.projectType || "";
  proForm.menuCode = data.menuCode || "";

  // 跳转到当前页面，tab切换
  if (tagIndex.value == "") {
    proForm.status = "";
  } else {
    proForm.status = Number(tagIndex.value + "0")
  }
  // if(data.status){
  // let tempIndex = data.status.substr(0, 1)
  // 部分展开的 高亮
  console.log(tagsData,"tagsData")
  tagsData.forEach((item) => {
    item.checked = false;
    if(item.status == tagIndex.value){
      item.checked = true;
    }
  });

  tagsDataAll.forEach((item) => {
    item.checked = false;
    if(item.status == tagIndex.value){
      item.checked = true;
    }
  });
  // 工作台跳过来的-status赋值搜索组件的状态拉下框
  if (data.status) {
    proForm.flowStatus = data.status*1
    comSearchRef.value.setData("flowStatus",data.status*1) 
  }
  getList();
  // ipConfig
  pageIpConfig.value = window.ipConfig.viewSetup.transactionManageView
});
const pageIpConfig = ref({})
// 跳转页面
const jump = reactive({
  editAffiche: false,
  editAffiche2:  false,
  resultAnnouncementView: false,
  contractManagementView: false,
  biddingDetail: false,
  openPageEdit: false,
  result: false,
});
const jumpData = reactive({
  editAfficheData: {
    data: {},
  },
  resultAnnouncementData: {
    data: {},
  },
  contractManagementData: {
    data: {},
  },
  openPageEditData: {
    data: {},
  },
});
const router_userRouter = useRouter();
const { mock, page, tagsData, tagsDrop, tagsDataAll } = useMock(); //mock
// ajax
const $axios = inject("$axios");
// ref
const ndbPageRef = shallowRef(null);
const biddingDetailRef = ref(null);
const againSendDialogRef = ref(null);
// 项目登记页面返回
function back() {
  getList();
  show.proRegist = false;
  show.proEdit = false;
  show.proDetail = false;
}
const comSearchRef = ref(null);
// init request-form
const proForm = reactive({
  projectType: "", //交易分类（1 集体资源交易； 2 集体资产交易；3 综合类交易； 4 农产品交易 ； 5 招投标项目）
  status: 10, //tag-status
  areaId: "",
  pageNo: 1,
  pageSize: 10,
});
const resData = reactive({
  table: [],
  total: 0,
  loading: false,
});
let requestTimer = ref(null);
// search moudle
const show = reactive({
  searchMore: false, //搜索更多
  proDetail: false,
  proEdit: false,
  proRegist: false,
});
const proRegForm = reactive({
  detailId: "",
  editId: "",
  proForm: {},
  proCode: "",
  tradeMode: "",
});
// button disabled flag
const btnDisabled = reactive({
  disDelete: false,
});
// tag
let tagIndex = ref("");
// let tagAllIndex = ref(1)
let tagCountFlag = ref(true);
// 全部展开的 tag
function tagAllChange(index, row) {
  tagCountFlag.value = false;
  comSearchRef.value.resetFn();
  tagIndex.value = row.status;
  tagsDataAll.forEach((item) => {
    item.checked = false;
  });
  row.checked = true;
  for (const key in proForm) {
    if(key != "projectType" && key != "menuCode"){
      proForm[key] = "";
    }
  }
  proForm.pageNo = 1;
  proForm.pageSize = 10;
  if (row.status == "") {
    proForm.status = "";
  } else {
    proForm.status = Number(row.status + "0");
  }
  // 部分展开的 高亮
  tagsData.forEach((item) => {
    item.checked = false;
    if(item.status == row.status){
      item.checked = true
    }
  });
  // 部分展开的 高亮
  getList();
}
// 部分展开的 tag
function tagChange(index, row) {
  tagCountFlag.value = false;
  comSearchRef.value.resetFn();
  tagIndex.value = row.status;
  tagsData.forEach((item) => {
    item.checked = false;
  });
  row.checked = true;
  for (const key in proForm) {
    if(key != "projectType" && key != "menuCode"){
      proForm[key] = "";
    }
  }
  proForm.pageNo = 1;
  proForm.pageSize = 10;
  if (row.status == "") {
    proForm.status = "";
  } else {
    proForm.status = Number(row.status + "0");
  }
  // 全部展开 对应高亮
  tagsDataAll.forEach((item) => {
    item.checked = false;
    if(item.status == row.status){
      item.checked = true
    }
  });
  // 全部展开 对应高亮
  getList();
}
// 点击更多
function dropItemClick(params) {
  comSearchRef.value.resetFn();
  tagIndex.value = params.status
  tagsData.forEach((item) => {
    item.checked = false;
  });
  proForm.status = Number(params.status + "0");
  // 全部展开 对应高亮
  tagsDataAll.forEach((item) => {
    item.checked = false;
    if(item.status == params.status){
      item.checked = true
    }
  });
  // 全部展开 对应高亮
  getList();
}
// btn 项目登记
const proRecordRef = ref(null);
const projectEditor = ref(null);
const projectAdd = ref(null);
function projectClick() {
  proRecordRef.value.open();
}
function projectEdit(row) {
  proRegForm.proCode = "";
  proRegForm.editId = row.id;
  show.proEdit = true;
  projectEditor.value.projectInfor.id = row.id;
  projectEditor.value.projectInfor.retryFlag = row.retryFlag; //重发标记（0不是,1是）
  projectEditor.value.projectInfor.code = "";
  projectEditor.value.projectInfor.jyfs = row.tradeType;
  projectEditor.value.formType.jypz = row.childTradeVariety;
  projectEditor.value.formType.ywlx = row.busType;
  projectEditor.value.projectInfor.configAreaId  = row.villageId;
  projectEditor.value.refeshFun();
  // router_userRouter.push({path:"../projectRegistrationEditorView",query:{id:row.id}})
}
function projectEdit2(row) {
  row.edit=true;
  router_userRouter.push({path:"../transactionView",query:{projectType:proForm.projectType,type:3,params:JSON.stringify(row)}})
}
const projectResg = ref(null);
function projectDetail(row) {
  console.log("点击详情行数据", row);
  // return console.log(row);
  if (row.tenderId) return;
  show.proDetail = true;
  proRegForm.detailId = row.id;
  proRegForm.tradeMode = row.tradeMode;
  projectResg.value.refeshFun(row.id, row.tradeMode);
  return console.log(row);
  // router_userRouter.push({path:"../projectRegistrationDetailView",query:{id:row.id}})
}
function projectEdit02(params) {
  projectEditor.value.projectInfor.id = params.valObj.id;
  console.log(params);
  projectEditor.value.projectInfor.jyfs = params.valObj.tradeType;
  projectEditor.value.formType.jypz = params.valObj.childTradeVariety;
  projectEditor.value.formType.ywlx = params.valObj.busType;
  projectEditor.value.projectInfor.code = params.proCode;
  projectEditor.value.projectInfor.configAreaId  = params.valObj.configAreaId;
  proRegForm.editId = "";
  show.proEdit = true;
  projectEditor.value.refeshFun();
}
// 重发申请
function repeatApply(row) {
  // 1项目，2标段   projectType:1  重发
  let tempParams = {};
  if (!row.tenderId) {
    //1项目
    projectEditor.value.projectInfor.projectId = row.id;
    tempParams.isProOrTender = 1;
    tempParams.id = row.id;
  } else {
    //，2标段
    projectEditor.value.projectInfor.tenderId = row.tenderId;
    tempParams.isProOrTender = 2;
    tempParams.id = row.tenderId;
  }
  beforeRepeatApply(tempParams).then((flag) => {
    if (flag) {
      projectEditor.value.projectInfor.projectType = 1;
      show.proEdit = true;
      projectEditor.value.formType.jypz = row.childTradeVariety;
      projectEditor.value.formType.ywlx = row.busType;
      projectEditor.value.refeshFun();
    }
  });
}
function beforeRepeatApply(params) {
  return new Promise((reslove, reject) => {
    $axios({
      url: "/project/retry/checkRetry",
      method: "post",
      data: {
        id: params.id,
        isProOrTender: params.isProOrTender,
      },
    }).then((res) => {
      if (res.data.code === 200) {
        reslove(true);
      } else {
        ElMessage.warning(res.data.msg);
        reslove(false);
      }
    });
  });
}
function projectRegistration(params) {
  console.log(params, "params");
  proRegForm.proForm = params;
  projectAdd.value.formType.ywlx = proRegForm.proForm.YWLXdataKey;
  projectAdd.value.formType.jypz = proRegForm.proForm.JYPZdataKey;
  projectAdd.value.formType.jyfs = proRegForm.proForm.JYFSdataKey;
  projectAdd.value.projectObj.jypz = proRegForm.proForm.JYPZdataValue;
  projectAdd.value.projectObj.jyfs = proRegForm.proForm.JYFSdataValue;
  projectAdd.value.projectObj.areaId = proRegForm.proForm.areaId;
  projectAdd.value.refeshFun();
  show.proRegist = true;
}
function projectRegistration2(params) {
  console.log(params, "params");
  params.busType = params.YWLXdataKey || '';
    params.tradeType = params.JYFSdataKey || '';
    params.transactionsValue = params.JYFSdataValue || '';
    params.tradingValue = params.JYPZdataValue || '';
    params.childTradeVariety = params.JYPZdataKey || '';
  params.edit=false;
  router_userRouter.push({path:"../transactionView",query:{projectType:proForm.projectType,type:3,params:JSON.stringify(params)}})
}
//导出
function exportExcel() {
  $axios({
    url: "/project/exportExcel",
    method: "get",
    data: proForm,
    responseType: "blob",
  }).then((res) => {
    if (!res) return;
    const blob = new Blob([res.data], {
      type: "application/vnd.ms-excel",
    }); // 构造一个blob对象来处理数据，并设置文件类型

    if (window.navigator.msSaveOrOpenBlob) {
      //兼容IE10
      navigator.msSaveBlob(blob, "交易项目汇总表.xls");
    } else {
      const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
      const a = document.createElement("a"); //创建a标签
      a.style.display = "none";
      a.href = href; // 指定下载链接
      a.setAttribute("download", "交易项目汇总表.xls");
      //a.download = this.filename; //指定下载文件名
      a.click(); //触发下载
      URL.revokeObjectURL(a.href); //释放URL对象
    }
  });
}
// 子 search
function searchFn(params) {
  tagIndex.value = 0;
  proForm.status = "";
  tagsData.forEach((item) => {
    item.checked = false;
  });
  tagsData[0].checked = true;
  tagsDataAll.forEach((item) => {
    item.checked = false;
  });
  tagsDataAll[0].checked = true;
  for (const key in params) {
    proForm[key] = params[key];
  }
  getList();
}
// 分页调用搜索（不重置tabs）
function paginSearch(params) {
  for (const key in params) {
    proForm[key] = params[key];
  }
  getList();
}
// table init-data
function getList() {
  resData.loading = true;
  if (requestTimer.value) {
    clearTimeout(requestTimer.value);
  }
  requestTimer.value = setTimeout(() => {
    $axios({
      method: "get",
      data: {
        ...proForm,
      },
      url: "/project/listPage",
    })
      .then((res) => {
        if (!res) return;
        tagCountFlag.value = true;
        let obj = res.data.data;
        resData.table = obj.records;
        resData.total = obj.total;
        resData.table.forEach((item01, index01) => {
          // 序号问题
          if (!item01.tenderId) {
            item01.parentIndex =
              (proForm.pageNo - 1) * proForm.pageSize + index01 + 1;
          }
          //
          if (!item01.tradeTendersList) {
            item01.flowStatuses = item01.flowStatus;
            item01.tenderStatus = item01.status;
          }
          // 没有标段
          if (!item01.tradeTendersList) return;
          // 项目状态为公告待提交，【编辑公告】同时显示在项目行和标段行
          if (item01.flowStatus === 31 && item01.annoFlag) {
            item01.projectAndTender = true;
            item01.tradeTendersList.forEach((item02) => {
              if (item02.flowStatus != 101) {
                item02.projectAndTender = true;
              }
            });
          }
          // 单个标段不展开，多个标段展开
          if (item01.tradeTendersList.length !== 0) {
            if (item01.tenderCount <= 1) {
              item01.hasChildren = true;
            } else {
              item01.hasChildren = false;
            }
            // 标段赋 flowStatuses，标段获取 项目proId
            item01.tradeTendersList.forEach((item02) => {
              item02.id = item02.tenderId; //标段id  row-key
              item02.flowStatuses = item02.flowStatus; //判断按钮
              item02.proId = item01.id; //项目proId
            });
          }
          // 项目行赋予标段状态
          if (item01.tradeTendersList.length > 0 && item01.tenderCount > 1) {
            item01.flowStatuses = item01.flowStatus;
            item01.tenderStatus = item01.tradeTendersList[0].status;
          }
          item01.tradeTendersList.forEach((item02) => {
            item02.flowStatuses = item02.flowStatus;
            item02.tenderStatus = item02.status;
            // 标段 项目编号 项目名称更改
            item02.code = item02.name;
            item02.name = item01.name;
          });
          // 单个标段，标段符合则符合
          if (item01.tradeTendersList.length <= 1) {
            //单个标段，项目添加flag
            if (
              item01.tradeTendersList[0] &&
              item01.tradeTendersList[0].annoFlag &&
              item01.tenderCount <= 1
            ) {
              item01.singleFlag = true;
            }
            if (item01.tradeTendersList[0] && item01.tenderCount <= 1) {
              item01.singleFlag02 = true;
              item01.flowStatuses = item01.tradeTendersList[0].flowStatus;
              item01.resultChangeState = item01.tradeTendersList[0].resultChangeState;
            }
          }
          // 判断是否多个标段
          // 单个标段，项目添加flag
          if (item01.tradeTendersList.length <= 1 && item01.tenderCount <= 1) {
            item01.tenderFlag = true;
            // 合同登记（新增流标状态）
            if (
              item01.tradeTendersList.length > 0 &&
              item01.tradeTendersList[0].busType != 4 &&
              item01.tradeTendersList[0].tradeFlag != 2
            ) {
              item01.contractTenderFlag = true;
            }
            // 部分竞价方式，不展示设置规则按钮
            let tempBidType = item01.tradeTendersList[0];
            if (!tempBidType) return;
            if (
              tempBidType.bidType != 6 &&
              tempBidType.bidType != 7 &&
              tempBidType.bidType != 8
            ) {
              item01.bidTypeFlag = true;
            }
          }
          //多个标段，标的添加flag
          else {
            // 标段行
            item01.tradeTendersList.forEach((item02) => {
              item02.tenderFlag = true;
              // 合同登记（新增流标状态）
              if (item02.busType != 4 && item02.tradeFlag != 2) {
                item02.contractTenderFlag = true;
              }
              if (
                item02.bidType != 6 &&
                item02.bidType != 7 &&
                item02.bidType != 8
              ) {
                item02.bidTypeFlag = true;
              }
            });
            // 项目行
            // 合同登记，多个标段显示在标段行
            // if(item01.busType != 4 && item01.tradeFlag != 2){
            //     item01.contractTenderFlag = true
            // }
          }
        });
        proForm.pageNo = res.data.data.current;
        page.total = res.data.data.total;
        console.log(resData.table, "resData.table");
      })
      .finally(() => {
        btnDisabled.disDelete = false;
        resData.loading = false;
      });
  }, 700);
}
function handleCurrentChange(val) {
  proForm.pageNo = val;
  // resData.table = []
  comSearchRef.value.paginSearch();
  getList();
}
function handleSizeChange(val) {
  proForm.pageSize = val;
  // resData.table = []
  comSearchRef.value.paginSearch();
  getList();
}

// 已登记 删除
const deleteFn = (row) => {
  // return console.log(row)
  ElMessageBox.confirm(`确定要删除${row.name}吗？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      btnDisabled.disDelete = true;
      $axios({
        url: `/project/${row.id}`,
        method: "delete",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功",
          });
          getList();
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {
      //   ElMessage({
      //     type: 'info',
      //     message: 'Delete canceled',
      //   })
    });
};
// 审核中的撤回
function withdraw(row) {
  ElMessageBox.confirm(`确定要撤回${row.name}吗？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      $axios({
        url: `/project/withdrawProById?proId=${row.id}`,
        method: "post",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            type: "success",
            message: "撤回成功",
          });
          getList();
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {});
}
// 竞价中的撤回（结果待审核 结果审核中）
function withdrawByResultId(row) {
  let tempTenderId = row.tenderId
    ? row.tenderId
    : row.tradeTendersList[0].tenderId;
  ElMessageBox.confirm(`确定要撤回${row.name}吗？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      $axios({
        url: `/result/withdrawByResultId?tenderId=${tempTenderId}`,
        method: "POST",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success("撤回成功");
          getList();
        } else {
          ElMessage.warning(res.data.msg);
        }
      });
    })
    .catch(() => {});
}

// 公告中的撤回
function annoWithdraw(row) {
  ElMessageBox.confirm(`确定要撤回${row.name}吗？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      let type = "";
      if (row.tenderId) {
        if (row.flowStatus == 86 || row.flowStatus == 87) {
          // 结果撤回
          type = "resultRevoke";
        } else if (row.flowStatus == 32 || row.flowStatus == 33) {
          // 交易公告撤回
          type = "tradeRevoke";
        }
      } else {
        if (!row.tradeTendersList) return;
        if (row.tradeTendersList.length > 0) {
          if (
            row.tradeTendersList[0].flowStatus == 86 ||
            row.tradeTendersList[0].flowStatus == 87
          ) {
            type = "resultRevoke";
          } else if (
            row.tradeTendersList[0].flowStatus == 32 ||
            row.tradeTendersList[0].flowStatus == 33
          ) {
            type = "tradeRevoke";
          }
        }
      }
      $axios({
        url: `/announcement/${type}/${row.annoId}`,
        method: "post",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            type: "success",
            message: "撤回成功",
          });
          getList();
        } else if (res.data.code !== 403) {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {});
}
// 已结束的撤回
function finishWithdraw(row) {
  ElMessageBox.confirm(`确定要撤回${row.name}吗？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      $axios({
        url: `/announcement/resultRevoke/${row.pubId}`,
        method: "POST",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            type: "success",
            message: "撤回成功",
          });
          getList();
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {});
}
function finishWithdraw2(row) {
  let tempTenderId = row.tenderId
    ? row.tenderId
    : row.tradeTendersList[0].tenderId;
  ElMessageBox.confirm(`确认撤回结果变更申请？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      $axios({
        url: `/tradeResultChange/withdrawByResultId?tenderId=`+tempTenderId,
        // url: `/tradeResultChange/withdrawByResultId/${tempTenderId}`,
        method: "POST",
      }).then((res) => {
        if (res.data.code === 200) {
           ElMessage({
            type: "success",
            message: "撤回成功",
          });
          getList();
          // ElMessage({
          //   type: "success",
          //   message: "撤回成功",
          // });
          getList();
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {});
}
function finishWithdraw3(row) {
  let tempTenderId = row.tenderId
    ? row.tenderId
    : row.tradeTendersList[0].tenderId;
  ElMessageBox.confirm(`确认撤回结果变更公告？`, "提示", {
    //   confirmButtonText: 'OK',
    //   cancelButtonText: 'Cancel',
    type: "warning",
  })
    .then(() => {
      $axios({
        url: `/resultChangeAn/withdrawByResultId?tenderId=`+tempTenderId,
        // url: `/tradeResultChange/withdrawByResultId/${tempTenderId}`,
        method: "POST",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            type: "success",
            message: "撤回成功",
          });
          getList();
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
    })
    .catch(() => {});
}
// 待发布 中止
const toBeReleasedDialogRef = shallowRef(null);
function toBeReleasedClick(row) {
  let data = {};
  let requestForm = {
    projectId: "",
    tenderId: "",
  };
  if (!row.tenderId) {
    //项目
    data.dateId = row.id;
    data.projectId = row.id;
    data.type = 1;
    // requestForm = {
    //     projectId: data.projectId
    // }
  } else if (row.tenderId) {
    //标段
    data.dateId = row.tenderId;
    data.projectId = row.proId;
    data.type = 2;
    // requestForm = {
    //     tenderId: row.tenderId
    // }
  }
  requestForm.projectId = !row.tenderId ? row.id : "";
  requestForm.tenderId = row.tenderId ? row.tenderId : "";
  $axios({
    url: `/announcement/abort`,
    method: "get",
    data: requestForm,
  }).then((res) => {
    if (res.data.code !== 200) return;
    data.operate = res.data.data.operate;
    data.reason = res.data.data.reason ? res.data.data.reason : "";
    toBeReleasedDialogRef.value.open(data);
  });
}
// 编辑公告跳转
function announcementEdit(row, flag = true) {
  // 项目状态为公告待提交，【编辑公告】同时显示在项目行和标段行
  let data = {};
  if (!row.tenderId) {
    data.projectId = row.id;
    data.annType = 1; //项目
    // 跳转到 编辑公告 所需判断
    if(row.tradeTendersList && row.tradeTendersList.length <= 1 && row.tenderCount <= 1){
      data.annProFlag = true
    }
  } else {
    data.projectId = row.proId;
    data.annType = 2; //标段
    data.tenderId = row.tenderId;
  }
  data.annoFlag = row.annoFlag
  data.dataKey = row.childTradeVariety;
  data.villageId = row.villageId
    ? row.villageId
    : row.townId
    ? row.townId
    : row.countyId;
  data.id = row.annoId ? row.annoId : "";
  data.status = 0;
  data.child = row.tradeTendersList || [];
  jumpData.editAfficheData.data = data;
  // return console.log(jumpData.editAfficheData,"jumpData.editAfficheData.data")
  if(flag) {
    jump.editAffiche = true;
    ndbPageRef.value.addBreadcrumb("编辑公告");
  }else {
    jump.editAffiche2 = true;
    ndbPageRef.value.addBreadcrumb("公告变更");
  }
  // router_userRouter.push({
  //     name:"tradingAnnouncementView",
  //     query: data
  // })
}
provide("rowObject01", jumpData.editAfficheData);
function showNewEdit() {
  jump.editAffiche = false;
  jump.editAffiche2 = false;
  jump.openPageEdit = false;
  ndbPageRef.value.resetBreadcrumb();
}
function beforeShowNewEdit(type, row, againSendReason) {
  jump.openPageEdit = true;
  jumpData.openPageEditData.againSendReason = againSendReason;
  ndbPageRef.value.addBreadcrumb("重发公告");
}
// 待发布 重发公告
function openRetransmissionInformation(row) {
  let data = {};
  if (!row.tenderId) {
    data.projectId = row.id;
    data.annType = 1; //项目
  } else {
    data.projectId = row.proId;
    data.annType = 2; //标段
  }
  data.status = 5;
  data.id = row.annoId ? row.annoId : ""; //公告id
  data.dataKey = row.childTradeVariety;
  data.villageId = row.villageId
    ? row.villageId
    : row.townId
    ? row.townId
    : row.countyId;
  jumpData.openPageEditData.data = data;
  // jump.openPageEdit = true
  // ndbPageRef.value.addBreadcrumb('重发公告')

  data.tenderId = row.tenderId ? row.tenderId : "";
  $axios({
    url: "/announcement/repeatDetail",
    method: 'get',
    data: {
      id: row.annoId,
      projectId: data.projectId,
      annType: data.annType,
    }
  }).then(res=>{
    if(res.data.code===200){
      againSendDialogRef.value.open(3, data);
    }
    else{
      ElMessage({
        type: "warning",
        message: res.data.msg,
      });
    }
  })

  // return console.log(jumpData.openPageEditData,"jumpData.openPageEditData.data")
}
provide("rowObject03", jumpData.openPageEditData);
// 报名中 报名登记
const underApplyDialogRef = ref(null);
function underApplyClick(row) {
  let data = {
    projectId: "",
    tenderId: "",
  };
  if (!row.tenderId) {
    data.projectId = row.id;
    data.tenderId = row.tradeTendersList[0].tenderId;
  } else {
    data.projectId = row.proId;
    data.tenderId = row.tenderId;
  }
  underApplyDialogRef.value.open(data);
}
// 竞价中 交易结果登记
const underBidResultRegisterRef = shallowRef(null);
function underBidResultRegisterClick(row) {
  let tempTenderId = row.tenderId
    ? row.tenderId
    : row.tradeTendersList[0].tenderId;
  $axios({
    // url: `/result/getResultInfo?tenderId=686d4a43a1a94598b6ef2e94190c5aa2`,
    url: `/result/check/${tempTenderId}`,
    method: "get",
  }).then((res) => {
    if (res.data.code !== 200) {
      ElMessage({
        type: "warning",
        message: res.data.msg,
      });
    } else {
      let data = {
        tenderId: "",
        flowStatus: "",
        childTradeVariety: "",
      };
      if (!row.tenderId) {
        data.tenderId = row.tradeTendersList[0].tenderId;
        data.flowStatus = row.tradeTendersList[0].flowStatus;
        data.childTradeVariety = row.tradeTendersList[0].childTradeVariety;
      } else {
        data.tenderId = row.tenderId;
        data.flowStatus = row.flowStatus;
        data.childTradeVariety = row.childTradeVariety;
      }
      if (row.villageId) {
        data.villageId = row.villageId;
      } else if (row.townId) {
        data.townId = row.townId;
      } else {
        data.countyId = row.countyId;
      }
      underBidResultRegisterRef.value.open(data);
    }
  });
}
// 已结束 结果公示跳转
function finishedResultPublicityClick(row) {
  let data = {};
  data.tenderId = row.tenderId ? row.tenderId : "";
  if (!row.tenderId) {
    data.projectId = row.id;
    data.annType = 1;
  } else {
    data.projectId = row.proId;
    data.annType = 2;
  }
  data.projectCode = row.code;
  data.status = 0;
  data.dataKey = row.childTradeVariety;
  data.villageId = row.villageId
    ? row.villageId
    : row.townId
    ? row.townId
    : row.countyId;
  // data.id = row.annoId ? row.annoId : ''
  $axios({
    method: "get",
    url: `/announcement/resultDetail`, //根据公告id获取，公示id
    data: {
      id: row.pubId, //公告id
      projectId: data.projectId, //项目id
      tenderId: data.tenderId,
      checkFlag: 1,
    },
  }).then((res) => {
    if (res.data.code !== 200) {
      return ElMessage({
        type: "warning",
        message: res.data.msg,
      });
    }
    data.id = res.data.data.id;
    jumpData.resultAnnouncementData.data = data;
    jump.resultAnnouncementView = true;
    ndbPageRef.value.addBreadcrumb("结果公示");
  });
  // return
  // router_userRouter.push({
  //     name:"resultAnnouncementView",
  //     query: data
  // })
}
provide("rowObject02", jumpData.resultAnnouncementData);
function changCheck() {
  getList();
  jump.resultAnnouncementView = false;
  ndbPageRef.value.resetBreadcrumb();
}
// 已公示
// 公示质疑
const publishedQueryRef = shallowRef(null);
function publishedQueryClick(row) {
  let data = {};
  if (!row.tenderId) {
    data.projectId = row.id;
  } else {
    data.projectId = row.proId;
  }
  data.announcementId = row.pubId;
  publishedQueryRef.value.open(data);
}
const resultRef = ref(null);
function resultChange(row){
  let tenderId;
  if (!row.tenderId) {
    tenderId =row.tradeTendersList[0].tenderId;
  } else {
    tenderId = row.tenderId;
  }
   $axios({
        url: `/tradeResultChange/changeVerification/${tenderId}`,
        method: "get",
      }).then((res) => {
        if (res.data.code === 200) {
             resultRef.value.open(row);
        } else {
          ElMessage({
            type: "warning",
            message: res.data.msg,
          });
        }
      });
}
const resultChangeRef = ref(null);
function announcementChange(row){
    resultChangeRef.value.open(row);
}
// 合同登记/跳转
function contractRegist(row) {
  let id = "";
  let data = {};
  // 登记
  data.flag = 1;
  // 文书参数
  if (!row.tenderId) {
    data.projectId = row.id;
  } else {
    data.projectId = row.proId;
  }
  data.projectType = row.childTradeVariety;
  data.villageId = row.villageId
    ? row.villageId
    : row.townId
    ? row.townId
    : row.countyId;
  // tenderId请求合同ID
  if (!row.tenderId) {
    if (!row.tradeTendersList) return;
    id = row.tradeTendersList[0].tenderId;
  } else {
    id = row.tenderId;
  }
  $axios({
    method: "get",
    url: `/contract/getContractIdByTenderId/${id}`,
    // url:`/contract/getContractIdByTenderId/adff055cd5aa4dc4aa6326a22763a519`
  }).then((res) => {
    if (res.data.code === 200) {
      data.id = res.data.data;
      jumpData.contractManagementData.data = data;
      jump.contractManagementView = true;
      ndbPageRef.value.addBreadcrumb("合同登记");
    } else {
      ElMessage({
        type: "warning",
        message: res.data.msg,
      });
    }
  });

  // data.id = "1720024579692896258"
  // jumpData.contractManagementData.data = data
  // jump.contractManagementView = true
  // ndbPageRef.value.addBreadcrumb("合同登记")
  // router_userRouter.push({
  //     name:"contractManagementView",
  //     query: data
  // })
}
provide("rowObject", jumpData.contractManagementData);
function contractShowFn() {
  getList();
  jump.contractManagementView = false;
  ndbPageRef.value.resetBreadcrumb();
}
// 设置规则/跳转
function biddingFn(row) {
  let newTenderId = row.tenderId
    ? row.tenderId
    : row.tradeTendersList[0].tenderId;
  $axios({
    method: "post",
    url: `/manageRuleInfo/checkBidTime?tenderId=${newTenderId}`,
  }).then((res) => {
    if (res.data.code === 200) {
      let data = {
        bidType: "",
        flowStatus: "",
        projectId: "",
        tendersId: "",
      };
      if (!row.tenderId) {
        data.bidType = row.tradeTendersList
          ? row.tradeTendersList[0].bidType
          : row.bidType;
        data.flowStatus = row.flowStatuses;
        data.projectId = row.id;
        data.tendersId = row.tradeTendersList[0].tenderId;
      } else {
        // debugger
        data.bidType = row.bidType;
        data.flowStatus = row.flowStatus;
        data.projectId = row.proId;
        data.tendersId = row.tenderId;
      }
      jump.biddingDetail = true;
      setTimeout(() => {
        // return console.log(biddingDetailRef._value,"biddingDetailRef.value")
        biddingDetailRef.value.refeshFun(data);
      }, 500);
      ndbPageRef.value.addBreadcrumb("规则设置");
    } else {
      ElMessage({
        type: "warning",
        message: res.data.msg,
      });
    }
  });
}
function biddingClose() {
  getList();
  jump.biddingDetail = false;
  ndbPageRef.value.resetBreadcrumb();
}

const isDealPre = ref(false);
const dataObj = ref({});
// 交易准备
function goDealPrepare(row) {
  console.log(row, "当前行数据");
  isDealPre.value = true;
  let obj = {};
  if (row.tenderId) {
    obj = {
      tenderId: row.tenderId,
      proId: row.proId,
      tradeMode: row.tradeMode,
      busType: row.busType,
      tenderCode: row.tenderCode,
      varietyKey: row.childTradeVariety,
    };
  } else {
    obj = {
      tenderId: row.tradeTendersList[0].tenderId,
      proId: row.id,
      tradeMode: row.tradeMode,
      busType: row.busType,
      tenderCode: row.tradeTendersList[0].tenderCode,
      varietyKey: row.childTradeVariety,
    };
  }

  dataObj.value = obj;
}

const dealPrepareBtn = computed(() => (row) => {
  return (
    (row.tenderId &&
      (row.flowStatus === 51 ||
        row.flowStatus === 52 ||
        row.flowStatus === 53 ||
        row.flowStatus === 61 ||
        row.flowStatus === 62 ||
        row.flowStatus === 63 ||
        row.flowStatus === 71 ||
        row.flowStatus === 75 ||
        row.flowStatus === 81 ||
        row.flowStatus === 82 ||
        row.flowStatus === 84 ||
        row.flowStatus === 85 ||
        row.flowStatus === 83)) ||
    (row.tradeTendersList && row.tradeTendersList.length == 1 &&
      (row.tradeTendersList[0].flowStatus === 51 ||
        row.tradeTendersList[0].flowStatus === 52 ||
        row.tradeTendersList[0].flowStatus === 53 ||
        row.tradeTendersList[0].flowStatus === 61 ||
        row.tradeTendersList[0].flowStatus === 62 ||
        row.tradeTendersList[0].flowStatus === 63 ||
        row.tradeTendersList[0].flowStatus === 71 ||
        row.tradeTendersList[0].flowStatus === 75 ||
        row.tradeTendersList[0].flowStatus === 81 ||
        row.tradeTendersList[0].flowStatus === 82 ||
        row.tradeTendersList[0].flowStatus === 84 ||
        row.tradeTendersList[0].flowStatus === 85 ||
        row.tradeTendersList[0].flowStatus === 83))
  );
});

const goBack = () => {
  isDealPre.value = false;
  getList();
};
</script>

<style lang="scss" scoped>
.point {
  cursor: pointer;
}
.main-box {
  position: relative;
  height: 100%;
  background: #fff;
  .second-box {
    padding: 15px;
    height: 100%;
    .search-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      :deep(.el-badge__content.is-fixed) {
        right: calc(21px + var(--el-badge-size) / 2);
      }
      .table-box {
        flex: 1;
        overflow-y: auto;
        height: 100%;
      }
      .dropdown-menu {
        :deep(.el-check-tag) {
          padding: 5px 15px;
        }
      }
      :deep(.status-circle) {
        display: flex;
        // justify-content: center;
        align-items: center;
        span {
          width: 8px;
          height: 8px;
          border-radius: 100px;
          background: red;
          margin-right: 5px;
        }
      }
    }
    .under-apply-dialog,
        .to-be-released-dialog,
        .under-bid-register,
        .under-bid-result-register,
        .under-apply-people,
        .finished-result-publicity,
        .bidding-detail-class,

        // 项目登记
        .project-registration-detailView,
        .published-query {
          width: 100%;
          height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 3;
    }
  }
}

@media screen and (max-width: 1536px) {
  .small-view {
    display: inline-block;
  }
  .big-view {
    display: none;
  }
}
@media screen and (min-width: 1536px) {
  .small-view {
    display: none;
  }
  .big-view {
    display: inline-block;
  }
}
</style>