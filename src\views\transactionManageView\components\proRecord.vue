<template>
    <!-- 项目登记前置 -->
    <ndDialog ref="dialog" height="210px" width="800px" align-center title="项目登记">
        <div class="tab-class">
            <el-tabs type="border-card" v-model="tabIndex">
                <!-- <el-tab-pane label="引用表单" :class="{ active: tabIndex === '0'&&flag.JYFSShow }"> -->
                <el-tab-pane label="新增项目" class="active">
                    <table v-if="tabIndex === '0'">

                        <tr v-if="treeForm.deptType == 11">
                            <td class="title">项目所属地区</td>
                            <td>
                                <nd-tree-select v-model="proForm.areaId" 
                                lazy :load="treeLoad" :props="defaultProps" node-key="id" 
                                accordion
                                style="width: 100%;"
                                teleported/>
                                 <!-- :default-expanded-keys="[treeForm.defaultExpandedKeys]" -->
                            </td>
                        </tr>

                        <tr>
                            <td class="title">交易品种</td>
                            <td>
                                <el-cascader 
                                v-model="proKey" 
                                :options="response.JYPZList" 
                                :props="JYPZProps" 
                                style="width: 100%;"
                                @change="cascaderChange"
                                ref="ndCascaderRef"
                                placeholder="请选择交易品种"
                                clearable></el-cascader>
                            </td>
                        </tr>
                        <tr v-if="response.YWLXList.length > 0">
                            <td class="title">业务类型</td>
                            <td>
                                <nd-radio-group v-model="proForm.YWLXdataKey">
                                    <nd-radio :label="item.dataKey" size="large" v-for="(item,index) in response.YWLXList" :key="index" @change="YWLXChange">{{item.dataValue}}</nd-radio>
                                </nd-radio-group>
                            </td>
                        </tr>
                        <tr v-if="flag.JYFSShow">
                            <td class="title">交易方式</td>
                            <td>
                                <nd-select v-model="proForm.JYFSdataKey" style="width: 100%;" placeholder="请选择交易方式" @change="changeJyfs" :teleported="true">
                                    <el-option v-for="item in response.JYFSList" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" :disabled="!item.selectedFlag" />
                                </nd-select>
                            </td>
                        </tr>
                        <tr v-if="proForm.YWLXdataKey == 3" style="color: #F56C6C;height: initial;">
                            <td></td>
                            <td>专场项目指针对项目统一报名，可参与项目中任意标段的竞价</td>
                        </tr>
                    </table>
                </el-tab-pane>
                <el-tab-pane label="复制项目">
                    <table>
                        <tr>
                            <td class="title">项目编号</td>
                            <td>
                                <el-input width="100%" v-model.trim="proCode" placeholder="请输入">
                                    <template #suffix>
                                        <el-button link icon="Search" @click="getByCode" />
                                    </template>
                                </el-input>
                            </td>
                        </tr>
                    </table>
                    <div v-if="flag.proNameShowFlag" :style="{paddingLeft:'65px',paddingTop:'13px',color: flag.proNameColorFlag ? '#F56C6C' : '#67C23A',}">
                        {{ flag.proNameColorFlag ? proName : `已为您查找到项目：${proName}` }} 
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer>
            <ndButton @click="toProRecord" :disabled="disabledBtn" :loading="disabledBtn" :icon="Check" type="primary" color="#0b8df1">确定</ndButton>
            <ndButton @click="toProRecord2" :disabled="disabledBtn" :loading="disabledBtn" :icon="Check" type="primary" color="#0b8df1">确定2</ndButton>
        </template>
    </ndDialog>
</template>

<script setup>
import ndDialog from '@/components/ndDialog.vue';
import ndSelect from '@/components/ndSelect.vue';
import ndRadioGroup from '@/components/ndRadioGroup.vue';
import ndRadio from '@/components/ndRadio.vue';
import ndButton from '@/components/ndButton.vue';
import ndTreeSelect from "@/components/ndTreeSelect.vue";
// utils
import { getRequestParams } from "./mockData.js"
// element
import { ElMessage,ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
// route
import {useRouter} from "vue-router"
// vue
import {onMounted, ref, inject, reactive, watchEffect,} from "vue"
const router_userRouter = useRouter()
// axios
const $axios = inject("$axios")
// emit
const emit = defineEmits(["edit","registration","registration2"])

const processFlag=ref('')

// mounted
onMounted(()=>{
    //开发环境
  processFlag.value=process.env.NODE_ENV
})
const JYPZProps = {     //cascsder-prop
    label:"dataValue",
    value:"id",
    // value:"dataKey",
    children:"childList",
    emitPath:false,
    // checkStrictly:true,
    expandTrigger:"hover",
}
const proForm = reactive({  //引用表单-request-form
    areaId:"",
    JYPZdataKey:"",
    YWLXdataKey:"",
    JYFSdataKey:"",
    JYPZdataValue:"",
    JYFSdataValue:"",
})
const response = reactive({ //引用表单-response
    JYPZList:[],
    YWLXList:[],
    JYFSList:[],
})
const flag = reactive({
    proNameShowFlag: false,
    proNameColorFlag: false,
    JYFSShow: false,
})
let tabIndex = ref('0')
let proCode = ref("")   // 已找到项目code
let proName = ref("")   // 已找到项目名 
let proKey = ref("")
let disabledBtn = ref(false)    //按钮防重

const defaultProps = {
    // children: "children",
    label: "name",
    value: "id",
    isLeaf: "isLeaf"
}

const treeForm = reactive({
    deptType:"",    // 等于11  当前用户是否为中介用户  
    areaId:"",
    defaultExpandedKeys: null,
})
const ndCascaderRef = ref(null) //交易品种 级联实例
// dialog
const dialog = ref(null)
function open() {
    proForm.JYPZdataKey = "",
    proForm.YWLXdataKey = "",
    proForm.JYFSdataKey = "",
    proForm.JYPZdataValue = "",
    proForm.JYFSdataValue = "",
    proKey.value = ""
    proCode.value = ""
    response.YWLXList = []
    response.JYFSList = []
    tabIndex.value = '0'
    proName.value = ""
    flag.proNameShowFlag = false
    flag.JYFSShow = false
    disabledBtn.value = false
    dialog.value.open()
    proForm.areaId = ""
    treeForm.areaId = ""
    treeForm.deptType = localStorage.getItem("deptType")
    // if(treeForm.deptType != 11){
        init()
    // }
    console.log(treeForm.deptType,"treeForm.deptType")
}
function close(params) {
    dialog.value.close()
}
function changeJyfs(e){
    response.JYFSList.forEach(item=>{
        if(e==item.dataKey){
            proForm.JYFSdataValue = item.dataValue
        }
    })
}
// init
function init(value) {
    let projectType = getRequestParams().projectType ? getRequestParams().projectType : ""
    $axios({    //交易品种
        method:"get",
        data:{},
        // url:`/project/getVariety?projectType=${projectType}&areaId=${value || ""}`
        url:`/project/getVariety?projectType=${projectType}`
    }).then(res=>{
        response.JYPZList = res.data.data || []
        if(res.data.code!=200) ElMessage.warning(res.data.msg)
    }) 
}

// init tree
async function treeLoad(node, resolve) {
    console.log(node,"node")
  if (node.level === 0) {

  } else {
    treeForm.areaId = node.data.id;
  }
  return resolve(await getAreaTree());
}
function getAreaTree(params) {
  let p = new Promise((resolve, reject) => {
    $axios({
      method: "post",
    //   data: {
    //     areaId: treeForm.areaId,
    //     // minLevel: 5,
    //     // useToken: 1,
    //   },
      url: `/tradeIntermediaryOrgan/getAreaTree?areaId=${treeForm.areaId}`,
    }).then((res) => {
      if (res.data.code !== 200) return;
      // data.tree =
      if (!treeForm.areaId) {
        treeForm.defaultExpandedKeys = res.data.data[0].id;
      }
      let data = []
        if(Array.isArray(res.data.data) && res.data.data.length>0){
          data = res.data.data.map(item=>{
            if(item.level != 5) item.disabled = true
            return item
          })
        }
        resolve(data);
    });
  });
  return p;
}

// watchEffect(()=>{
//     if(proForm.areaId){
//         init(proForm.areaId)
//         proKey.value = ""
//         proForm.JYPZdataKey = ""
//         proForm.JYPZdataValue = ""
//     }
//     else{
//         response.JYPZList = []
//     }
// })

function cascaderChange() {
    proForm.YWLXdataKey = ""
    response.JYFSList = []
    proForm.JYFSdataKey = ""
    flag.JYFSShow = false
    $axios({    //业务类型
        method:"get",
        url:`/project/getBusType?childTradeVarietyId=${proKey.value}`
        // url:`/project/getBusType?childTradeVarietyId=${proKey.value}&areaId=${proForm.areaId}`
    }).then(res=>{
        response.YWLXList = res.data.data || []
        if(proKey.value){
            proForm.JYPZdataKey = ndCascaderRef.value.getCheckedNodes()[0].data.dataKey
            proForm.JYPZdataValue = ndCascaderRef.value.getCheckedNodes()[0].data.dataValue
        }
    })
}
function YWLXChange(params) {
    //交易方式
    proForm.JYFSdataKey = ""
    $axios({    
        method:"get",
        data:{
            busType: proForm.YWLXdataKey,
            childTradeVariety: proForm.JYPZdataKey,
            areaId: proForm.areaId
        },
        url:`/project/getFormTradeType`
    }).then(res=>{
        if(res.data.code !== 200) {
            response.JYFSList = []
            proForm.JYFSdataKey = ""
            // ElMessage.warning(res.data.msg)
        }
        response.JYFSList = res.data.data || []
        flag.JYFSShow = res.data.data.length > 0 ? true : false
    }).catch(()=>{
        response.JYFSList = []
        flag.JYFSShow = false
    })
    
}
// to 项目登记（引用表单）
function toProRecord() {  
      ElMessageBox.alert('请去新入口测试', { confirmButtonText: '知道了', center: true }).then(() => {
             if(tabIndex.value === '0'){
        // 中介用户
        if(treeForm.deptType == 11 && !proForm.areaId) return ElMessage({
            message: '请选择项目所属地区！',
            type: 'warning',
        })
        if(!proForm.JYPZdataKey) return ElMessage({
            message: '请选择交易品种！',
            type: 'warning',
        })
        if(!proForm.YWLXdataKey) return ElMessage({
            message: '请选择业务类型！',
            type: 'warning',
        })
        if(response.JYFSList.length > 0){
            if(!proForm.JYFSdataKey) return ElMessage.warning("请选择交易方式！")
        }
        disabledBtn.value = true
        $axios({    //业务类型
            url:`/project/initProFormTemplateV2`,
            method:"post",
            data:{
                unitId: proForm.areaId,
                busType: proForm.YWLXdataKey,
                childTradeVariety: proForm.JYPZdataKey,
                tradeType: proForm.JYFSdataKey,
            }
        })
        .then(res=>{
            if(res.data.code===200){
                close()
                // router_userRouter.push({path:"../../projectRegistrationView",query:proForm})
                emit("registration", proForm)
            }else{
                ElMessage({
                    message: res.data.msg,
                    type: 'warning',
                })
            }
        }).finally(()=>{
            disabledBtn.value = false
        })
    }else{
        if(!proCode.value){
            return ElMessage({
                message: '请输入项目编号',
                type: 'warning',
            })
        }
        disabledBtn.value = true
        if(!proName.value){
            // 获取项目信息 调整 项目登记
            getByCode().then(res=>{
                if(res){
                    beforeToProRecord()
                }
            })
        }else{
            beforeToProRecord()
        }
    }  
            })
    
}
// to 项目登记（引用表单）
function toProRecord2() {  
    if(tabIndex.value === '0'){
        // 中介用户
        if(treeForm.deptType == 11 && !proForm.areaId) return ElMessage({
            message: '请选择项目所属地区！',
            type: 'warning',
        })
        if(!proForm.JYPZdataKey) return ElMessage({
            message: '请选择交易品种！',
            type: 'warning',
        })
        if(!proForm.YWLXdataKey) return ElMessage({
            message: '请选择业务类型！',
            type: 'warning',
        })
        if(response.JYFSList.length > 0){
            if(!proForm.JYFSdataKey) return ElMessage.warning("请选择交易方式！")
        }
        disabledBtn.value = true
        $axios({    //业务类型
            url:`/project/initProFormTemplateV2`,
            method:"post",
            data:{
                unitId: proForm.areaId,
                busType: proForm.YWLXdataKey,
                childTradeVariety: proForm.JYPZdataKey,
                tradeType: proForm.JYFSdataKey,
            }
        })
        .then(res=>{
            if(res.data.code===200){
                close()
                // router_userRouter.push({path:"../../projectRegistrationView",query:proForm})
                emit("registration2", proForm)
            }else{
                ElMessage({
                    message: res.data.msg,
                    type: 'warning',
                })
            }
        }).finally(()=>{
            disabledBtn.value = false
        })
    }else{
        if(!proCode.value){
            return ElMessage({
                message: '请输入项目编号',
                type: 'warning',
            })
        }
        disabledBtn.value = true
        if(!proName.value){
            // 获取项目信息 调整 项目登记
            getByCode().then(res=>{
                if(res){
                    beforeToProRecord()
                }
            })
        }else{
            beforeToProRecord()
        }
    }
}
let valObj=ref({})
function beforeToProRecord(params) {
    if(flag.proNameColorFlag){ ///////////////////////
        close()
        return ElMessage({
            message: proName.value,
            type: 'warning',
        })
    }
    $axios({    //业务类型
        url:`/project/getCopyByCodeV2/${proCode.value}`,
        method:"get",
    }).then(res=>{
        if(res.data.code !== 200){
            return ElMessage({
                message: res.data.msg,
                type: 'warning',
            })
        }else{
            close()
            // router_userRouter.push({path:"../../projectRegistrationEditorView",query:{proCode:proCode.value}})
            if(tabIndex.value === '1') ElMessage.success("复制成功，已自动保存项目信息，可编辑修改")
            console.log(valObj.value)
            emit("edit",{proCode:proCode.value,valObj:valObj.value})
        }
    }).finally(()=>{
        disabledBtn.value = false
    })
}
// to 项目登记（复制项目）
// 项目编号 => 基本信息
function getByCode(){
    return new Promise((reslove,reject)=>{
        if(!proCode.value){
            return ElMessage({
                message: '请填写项目编号',
                type: 'warning',
            })
        }
        $axios({    //业务类型
            url:`/project/findCopyByCode/${proCode.value}`
        }).then(res=>{
            if(res.data.code===200){
                valObj.value=res.data.data;
                flag.proNameShowFlag = true
                flag.proNameColorFlag = false
                proName.value = res.data.data.name
                reslove(true)
            }else if(res.data.code===202){
                flag.proNameShowFlag = true
                flag.proNameColorFlag = true
                proName.value = res.data.msg
                reslove(false)
            }else{
                flag.proNameShowFlag = false
                reslove(false)
                return ElMessage({
                    message: res.data.msg,
                    type: 'warning',
                })
            }
        }).finally(()=>{
            disabledBtn.value = false
        })
    })
}
defineExpose({open})
</script>

<style lang="scss" scoped>
.tab-class{
    padding: 0 15px;
}
.main-box{
    padding: 15px 10% 15px 15px;
    :deep(.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active){
        font-weight: bold;
        color: #0b8df1;
    }
    table{
        width: 100%;
    }
    // table,tr,td {
    //     border-collapse: collapse;
    //     border: 1px solid #eee;
    //     text-align: center;
    // }
    tr{
        height: 36px;
        td:nth-child(1){
            // text-align: left;
            // padding-left: 10px;
            width: 90px;
        }
        td:nth-child(2){
            text-align: left;
            padding-left: 10px;
        }
    }
    .title{
        text-align: right;
        // padding-right: 6px;
        // background: #F9F9F9;
    }
}
.active{
    // height: 300px;
}
:deep(.el-cascader) {
  width: 100% !important;
  border-radius: 0px;
  // border: 1px solid #dcdfe6;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 14px;
  background-color: transparent;

  .el-input__inner {
    border: none;
  }
}
</style>