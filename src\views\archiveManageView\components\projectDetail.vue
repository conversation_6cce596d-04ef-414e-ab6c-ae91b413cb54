<template>
  <!-- 项目登记 -->
  <ndb-page ref="pageRef">
    <el-scrollbar>
    <div class="project-registration" :id="id" ref="mainBoxRef">
      <!-- 左侧表单 -->
      <div
        class="project-registration-left"
        :class="rightShow ? 'action2' : 'action1'"
      >
        <div
          class="form-box"
          style="border-radius: 5px; border: 1px solid #eeeeee"
        >
          <div class="form-box-title">
            <div>
              <img src="@/assets/images/projectRegistration/icon1.png" alt="" />
              <span>{{ projectInfor.forms.name }}</span>
            </div>
          </div>
          <div>
            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
              style="margin-top: 15px"
            >
              <tbody style="display: flex; flex-wrap: wrap">
                <tr
                  style="width: 50%"
                  v-for="(item, index) in projectInfor.forms.baseFieldList"
                  :key="index"
                >
                  <td class="title" width="360">{{ item.fieldName }}</td>
                  <td style="width: 75%">
                    <span class="text1">{{ item.fieldValue }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- <table
            width="100%"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
            style="margin-top: 15px"
          >
            <tbody style="display: flex; flex-wrap: wrap">
              <tr style="width: 50%">
                <td class="title" width="360">业务类型</td>
                <td style="width: 75%">
                  <span class="text1">{{
                    projectInfor.forms.busTypeName
                  }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">交易品种</td>
                <td style="width: 75%">
                  <span class="text1"
                    >{{ projectInfor.forms.tradeVarietyName }}-{{
                      projectInfor.forms.childTradeVarietyName
                    }}</span
                  >
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">项目名称</td>
                <td style="width: 75%">
                  <span class="text1">{{ projectInfor.forms.name }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">项目编号</td>
                <td style="width: 75%">
                  <span class="text1">{{ projectInfor.forms.code }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">乡镇（街道）</td>
                <td style="width: 75%">
                  <span class="text1">{{
                    projectInfor.forms.townName || "--"
                  }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">村（社区）</td>
                <td style="width: 75%">
                  <span class="text1">{{
                    projectInfor.forms.villageName || "--"
                  }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">交易方式</td>
                <td style="width: 75%">
                  <span class="text1">{{
                    projectInfor.forms.tradeTypeName || "--"
                  }}</span>
                </td>
              </tr>
              <tr style="width: 50%">
                <td class="title" width="360">交易面积</td>
                <td style="width: 75%">
                  <span class="text1"
                    >{{ projectInfor.forms.tradeArea || "--"
                    }}{{ tradeAreaUnit }}</span
                  >
                </td>
              </tr>
              <tr style="width: 100%">
                <td class="title" width="360">项目描述</td>
                <td style="width: 87.5%">
                  <span class="text1">{{
                    projectInfor.forms.description || "--"
                  }}</span>
                </td>
              </tr>
            </tbody>
          </table> -->
          </div>
        </div>
        <div
          class="form-box"
          style="background-color: #f7f8fa; padding: 15px 0px"
        >
          <div
            style="
              background-color: #fff;
              border: 1px solid #eeeeee;
              border-radius: 5px;
            "
          >
            <el-tabs
              style="width: 750px; margin: 0 auto"
              v-model="editableTabsValue"
              :stretch="true"
              class="demo-tabs"
              @tab-click="handleClick"
            >
              <el-tab-pane
                v-for="item in tabList"
                :key="item.name"
                :label="item.title"
                :name="item.name"
              >
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="form-box-title" v-show="activeName == '0'">
            <div
              v-for="(item, index) in formRespons.list"
              :key="index"
              style="
                background-color: #fff;
                margin-top: 15px;
                padding-bottom: 15px;
                border-radius: 5px;
                border: 1px solid #eeeeee;
              "
            >
              <div style="padding: 15px 15px 0px 15px">
                <!-- <img v-if="item.moduleType == 1" :src="img.arry[0]" alt="">
                            <img v-if="item.moduleType == 2" :src="img.arry[1]" alt="">
                            <img v-if="item.moduleType == 3" :src="img.arry[2]" alt="">
                            <img v-if="item.moduleType == 7 || item.moduleType == 8 || item.moduleType == 9"
                                :src="img.arry[3]" alt="">
                            <img v-if="item.moduleType == 4" :src="img.arry[3]" alt="">
                            <img v-if="item.moduleType == 5 || item.moduleType == 14 || item.moduleType == 15"
                                :src="img.arry[4]" alt="">
                            <img v-if="item.moduleType == 6" :src="img.arry[5]" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px">{{
                  item.moduleName
                }}</span>
                <el-icon
                  v-show="item.isShow == true"
                  style="float: right; cursor: pointer"
                  color="#0B8DF1"
                  @click="showDetail(item)"
                >
                  <ArrowUp />
                </el-icon>
                <el-icon
                  v-show="item.isShow == false"
                  style="float: right; cursor: pointer"
                  color="#0B8DF1"
                  @click="showDetail(item)"
                >
                  <ArrowDown />
                </el-icon>
              </div>
              <div
                style="padding: 0px 15px 15px 15px"
                v-show="item.isShow == true"
                v-if="item.childFlag == 1"
              >
                <div
                  v-for="(itemChild, index) in item.childModuleList"
                  :key="index"
                >
                  <table
                    width="100%"
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                  >
                    <tbody
                      v-for="(
                        itemChild2, index2
                      ) in itemChild.templateModleParam"
                      :key="index2"
                      style="display: flex; flex-wrap: wrap"
                    >
                      <tr
                        v-for="(itemChild3, index) in itemChild2
                          .templateParamVoList[0].templateParam"
                        :key="index"
                        :style="
                          itemChild3.paramCells == '1'
                            ? 'width:50%'
                            : 'width:100%'
                        "
                      >
                        <td
                          class="title"
                          :width="itemChild3.paramCells == '1' ? '220' : '360'"
                        >
                          {{ itemChild3.paramName }}
                        </td>
                        <td
                          :width="
                            itemChild3.paramCells == '1' ? '75%' : '87.5%'
                          "
                        >
                          <!-- <span
                          class="text1"
                          v-if="itemChild3.paramKey == 'rentIncRule'"
                          v-for="(
                            rentIncChild, rentIncIndex
                          ) in itemChild3.optionList"
                          :key="rentIncIndex"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                            display: flex;
                            flex-direction: column;
                          "
                        >
                          <span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                            "
                            >第{{ rentIncChild.year }}年递增{{
                              rentIncChild.values
                            }}%</span
                          >
                        </span>
                        <span
                          class="text1"
                          v-else
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ itemChild3.defaultValue }}</span
                        > -->
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ itemChild3.defaultValue }}</span
                          >
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div
                style="padding: 0px 15px 15px 15px"
                v-show="item.isShow == true"
                v-else
              >
                <div
                  v-for="(itemChild, index) in item.templateParamVoList"
                  v-show="itemChild.id != ''"
                  :key="index"
                >
                  <div
                    v-if="item.moduleType != 1"
                    style="
                      background-color: #f9f9f9;
                      font-size: 14px;
                      color: rgb(96, 98, 102);
                      height: 30px;
                      line-height: 30px;
                      margin-top: 12px;
                    "
                  >
                    <i
                      style="
                        margin-left: 12px;
                        font-style: normal;
                        font-weight: bold;
                      "
                      >{{ item.moduleName }}{{ index + 1 }}</i
                    >
                  </div>
                  <table
                    width="100%"
                    :class="
                      item.moduleType == 1
                        ? 'detailsbd-tc-s2'
                        : 'detailsbd-tc-s'
                    "
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                  >
                    <tbody style="display: flex; flex-wrap: wrap">
                      <!-- <tr style="width: 100%;">
                                        <td class="title" width="360">{{ item.moduleName }}{{index}}</td>
                                        <td style="width:75%">
                                            <span class="text1"></span>
                                        </td>
                                    </tr> -->
                      <tr
                        v-for="(itemChild2, index) in itemChild.templateParam"
                        :key="index"
                        :style="
                          itemChild2.paramCells == '1'
                            ? 'width:50%'
                            : 'width:100%'
                        "
                        style="display: flex; flex-direction: row"
                      >
                        <!-- <td class="title" :width="itemChild2.paramCells == '1' ? '220' : '360'" > -->
                        <td
                          class="title"
                          :class="
                            itemChild2.paramCells == '1' ? 'width3' : 'width4'
                          "
                        >
                          {{ itemChild2.paramName
                          }}<span
                            v-if="itemChild2.paramUnit"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >({{ itemChild2.paramUnit }})</span
                          >
                        </td>
                        <!-- <td :width="itemChild2.paramCells == '1' ? '75%' : '87.5%'"> -->
                        <td
                          :class="
                            itemChild2.paramCells == '1' ? 'width1' : 'width2'
                          "
                        >
                          <span
                            class="text1"
                            v-if="
                              itemChild2.paramKey == 'yearNum' &&
                              (itemChild2.paramKey != 'addressLocation' ||
                                itemChild2.paramKey != 'storeLocation')
                            "
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                              cursor: pointer;
                            "
                            >{{ itemChild2.defaultValue }}年{{
                              itemChild2.optionSetValue
                            }}月</span
                          >
                          <!-- <span  class="text1"
                                            v-if="itemChild2.otheRuleFlag == 1"
                                            style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;cursor:pointer;">其他<i v-if="itemChild2.otheFieldKeyValue"> ({{
                                                itemChild2.otheFieldKeyValue }})</i></span> -->
                          <span
                            class="text1"
                            v-if="itemChild2.otheRuleFlag == 1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                              cursor: pointer;
                            "
                          >
                            <!-- 其他 -->
                            {{ itemChild2.defaultValue }}
                            <i
                              v-if="
                                itemChild2.defaultValue.includes('其他') &&
                                itemChild2.otheFieldKeyValue
                              "
                            >
                              ({{ itemChild2.otheFieldKeyValue }})</i
                            >
                          </span>
                          <span
                            @click="mapClick(itemChild2)"
                            class="text1 operateBtn"
                            v-if="
                              itemChild2.paramKey == 'addressLocation' ||
                              itemChild2.paramKey == 'storeLocation'
                            "
                            style="
                              color: #0b8df1;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                              cursor: pointer;
                            "
                            >{{ itemChild2.defaultValue[2] }}</span
                          >
                          <span
                            v-if="itemChild2.controlType == 12"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                              cursor: pointer;
                            "
                            >{{ itemChild2.defaultValue }}至{{
                              itemChild2.optionSetValue
                            }}</span
                          >
                          <span
                            class="text1"
                            v-if="
                              itemChild2.otheRuleFlag != 1 &&
                              itemChild2.paramKey != 'yearNum' &&
                              itemChild2.paramKey != 'addressLocation' &&
                              itemChild2.paramKey != 'storeLocation' &&
                              itemChild2.controlType != 12 &&
                              itemChild2.paramName != '交易标的物'
                            "
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ itemChild2.defaultValue || "--" }}
                            <!-- <span
                                                    style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;"
                                                    v-if="itemChild2.paramKey == 'tradeArea'">亩</span> -->
                            <span
                              style="
                                color: #444444;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                                margin-left: 0px;
                              "
                              v-if="itemChild2.controlType == 11"
                              >{{ itemChild2.optionSetValue }}</span
                            ></span
                          >

                          <!-- <span class="text1" v-else
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;">{{
                                                    itemChild2.defaultValue }}<span
                                                    style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;"
                                                    v-if="itemChild2.paramKey == 'tradeArea'">亩</span></span> -->
                          <el-icon
                            v-if="
                              itemChild2.encrypt &&
                              itemChild2.desensitizationFlag == true &&
                              itemChild2.isShow != true
                            "
                            style="margin-left: 10px; cursor: pointer"
                            @click="certNoClick3(itemChild2)"
                          >
                            <Hide />
                          </el-icon>
                          <el-icon
                            v-if="
                              itemChild2.encrypt &&
                              itemChild2.desensitizationFlag == true &&
                              itemChild2.isShow == true
                            "
                            style="margin-left: 10px; cursor: pointer"
                            @click="certNoClick4(itemChild2)"
                          >
                            <View />
                          </el-icon>

                          <div v-if="itemChild2.paramName == '交易标的物'" class="test" >
                          <div
                            style="
                              width: 100%;
                              font-size: 14px;
                              display: flex;
                              flex-direction: row;
                              align-items: center;
                              padding: 10px 0px;
                              color: #909399;
                            "
                          >
                            <span
                              style="
                                margin-right: 10px;
                                color: #909399;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                              "
                              >本次交易标的数量合计:<i
                                style="color: #606266; margin-left: 15px"
                              >
                                {{ item.matterVo.tradeMatterVoList.length }} </i
                              >个</span
                            >
                            <span
                              style="
                                margin-left: 20px;
                                color: #909399;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                              "
                              >交易面积/数量合计:<i
                                style="color: #606266; margin-left: 15px"
                                >{{ item.matterVo.matterArea }}</i
                              ></span
                            >{{ item.matterVo.matterAreaUnit }}
                          </div>

                          <nd-table
                            style="width: 100%; margin-bottom: 15px"
                            :data="pagedData"
                          >
                          <!-- <nd-table
                            style="width: 100%; margin-bottom: 15px"
                            max-height="500"
                            :data="item.matterVo.tradeMatterVoList"
                          > -->
                            <el-table-column
                              align="center"
                              :index="indexMethod"
                              type="index"
                              label="序号"
                              width="80"
                            />
                            <el-table-column
                              align="center"
                              prop="name"
                              label="标的物名称"
                            >
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="code"
                              label="标的物编号"
                            >
                            <template #default="{ row }">
                                <div>{{ row.code || '--' }}</div>
                              </template>
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="type"
                              label="标的物类型"
                            >
                            <template #default="{ row }">
                                <div>{{ row.type || '--' }}</div>
                              </template>
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="matterArea"
                              label="交易面积/数量"
                            >
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="matterAreaUnit"
                              label="单位"
                            >
                            </el-table-column>
                            <!-- <el-table-column
                              align="center"
                              prop="matterPrice"
                              label="预期价格（元）"
                            >
                            <template #default="{ row }">
                                <div>{{ row.matterPrice || '--' }}</div>
                              </template>
                            </el-table-column> -->
                            <el-table-column
                              align="center"
                              prop="matterPrice"
                              label="交易底价"
                            >
                              <template #default="{ row }">
                                <!-- <div>{{ Number(row.matterPrice)?.toFixed(2) || "--" }}</div> -->
                                <div>
                                  {{
                                    row.matterPrice
                                      ? Number(row.matterPrice).toFixed(2)
                                      : "--"
                                  }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="matterPriceUnitName"
                              label="价格单位"
                            >
                              <template #default="{ row }">
                                <div>{{ row.matterPriceUnitName || "--" }}</div>
                              </template>
                            </el-table-column>
                          </nd-table>
                          <div
              class="pagination-box"
              style="width: 100%;
                padding: 10px 15px;
                background: #f7f8fa;
                border: 1px solid #eeeeee;
                border-top: none;
              " v-if="page.total2>=10"
            >
              <nd-pagination
                v-model:total="page.total2"
                v-model:current-page="proForm.pageNo2"
                v-model:page-size="proForm.pageSize2"
                @size-change="handleSizeChange2"
                @current-change="handleCurrentChange2"
              >
              </nd-pagination>
            </div>
                        </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- <div style="padding: 0px 15px 15px 15px;" class="bdw" v-if="item.moduleType==1 && item.matterFlag==1&&item.matterVo">
                            <div
                                style="
                                    width: 100%;
                                    font-size: 14px;
                                    display: flex;
                                    flex-direction: row;
                                    align-items: center;
                                    padding: 10px 10px;
                                    border: 1px solid #eeeeee;
                                    border-bottom: none;
                                "
                                >
                                <span style="margin-right: 10px">本次交易标的数量合计:{{item.matterVo.tradeMatterVoList.length}}个</span>
                                <span style="margin-left: 20px"
                                    >交易面积/数量合计:{{item.matterVo.matterArea}}</span
                                >{{item.matterVo.matterAreaUnit}}
                                </div>

                                <nd-table
      style="height: 350px; width: 100%; margin-bottom: 15px"
      :data="item.matterVo.tradeMatterVoList"
    >
      <el-table-column
        align="center"
        :index="indexMethod"
        type="index"
        label="序号"
        width="80"
      />
      <el-table-column align="center" prop="name" label="标的物名称">
      </el-table-column>
      <el-table-column align="center" prop="code" label="标的物编号">
       
      </el-table-column>
      <el-table-column align="center" prop="type" label="标的物类型">
       
      </el-table-column>
      <el-table-column align="center" prop="matterArea" label="交易面积/数量">
        
      </el-table-column>
      <el-table-column align="center" prop="matterAreaUnit" label="单位">
        
      </el-table-column>
      <el-table-column
        align="center"
        prop="matterPrice"
        label="预期价格（元）"
      >
      </el-table-column>
    </nd-table>


                        </div> -->
            </div>

            <div
              id="tpxx"
              style="
                margin-top: 15px;
                background-color: #fff;
                border-radius: 5px;
                border: 1px solid #eeeeee;
              "
            >
              <div class="form-box-title">
                <div
                  style="
                    background-color: #fff;
                    margin-top: 15px;
                    padding: 0px 15px 0px 15px;
                    padding-bottom: 15px;
                  "
                >
                  <!-- <img src="@/assets/images/projectRegistration/icon3.png" alt=""> -->
                  <span style="margin-left: 0; color: #606266; font-size: 14px"
                    >图片信息</span
                  >
                  <el-icon
                    v-show="imgShow.falg1 == true"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail2()"
                  >
                    <ArrowUp />
                  </el-icon>
                  <el-icon
                    v-show="imgShow.falg1 == false"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail2()"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
              <div v-show="imgShow.falg1 == true" style="padding: 0px 0px">
                <div class="border-card-content">
                  <el-tabs type="border-card">
                    <el-tab-pane label="静态图片">
                      <div
                        class="upload-box"
                        v-for="(item, index) in formRespons.uploadList"
                        :key="index"
                      >
                        <div
                          class="upload-box-title"
                          style="color: #0b8df1; font-size: 14px"
                        >
                          {{ item.fileName }}
                        </div>
                        <div class="upload-box-img">
                          <div
                            class="upload-box-img-box"
                            v-if="isUpload && bidStatus"
                          >
                            <ndb-upload
                              v-model="item.fileList"
                              style="margin-top: 12px"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                              @successCallback="
                                successCallback($event, {
                                  operationTitle: '项目-图片信息',
                                  operationContent: item.fileName,
                                })
                              "
                            ></ndb-upload>
                          </div>
                          <div class="upload-box-img-box" v-else>
                            <img
                              v-if="item.fileList.length == 0"
                              style="
                                width: 100px;
                                height: 100px;
                                margin: 10px 0 0 0;
                                border-radius: 6px;
                              "
                              src="@/assets/images/biddingSupervisionDetailView/empty.png"
                              alt=""
                            />
                            <ndb-upload
                              disabled
                              v-model="item.fileList"
                              style="margin-top: 12px"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                            ></ndb-upload>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="全景图片">
                      <nd-search-more-item
                        title="项目全景图展示链接地址"
                        width="180px"
                      >
                        <nd-input
                          v-model="projectInfor.panoramaPictureUrl"
                          placeholder="请输入"
                          width="90%"
                          :disabled="locationShow"
                          clearable
                        ></nd-input>
                      </nd-search-more-item>
                    </el-tab-pane>
                    <el-tab-pane label="视频">
                      <nd-search-more-item
                        title="视频播放链接地址"
                        width="180px"
                      >
                        <nd-input
                          v-model="projectInfor.panoramaVideoUrl"
                          placeholder="请输入"
                          width="90%"
                          :disabled="locationShow"
                          clearable
                        ></nd-input>
                      </nd-search-more-item>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>

            <div
              id="wsxx"
              style="
                margin-top: 15px;
                background-color: #fff;
                border-radius: 5px;
                border: 1px solid #eeeeee;
              "
            >
              <div class="form-box-title">
                <div
                  style="
                    background-color: #fff;
                    margin-top: 15px;
                    padding: 0px 15px 0px 15px;
                    padding-bottom: 15px;
                  "
                >
                  <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                  <span style="margin-left: 0; color: #606266; font-size: 14px"
                    >文书信息</span
                  >
                  <el-icon
                    v-show="imgShow.falg2 == true"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail3()"
                  >
                    <ArrowUp />
                  </el-icon>
                  <el-icon
                    v-show="imgShow.falg2 == false"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail3()"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
              <div
                style="padding: 0px 15px 15px 15px"
                v-show="imgShow.falg2 == true"
              >
                <nd-table style="height: 100%" :data="getDocList.arry">
                  <el-table-column
                    align="center"
                    type="index"
                    width="100"
                    label="序号"
                  />
                  <el-table-column
                    align="left"
                    prop="modelName"
                    min-width="520"
                    label="文书名称"
                  >
                    <template #default="{ row }">
                      <span
                        style="
                          cursor: pointer;
                          color: #0098ff;
                          font-size: 14px;
                          margin-left: 0px !important;
                          font-weight: normal;
                        "
                        class="operateBtn"
                        @click.stop="pushFlag(2, row, '预览文书')"
                        >{{ row.modelName }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="genFlag"
                    width="150"
                    label="文书状态"
                  >
                    <template #default="{ row }">
                      <span
                        style="
                          color: #444444;
                          font-size: 13px;
                          font-style: normal;
                          font-weight: 400;
                          margin-left: 0px !important;
                        "
                        >{{
                          row.dtbId == null || row.dtbId == ""
                            ? "未生成"
                            : "已生成"
                        }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="genFlag"
                    label="是否必须生成文书"
                    width="250"
                  >
                    <template #default="{ row }">
                      <span
                        style="
                          color: #444444;
                          font-size: 13px;
                          font-style: normal;
                          font-weight: 400;
                          margin-left: 0px !important;
                        "
                        >{{
                          row.genFlag == 0
                            ? "否"
                            : row.genFlag == 1
                            ? "是"
                            : "-"
                        }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="pushStatus"
                    label="推送状态"
                    width="150"
                  >
                    <template #default="{ row }">
                      <span
                        style="
                          color: #444444;
                          font-size: 13px;
                          font-style: normal;
                          font-weight: 400;
                          margin-left: 0px !important;
                        "
                        >{{
                          (row.pushStatus == 0 || row.pushStatus == null) &&
                          row.pushFlag == 1
                            ? "待推送"
                            : row.pushStatus == 1 && row.pushFlag == 1
                            ? "已推送"
                            : row.pushFlag == 0
                            ? "-"
                            : "-"
                        }}</span
                      >
                    </template>
                  </el-table-column>

                  <el-table-column
                    align="center"
                    label="操作"
                    width="110px"
                    v-if="isUpload && bidStatus"
                  >
                    <template #default="{ row }">
                      <div
                        style="
                          display: flex;
                          justify-content: space-around;
                          align-items: center;
                        "
                      >
                        <nd-button
                          v-if="row.dtbId == null || row.dtbId == ''"
                          type="createWS"
                          @click.stop="writDoc(1, row, '生成文书')"
                        ></nd-button>
                        <nd-button
                          v-else
                          type="againCreateWS"
                          @click.stop="writDoc(1, row, '重新生成')"
                        ></nd-button>
                      </div>
                    </template>
                  </el-table-column>
                </nd-table>
              </div>
            </div>

            <div
              id="fjxx"
              style="
                margin-top: 15px;
                background-color: #fff;
                border-radius: 5px;
                border: 1px solid #eeeeee;
              "
            >
              <div class="form-box-title">
                <div
                  style="
                    background-color: #fff;
                    margin-top: 15px;
                    padding: 0px 15px 0px 15px;
                    padding-bottom: 15px;
                  "
                >
                  <!-- <img src="@/assets/images/projectRegistration/icon2.png" alt=""> -->
                  <span style="margin-left: 0; color: #606266; font-size: 14px"
                    >附件信息</span
                  >
                  <el-icon
                    v-show="imgShow.falg3 == true"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail4()"
                  >
                    <ArrowUp />
                  </el-icon>
                  <el-icon
                    v-show="imgShow.falg3 == false"
                    style="float: right; cursor: pointer"
                    color="#0B8DF1"
                    @click="showDetail4()"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
              <div
                v-show="imgShow.falg3 == true"
                style="padding: 0px 15px 15px 15px"
              >
                <div>
                  <div
                    class="upload-box"
                    style="margin: 10px 0px"
                    v-for="(item, index) in formRespons.uploadList2"
                    :key="index"
                  >
                    <div
                      class="upload-box-title"
                      style="color: #0b8df1; font-size: 14px"
                    >
                      {{ item.fileName }}
                    </div>
                    <div class="upload-box-img">
                      <div
                        class="upload-box-img-box"
                        v-if="isUpload && bidStatus"
                      >
                        <ndb-upload
                          v-model="item.fileList"
                          style="margin-top: 12px"
                          :uploadParams="{
                            busId: projectInfor.id,
                            configFileId: item.id,
                          }"
                          @successCallback="
                            successCallback($event, {
                              operationTitle: '项目-附件信息',
                              operationContent: item.fileName,
                            })
                          "
                        ></ndb-upload>
                      </div>
                      <div class="upload-box-img-box" v-else>
                        <img
                          v-if="item.fileList.length == 0"
                          style="
                            width: 100px;
                            height: 100px;
                            margin: 10px 0 0 0;
                            border-radius: 6px;
                          "
                          src="@/assets/images/biddingSupervisionDetailView/empty.png"
                          alt=""
                        />
                        <ndb-upload
                          disabled
                          v-model="item.fileList"
                          style="margin-top: 12px"
                          :uploadParams="{
                            busId: projectInfor.id,
                            configFileId: item.id,
                          }"
                        ></ndb-upload>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 自主交易 1  公告详情 -->
            <div
              class="form-box-title-tender"
              style="border: 1px solid #eeeeee; border-radius: 5px"
              v-if="tradeMode == 1"
            >
              <!-- <div class="form-box-title-tender" v-if="tradeMode==1"> -->
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >公告信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                >
                  <tbody>
                    <!-- 第一行 -->
                    <tr>
                      <td class="title" width="200">公告类型</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.type == 1
                              ? "项目公告"
                              : information.publicInfo.type == 2
                              ? "标段公告"
                              : "--"
                          }}</span
                        >
                      </td>
                    </tr>
                    <!-- 第二行 -->
                    <tr>
                      <td class="title" width="200">报名开始时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.signupBeginTime || "--"
                          }}</span
                        >
                      </td>
                      <td class="title" width="200">报名结束时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.signupEndTime || "--"
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">报名地点</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.signupAddress }}</span
                        >
                      </td>
                      <td class="title" width="200">报名缴款截止时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.signPayEndtime || "--"
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.publicInfo.biddingTypeName">
                      <td class="title" width="200">竞价方式</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.biddingTypeName }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        information.publicInfo.biddingType != '8' &&
                        information.publicInfo.biddingType != '7' &&
                        information.publicInfo.biddingType
                      "
                    >
                      <td class="title" width="200">出价方式</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                        >
                          {{
                            information.publicInfo.biddingType == 1 ||
                            information.publicInfo.biddingType == 2
                              ? "正向"
                              : information.publicInfo.biddingType == 3 ||
                                information.publicInfo.biddingType == 4
                              ? "反向"
                              : ""
                          }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">
                        交易底价<span
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          v-if="information.publicInfo.floorPriceUnit_dictText"
                          >({{
                            information.publicInfo.floorPriceUnit_dictText
                          }})</span
                        >
                      </td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.floorPrice }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        information.publicInfo.biddingType != '8' &&
                        information.publicInfo.biddingType != '7' &&
                        information.publicInfo.bidMargin
                      "
                    >
                      <td class="title" width="200">
                        幅度<span
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          v-if="information.publicInfo.floorPriceUnit_dictText"
                          >({{
                            information.publicInfo.floorPriceUnit_dictText
                          }})</span
                        >
                      </td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.bidMargin }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        information.publicInfo.biddingType != '8' &&
                        information.publicInfo.biddingType != '7' &&
                        information.publicInfo.limitPrice
                      "
                    >
                      <td class="title" width="200">
                        最高限价<span
                          v-if="information.publicInfo.floorPriceUnit_dictText"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >({{
                            information.publicInfo.floorPriceUnit_dictText
                          }})</span
                        >
                      </td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.limitPrice }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">竞价开始时间</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.biddingStartTime }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        information.publicInfo.biddingType != '8' &&
                        information.publicInfo.biddingType != '7' &&
                        information.publicInfo.biddingType
                      "
                    >
                      <td class="title" width="200">竞价结束时间</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.biddingEndTime }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        information.publicInfo.biddingType != '8' &&
                        information.publicInfo.biddingType != '7' &&
                        information.publicInfo.postponeSecond
                      "
                    >
                      <td class="title" width="200">限时竞价延迟(秒)</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.postponeSecond }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">标段</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.tenderDesc }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.publicInfo.tradeAddress">
                      <td class="title" width="200">交易地点</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.tradeAddress }}</span
                        >
                      </td>
                    </tr>
                    <!-- <tr>
                                        <td class="title" width="200">标段</td>
                                        <td>
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;">{{
                                                    information.publicInfo.tenderDesc }}</span>
                                        </td>
                                        <td class="title" width="200">交易地点</td>
                                        <td>
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;">{{
                                                    information.publicInfo.tradeAddress }}</span>
                                        </td>
                                    </tr> -->
                    <tr>
                      <td class="title" width="200">联系人</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.contactPerson }}</span
                        >
                      </td>
                      <td class="title" width="200">联系电话</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.publicInfo.contactPhone }}</span
                        >
                      </td>
                    </tr>

                    <tr>
                      <td class="title" width="200">是否定时发布公告</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.publishFlag == 0
                              ? "否"
                              : information.publicInfo.publishFlag == 1
                              ? "是"
                              : "--"
                          }}</span
                        >
                      </td>
                      <td class="title" width="200">是否开启挂牌公告</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.publicInfo.hangFlag == 0
                              ? "否"
                              : information.publicInfo.hangFlag == 1
                              ? "是"
                              : "--"
                          }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              class="form-box-title-tender"
              v-if="
                projectInfor.forms.abortPerson &&
                projectInfor.forms.status == 100
              "
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >终止信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                >
                  <tbody>
                    <tr>
                      <td class="title" width="150">操作人员</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ projectInfor.forms.abortPerson }}</span
                        >
                      </td>
                      <td class="title" width="150">操作时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ projectInfor.forms.abortTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">终止原因</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ projectInfor.forms.abortReason }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">附件信息</td>
                      <td colspan="3">
                        <div
                          v-for="(item, index) in projectInfor.forms.abortFiles"
                          :key="index"
                        >
                          <span>{{ item.fileName }}</span>
                          <div v-if="isUpload && bidStatus">
                            <ndb-upload
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                              @successCallback="
                                successCallback($event, {
                                  operationTitle: '项目-终止信息',
                                  operationContent: item.fileName,
                                })
                              "
                            ></ndb-upload>
                          </div>
                          <div v-else>
                            <ndb-upload
                              disabled
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                            ></ndb-upload>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              class="form-box-title-tender"
              v-if="projectInfor.forms.retryFlag"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >项目重发信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                >
                  <tbody>
                    <tr>
                      <td class="title" width="150">操作人员</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ projectInfor.forms.retryOperator }}</span
                        >
                      </td>
                      <td class="title" width="150">操作时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ projectInfor.forms.retryTime }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="form-box-title" v-show="activeName == '1'">
            <div class="form-box-title-tender">
              <div style="margin-top: 15px">
                <!-- <img :src="img.arry[4]" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >标段信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody
                    v-for="(itemChild2, index2) in information.formModule"
                    :key="index2"
                    style="display: flex; flex-wrap: wrap"
                  >
                    <tr
                      v-for="(itemChild3, index) in itemChild2.templateParam"
                      style="display: flex; flex-direction: row"
                      :key="index"
                      :style="
                        itemChild3.paramCells == '1'
                          ? 'width:50%'
                          : 'width:100%'
                      "
                    >
                      <td
                        class="title"
                        :class="
                          itemChild3.paramCells == '1' ? 'width3' : 'width4'
                        "
                      >
                        {{ itemChild3.paramName }}
                      </td>
                      <td
                        :class="
                          itemChild3.paramCells == '1' ? 'width1' : 'width2'
                        "
                      >
                        <span
                          class="text1"
                          v-show="itemChild3.paramKey == 'rentIncRule'"
                          v-for="(
                            rentIncChild, rentIncIndex
                          ) in itemChild3.optionList"
                          :key="rentIncIndex"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                            display: flex;
                            flex-direction: column;
                          "
                        >
                          <span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                            "
                            >第{{ rentIncChild.year }}年递增上年金额的{{
                              rentIncChild.values
                            }}%</span
                          >
                        </span>
                        <span
                          class="text1"
                          v-if="itemChild3.paramKey == 'yearNum'"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                            cursor: pointer;
                          "
                          >{{ itemChild3.defaultValue }}年{{
                            itemChild3.optionSetValue
                          }}月</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            (itemChild3.controlType == 11 ||
                              itemChild3.controlType == 14)
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ itemChild3.defaultValue
                          }}{{ itemChild3.optionSetValue }}</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'perFeeRule' &&
                            itemChild3.controlType == 17
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >履约金固定金额{{ itemChild3.defaultValue }}元</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'perFeeRule' &&
                            itemChild3.controlType == 16 &&
                            projectInfor.LZFS != '1'
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >收取成交价的{{ itemChild3.defaultValue }}%</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'perFeeRule' &&
                            itemChild3.controlType == 16 &&
                            projectInfor.LZFS == '1'
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >收取首年租金的{{ itemChild3.defaultValue }}%</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'serviceFeeRule' &&
                            itemChild3.controlType == 17
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >服务费固定金额{{ itemChild3.defaultValue }}元</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'serviceFeeRule' &&
                            itemChild3.controlType == 16 &&
                            projectInfor.LZFS == '1'
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >按{{ itemChild3.defaultValue }}年成交价收取</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.defaultValue &&
                            itemChild3.paramKey == 'serviceFeeRule' &&
                            itemChild3.controlType == 16 &&
                            projectInfor.LZFS != '1'
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >按成交价收取</span
                        >
                        <span
                          class="text1"
                          v-if="
                            itemChild3.paramKey != 'serviceFeeRule' &&
                            itemChild3.paramKey != 'perFeeRule' &&
                            itemChild3.paramKey != 'rentIncRule' &&
                            itemChild3.controlType != 11 &&
                            itemChild3.controlType != 14 &&
                            itemChild3.paramKey != 'yearNum' &&
                            itemChild3.paramName != '交易标的物'
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ itemChild3.defaultValue }}</span
                        ><span
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          v-if="
                            itemChild3.dictType == 'money' &&
                            itemChild3.paramKey != 'reletPrice' &&
                            itemChild3.paramKey != 'tradePrice' &&
                            itemChild3.paramKey != 'floorPrice' &&
                            itemChild3.paramKey != 'tradeAmount'
                          "
                          >元</span
                        >

                        <el-icon
                          v-if="itemChild3.encrypt && itemChild3.isShow != true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick3(itemChild3)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="itemChild3.encrypt && itemChild3.isShow == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick4(itemChild3)"
                        >
                          <View />
                        </el-icon>

                        <div v-if="itemChild3.paramName == '交易标的物'"  class="test">
                        <div
                          style="
                            width: 100%;
                            font-size: 14px;
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            padding: 10px 0px;
                            color: #909399;
                          "
                        >
                          <span
                            style="
                              margin-right: 10px;
                              color: #909399;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                            "
                            >本次交易标的数量合计:<span
                              v-if="matterVo.tradeMatterVoList"
                              style="
                                color: #606266;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                              "
                              >{{ matterVo.tradeMatterVoList.length }}</span
                            >个</span
                          >
                          <span
                            style="
                              margin-left: 20px;
                              color: #909399;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                            "
                            >交易面积/数量合计:<i
                              style="color: #606266; margin-left: 15px"
                              >{{ matterVo.matterArea }}</i
                            ></span
                          >{{ matterVo.matterAreaUnit }}
                        </div>

                        <nd-table
                          style="width: 100%; margin-bottom: 15px"
                          max-height="500"
                          :data="pagedData2"
                        >
                        <!-- <nd-table
                          style="width: 100%; margin-bottom: 15px"
                          max-height="500"
                          :data="matterVo.tradeMatterVoList"
                        > -->
                          <el-table-column
                            align="center"
                            :index="indexMethod2"
                            type="index"
                            label="序号"
                            width="80"
                          />
                          <el-table-column
                            align="center"
                            prop="name"
                            label="标的物名称"
                          >
                          </el-table-column>
                          <el-table-column
                            align="center"
                            prop="code"
                            label="标的物编号"
                          >
                          <template #default="{ row }">
                                <div>{{ row.code || '--' }}</div>
                              </template>
                          </el-table-column>
                          <el-table-column
                            align="center"
                            prop="type"
                            label="标的物类型"
                          >
                          <template #default="{ row }">
                                <div>{{ row.type || '--' }}</div>
                              </template>
                          </el-table-column>
                          <el-table-column
                            align="center"
                            prop="matterArea"
                            label="交易面积/数量"
                          >
                          </el-table-column>
                          <el-table-column
                            align="center"
                            prop="matterAreaUnit"
                            label="单位"
                          >
                          </el-table-column>
                          <!-- <el-table-column
                            align="center"
                            prop="matterPrice"
                            label="预期价格（元）"
                          >
                          <template #default="{ row }">
                                <div>{{ row.matterPrice || '--' }}</div>
                              </template>
                          </el-table-column> -->
                          <el-table-column
                              align="center"
                              prop="matterPrice"
                              label="交易底价"
                            >
                              <template #default="{ row }">
                                <!-- <div>{{ Number(row.matterPrice)?.toFixed(2) || "--" }}</div> -->
                                <div>
                                  {{
                                    row.matterPrice
                                      ? Number(row.matterPrice).toFixed(2)
                                      : "--"
                                  }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="matterPriceUnitName"
                              label="价格单位"
                            >
                              <template #default="{ row }">
                                <div>{{ row.matterPriceUnitName || "--" }}</div>
                              </template>
                            </el-table-column>
                        </nd-table>
                        <div
              class="pagination-box"
              style="width: 100%;
                padding: 10px 15px;
                background: #f7f8fa;
                border: 1px solid #eeeeee;
                border-top: none;
              " v-if="page.total3>=10"
            >
              <nd-pagination
                v-model:total="page.total3"
                v-model:current-page="proForm.pageNo3"
                v-model:page-size="proForm.pageSize3"
                @size-change="handleSizeChange3"
                @current-change="handleCurrentChange3"
              >
              </nd-pagination>
            </div>
                      </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- <div style="margin-top:15px;" class="bdw" v-if="matterFlag==1">
                            <div
                                style="
                                    width: 100%;
                                    font-size: 14px;
                                    display: flex;
                                    flex-direction: row;
                                    align-items: center;
                                    padding: 10px 10px;
                                    border: 1px solid #eeeeee;
                                    border-bottom: none;
                                "
                                >
                                <span style="margin-right: 10px">本次交易标的数量合计:<span v-if="matterVo.tradeMatterVoList">{{matterVo.tradeMatterVoList.length}}</span>个</span>
                                <span style="margin-left: 20px"
                                    >交易面积/数量合计:{{matterVo.matterArea}}</span
                                >{{matterVo.matterAreaUnit}}
                                </div>

                                <nd-table
      style="height: 350px; width: 100%; margin-bottom: 15px"
      :data="matterVo.tradeMatterVoList"
    >
      <el-table-column
        align="center"
        :index="indexMethod"
        type="index"
        label="序号"
        width="80"
      />
      <el-table-column align="center" prop="name" label="标的物名称">
      </el-table-column>
      <el-table-column align="center" prop="code" label="标的物编号">
       
      </el-table-column>
      <el-table-column align="center" prop="type" label="标的物类型">
       
      </el-table-column>
      <el-table-column align="center" prop="matterArea" label="交易面积/数量">
        
      </el-table-column>
      <el-table-column align="center" prop="matterAreaUnit" label="单位">
        
      </el-table-column>
      <el-table-column
        align="center"
        prop="matterPrice"
        label="预期价格（元）"
      >
      </el-table-column>
    </nd-table>


                        </div> -->
            </div>

            <div
              class="form-box-title-tender"
              v-if="
                information.infor1 != 'null' &&
                information.infor1 != null &&
                information.infor1.repeatFlag == true
              "
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >交易公告重发</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <!-- 第一行 -->
                    <tr>
                      <td class="title" width="150">重发理由</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor1.repeatReason }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">重发材料</td>
                      <td colspan="3">
                        <div
                          class="upload-box"
                          v-for="(item2, index) in information.infor1
                            .repeatFileList"
                          :key="index"
                        >
                          <span style="color: #606266; margin-left: 0px">{{
                            item2.fileName
                          }}</span>
                          <div class="upload-box-img" style="margin-top: 5px">
                            <div
                              class="upload-box-img-box"
                              v-if="isUpload && bidStatus"
                            >
                              <ndb-upload
                                v-model="item2.fileList"
                                :uploadParams="{
                                  busId: information.infor1.initialAnn,
                                  configFileId: item2.id,
                                }"
                                @successCallback="
                                  successCallback($event, {
                                    operationTitle: `${tabName}-交易公告重发`,
                                    operationContent: item2.fileName,
                                  })
                                "
                              ></ndb-upload>
                            </div>
                            <div class="upload-box-img-box" v-else>
                              <ndb-upload
                                v-model="item2.fileList"
                                :uploadParams="{
                                  busId: projectInfor.id,
                                  configFileId: item2.id,
                                }"
                                disabled
                              ></ndb-upload>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">操作日期</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor1.repeatTime }}</span
                        >
                      </td>
                      <td class="title" width="150">操作人</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor1.repeatBy }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!--  公告详情 -->
            <div
              class="form-box-title-tender"
              v-if="information.infor1 != 'null' && information.infor1 != null"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >公告信息</span
                >
                <span
                  style="float: right; cursor: pointer"
                  @click="historyClick()"
                  >公告历史记录</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <!-- 第一行 -->
                    <tr>
                      <td class="title" width="200">公告类型</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.type }}</span
                        >
                        <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                      </td>
                      <td class="title" width="200">公告发布时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.publishTime }}</span
                        >
                      </td>
                    </tr>
                    <!-- 第二行 -->
                    <tr>
                      <td class="title" width="200">报名开始时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.signupBeginTime }}</span
                        >
                      </td>
                      <td class="title" width="200">报名结束时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.signupEndTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">报名地点</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.signupAddress }}</span
                        >
                      </td>
                      <td class="title" width="200">报名缴款截止时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.signPayEndtime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">交易时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.tradeTime }}</span
                        >
                      </td>
                      <td class="title" width="200">交易地点</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.tradeAddress }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">联系人</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                        >
                          <span
                            v-if="phoneShow == false"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ information.infor1.contactPerson }}</span
                          >
                          <span
                            v-else
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ information.infor1.contactPerson2 }}</span
                          >
                        </span>
                        <el-icon
                          v-if="
                            information.infor1.contactPersonEncrypt &&
                            phoneShow == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="
                            phoneClick(
                              1,
                              information.infor1.contactPersonEncrypt,
                              information.infor1.contactPerson
                            )
                          "
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="phoneShow == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="
                            phoneClick(
                              2,
                              information.infor1.contactPersonEncrypt,
                              information.infor1.contactPerson
                            )
                          "
                        >
                          <View />
                        </el-icon>
                      </td>
                      <td class="title" width="200">联系电话</td>
                      <td>
                        <!-- <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;margin-left:0px;">{{
                                                    information.infor1.contactPhone }}</span> -->
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                        >
                          <span
                            v-if="phoneShow2 == false"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ information.infor1.contactPhone }}</span
                          >
                          <span
                            v-else
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ information.infor1.contactPhone2 }}</span
                          >
                        </span>
                        <el-icon
                          v-if="
                            information.infor1.contactPhoneEncrypt &&
                            phoneShow2 == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="
                            phoneClick(
                              3,
                              information.infor1.contactPhoneEncrypt,
                              information.infor1.contactPhone
                            )
                          "
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="phoneShow2 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="
                            phoneClick(
                              4,
                              information.infor1.contactPhoneEncrypt,
                              information.infor1.contactPhone
                            )
                          "
                        >
                          <View />
                        </el-icon>
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">成交缴款截止时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.finalDealPayEndtime }}</span
                        >
                      </td>
                      <td class="title" width="200">是否定时发布公告</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.infor1.publishFlag == 0
                              ? "否"
                              : information.infor1.publishFlag == 1
                              ? "是"
                              : "--"
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.infor1.publishFlag == 1">
                      <td class="title" width="200">定时发布时间</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.publishTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="200">是否开启挂牌公告</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{
                            information.infor1.hangFlag == 0
                              ? "否"
                              : information.infor1.hangFlag == 1
                              ? "是"
                              : "--"
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.infor1.hangFlag == 1">
                      <td class="title" width="200">延期时长（自然日）</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.delayTime }}天</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.infor1.hangFlag == 1">
                      <td class="title" width="200">延期轮次</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >{{ information.infor1.delayNum }}轮</span
                        >
                      </td>
                    </tr>
                    <tr v-if="information.infor1.hangFlag == 1">
                      <td class="title" width="200">延期说明</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                          "
                          >若公告挂牌延期，则报名缴款截止时间、交易时间以及成交缴款截止时间延长相同时间。</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              class="form-box-title-tender"
              v-if="information.infor2 != 'null' && information.infor2 != null"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >报名人信息</span
                >
              </div>
              <div style="margin-top: 15px">
                <nd-table
                  style="height: 100%; width: 100%"
                  :data="information.infor2.signUpList"
                >
                  <el-table-column
                    align="center"
                    type="index"
                    width="100"
                    label="序号"
                  />
                  <!-- <el-table-column align="center" prop="name" min-width="100" label="姓名/单位名称" /> -->
                  <el-table-column
                    align="center"
                    prop="name"
                    min-width="100"
                    label="姓名/单位名称"
                  >
                    <template #default="{ row }">
                      <i class="operateBtn" v-if="row.isShow3 == false">{{
                        row.name
                      }}</i>
                      <i class="operateBtn" v-else>{{ row.bidderName2 }}</i>
                      <el-icon
                        v-if="row.nameEncrypt && row.isShow3 == false"
                        style="margin-left: 10px; cursor: pointer"
                        @click="nameClick(3, row)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-if="row.nameEncrypt && row.isShow3 == true"
                        style="margin-left: 10px; cursor: pointer"
                        @click="nameClick2(3, row)"
                      >
                        <View />
                      </el-icon>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="signupType"
                    min-width="100"
                    label="主体类型"
                  />
                  <!-- <el-table-column align="center" prop="certNo" width="200" label="证件号码" /> -->
                  <el-table-column
                    align="center"
                    prop="certNo"
                    min-width="200"
                    label="证件号码"
                  >
                    <template #default="{ row }">
                      <i class="operateBtn" v-if="row.isShow2 == false">{{
                        row.certNo
                      }}</i>
                      <i class="operateBtn" v-else>{{ row.certNo2 }}</i>
                      <el-icon
                        v-if="row.certNoEncrypt && row.isShow2 == false"
                        style="margin-left: 10px; cursor: pointer"
                        @click="certNoClick(3, row)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-if="row.certNoEncrypt && row.isShow2 == true"
                        style="margin-left: 10px; cursor: pointer"
                        @click="certNoClick2(3, row)"
                      >
                        <View />
                      </el-icon>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="marginAmount"
                    min-width="150"
                    label="保证金总额(元)"
                  >
                    <template #default="{ row }">
                      <span class="operateBtn">{{ row.marginAmount }}</span
                      ><span
                        v-if="row.payMarginAmountState"
                        style="
                          font-size: 12px !important;
                          margin-left: 0px !important;
                        "
                        >({{ row.payMarginAmountState }})</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="entryFee"
                    min-width="150"
                    label="材料费(元)"
                    v-if="existSignupFee == 0"
                  >
                    <template #default="{ row }">
                      <span class="operateBtn">{{ row.entryFee }}</span
                      ><span
                        v-if="row.payEntryFeeState"
                        style="
                          font-size: 12px !important;
                          margin-left: 0px !important;
                        "
                        >({{ row.payEntryFeeState }})</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="createTime"
                    width="200"
                    label="报名时间"
                  />

                  <el-table-column
                    align="center"
                    prop="signupStatus"
                    label="报名状态"
                  />
                  <el-table-column
                    align="center"
                    prop="priorityStatus"
                    min-width="150"
                    label="优先权状态"
                  >
                    <template #default="{ row }">
                      <span
                        style="
                          color: #444444;
                          font-size: 14px;
                          font-style: normal;
                          font-weight: normal;
                          margin-left: 0px;
                        "
                        >{{
                          row.priorityStatus == 0
                            ? "待审核"
                            : row.priorityStatus == 1
                            ? "审核通过"
                            : row.priorityStatus == 2
                            ? "审核不通过"
                            : "无优先权"
                        }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="receiptState"
                    label="报名回执"
                  />
                  <el-table-column
                    align="center"
                    prop="receiptState"
                    label="操作"
                  >
                    <template #default="{ row }">
                      <!-- <span class="operateBtn" style="font-size: 12px!important;cursor: pointer;margin-left:0px!important;" @click="lookRegister(row)">查看</span> -->
                      <el-tooltip effect="light" content="查看">
                        <el-button link @click.stop="lookRegister(row)">
                          <span style="cursor: pointer; font-size: 14px"
                            >查看</span
                          >
                          <!-- <img style="width:12px;height: 14px;"
                                                    src="@/assets/images/biddingSupervisionDetailView/look.png" alt="" /> -->
                        </el-button>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </nd-table>
              </div>
            </div>

            <!--  竞价信息 -->
            <div
              class="form-box-title-tender"
              v-if="
                (information.infor7 &&
                  information.infor7.faiBidFlag == 0 &&
                  setObj.stateName) ||
                (information.infor7 && information.infor7.flowStatus == 100) ||
                (information.infor7 && information.infor7.flowStatus > 73)
              "
            >
              <!-- <div style="margin-top:15px;">
                            <img src="@/assets/images/projectRegistration/icon4.png" alt="">
                            <span  style="margin-left:0;color:#606266;font-size:14px;">竞价信息</span>
                        </div> -->

              <!-- 11 -->
              <div class="box">
                <!-- 自由竞价设置 -->
                <div v-if="setObj.type == 1 || setObj.type == 3">
                  <div
                    class="box-title"
                    style="
                      display: flex;
                      flex-direction: row;
                      padding-top: 10px;
                    "
                  >
                    <span style="margin-left: 0px">
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon1.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价设置</span
                      ></span
                    >
                    <nd-button style="margin-left: 12px">{{
                      setObj.stateName
                    }}</nd-button>
                  </div>
                  <table
                    width="100%"
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                    style="margin-top: 15px"
                  >
                    <tbody>
                      <!-- 第一行 -->
                      <tr>
                        <td class="title" width="13%">竞价方式</td>
                        <td width="37%">
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.typeName }}</span
                          >
                        </td>
                        <td class="title" width="150">出价方式</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.type == 1 ||
                              setObj.typeObj1.type == 2
                                ? "正向"
                                : setObj.typeObj1.type == 3 ||
                                  setObj.typeObj1.type == 4
                                ? "反向"
                                : "--"
                            }}</span
                          >
                        </td>
                      </tr>
                      <!-- 第二行 -->
                      <tr>
                        <td class="title" width="150">交易底价</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.floorPrice || "--"
                            }}{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">出价次数</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.oneOfferFlag == "0"
                                ? "多次"
                                : setObj.typeObj1.oneOfferFlag == "1"
                                ? "一次"
                                : "--"
                            }}</span
                          >
                        </td>
                      </tr>
                      <tr v-show="setObj.typeObj1.oneOfferFlag == '0'">
                        <td class="title" width="150">限时竞价时长（秒）</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          >
                            {{ setObj.typeObj1.postponeSecond || "--" }}
                          </span>
                        </td>
                        <td class="title" width="150"></td>
                        <td></td>
                      </tr>
                      <tr>
                        <td class="title" width="150">竞价开始时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.startTime }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价结束时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.endTime }}</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">
                          <span
                            v-show="
                              setObj.typeObj1.type == 1 ||
                              setObj.typeObj1.type == 2
                            "
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >最高限价</span
                          >
                        </td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            v-show="
                              setObj.typeObj1.type == 1 ||
                              setObj.typeObj1.type == 2
                            "
                            >{{ setObj.typeObj1.limitPrice || "--"
                            }}<span   style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            " v-if="setObj.typeObj1.limitPrice">{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span></span
                          >
                        </td>
                        <td class="title" width="150">幅度</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.bidMargin || "--"
                            }}<span
                              style="
                                color: #444444;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                                margin-left: 0px;
                              "
                              v-if="setObj.typeObj1.bidMargin"
                              >{{
                                setObj.typeObj2.floorPriceUnit_dictText
                              }}</span
                            ></span
                          >
                        </td>
                      </tr>
                      <tr v-if="setObj.typeObj1.priorityType == 1">
                        <td class="title" width="150">优先权行权</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          >
                            {{ setObj.typeObj1.priorityTypeName || "--" }}
                          </span>
                        </td>
                        <td class="title" width="150">是否挂牌公告</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.hangFlag == 1
                                ? "是"
                                : setObj.typeObj1.busType == "3"
                                ? "--"
                                : "否"
                            }}</span
                          >
                        </td>
                      </tr>
                      <tr v-if="setObj.typeObj1.priorityType != 1">
                        <td class="title" width="150">优先权行权</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          >
                            {{ setObj.typeObj1.priorityTypeName || "--" }}
                          </span>
                        </td>
                        <td class="title" width="150">
                          <span style="color: #606266;
    text-align: right;
    background-color: #f9f9f9;
    font-size: 14px;font-weight:normal;" v-show="setObj.typeObj1.priorityType != 1"
                            >优先权角色</span
                          >
                        </td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          >
                            {{ setObj.typeObj1.priorityRuleName || "--" }}
                          </span>
                        </td>
                      </tr>
                      <tr v-if="setObj.typeObj1.priorityType != 1">
                        <td class="title" width="150">
                          <span style="color: #606266;
    text-align: right;
    background-color: #f9f9f9;
    font-size: 14px;font-weight:normal;" v-show="setObj.typeObj1.priorityType != 1"
                            >优先权等级</span
                          >
                        </td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          >
                            {{ setObj.typeObj1.priorityLevelName || "--" }}
                          </span>
                        </td>
                        <td class="title" width="150">是否挂牌公告</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.hangFlag == 1
                                ? "是"
                                : setObj.typeObj1.busType == "3"
                                ? "--"
                                : "否"
                            }}</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">竞价开始时间延迟时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayTime || "--" }}</span
                          ><span
                            v-if="setObj.typeObj1.delayTime"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >天</span
                          >
                        </td>
                        <td class="title" width="150">延迟轮次</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayNum || "--" }}</span
                          ><span
                            v-if="setObj.typeObj1.delayNum"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >轮</span
                          >
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- 在线多轮竞价设置 -->
                <div v-if="setObj.type == 2 || setObj.type == 4">
                  <div
                    class="box-title"
                    style="display: flex; flex-direction: row"
                  >
                    <span style="margin-left: 0px">
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon1.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价设置</span
                      ></span
                    ><nd-button style="margin-left: 12px">{{
                      setObj.stateName
                    }}</nd-button>
                  </div>
                  <table
                    width="100%"
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                    style="margin-top: 15px"
                  >
                    <tbody>
                      <!-- 第一行 -->
                      <tr>
                        <td class="title" width="13%">竞价方式</td>
                        <td width="37%">
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.typeName }}</span
                          >
                        </td>
                        <td class="title" width="150">出价方式</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.type == 1 ||
                              setObj.typeObj1.type == 2
                                ? "正向"
                                : setObj.typeObj1.type == 3 ||
                                  setObj.typeObj1.type == 4
                                ? "反向"
                                : ""
                            }}</span
                          >
                        </td>
                      </tr>
                      <!-- 第二行 -->
                      <tr>
                        <td class="title" width="150">交易底价</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.floorPrice
                            }}{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价开始时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.startTime }}</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">竞价轮数</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.roundNum || "--" }}</span
                          >
                        </td>
                        <td class="title" width="150">每轮时长（秒）</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.roundSecond || "--" }}</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">
                          本轮未出价者是否可参加下一轮出价
                        </td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.nextRoundFlag == 0
                                ? "否"
                                : setObj.typeObj1.nextRoundFlag == 1
                                ? "是"
                                : "--"
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">
                          <span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >最高限价</span
                          >
                        </td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            v-show="
                              setObj.typeObj1.type != 1 &&
                              setObj.typeObj1.type != 2
                            "
                          >
                            --
                          </span>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            v-show="
                              setObj.typeObj1.type == 1 ||
                              setObj.typeObj1.type == 2
                            "
                            >{{ setObj.typeObj1.limitPrice || "--"
                            }}<span
                              style="
                                color: #444444;
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                                margin-left: 0px;
                              "
                              v-if="setObj.typeObj1.limitPrice"
                              >{{
                                setObj.typeObj2.floorPriceUnit_dictText
                              }}</span
                            ></span
                          >
                        </td>
                      </tr>

                      <tr>
                        <td class="title" width="150">是否挂牌公告</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.hangFlag == 1
                                ? "是"
                                : setObj.typeObj1.busType == "3"
                                ? "--"
                                : "否"
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价开始时间延迟时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayTime || "--" }}</span
                          ><span
                            v-if="setObj.typeObj1.delayTime"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >天</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">延迟轮次</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayNum || "--" }}</span
                          ><span
                            v-if="setObj.typeObj1.delayNum"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >轮</span
                          >
                        </td>
                        <td class="title" width="150"></td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                          ></span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 在线抽签设置 -->
                <div v-if="setObj.type == 5">
                  <div
                    class="box-title"
                    style="display: flex; flex-direction: row"
                  >
                    <span style="margin-left: 0px">
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon1.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价设置</span
                      ></span
                    ><nd-button style="margin-left: 12px">{{
                      setObj.stateName
                    }}</nd-button>
                  </div>

                  <table
                    width="100%"
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                    style="margin-top: 15px"
                  >
                    <tbody>
                      <!-- 第一行 -->
                      <tr>
                        <td class="title" width="13%">竞价方式</td>
                        <td width="37%">
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.typeName }}</span
                          >
                        </td>
                        <td class="title" width="150">成交价格</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.floorPrice
                            }}{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span
                          >
                        </td>
                      </tr>
                      <!-- 第二行 -->
                      <tr>
                        <td class="title" width="150">在线抽签时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.startTime }}</span
                          >
                        </td>
                        <td class="title" width="150">延迟轮次</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayNum || "--" }}</span
                          ><span v-if="setObj.typeObj1.delayNum">轮</span>
                        </td>
                      </tr>

                      <tr>
                        <td class="title" width="150">是否挂牌公告</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.hangFlag == 1
                                ? "是"
                                : setObj.typeObj1.busType == "3"
                                ? "--"
                                : "否"
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价开始时间延迟时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayTime || "--" }}</span
                          ><span v-if="setObj.typeObj1.delayTime">天</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- 荷兰式竞价(V型竞价) -->
                <div v-if="setObj.type == 10">
                  <div
                    class="box-title"
                    style="display: flex; flex-direction: row"
                  >
                    <span style="margin-left: 0px">
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon1.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价设置</span
                      ></span
                    ><nd-button style="margin-left: 12px">{{
                      setObj.stateName
                    }}</nd-button>
                  </div>
                  <table
                    width="100%"
                    border="0"
                    cellspacing="0"
                    cellpadding="0"
                    class="detailsbd-tc-s"
                    style="margin-top: 15px"
                  >
                    <tbody>
                      <!-- 第一行 -->
                      <tr>
                        <td class="title" width="13%">竞价方式</td>
                        <td width="37%">
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.typeName }}</span
                          >
                        </td>
                        <td class="title" width="150">底价</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.floorPrice || "--"
                            }}<span v-if="setObj.typeObj1.floorPrice">{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span></span
                          >
                        </td>
                      </tr>
                      <!-- 第二行 -->
                      <tr>
                        <td class="title" width="150">保留价格</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.reducePrice || "--"
                            }}<span v-if="setObj.typeObj1.reducePrice">{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span></span
                          >
                        </td>
                        <td class="title" width="150">底价降价周期</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                              flex-direction: row;
                              align-items: center;
                            "
                          >
                            <nd-input
                              width="100px"
                              style="margin-right: 10px"
                              disabled
                              v-model="formRespons.obj4.s"
                              placeholder="请输入"
                              clearable
                            ></nd-input
                            ><span style="margin-right: 10px">小时</span>
                            <nd-input
                              width="100px"
                              style="margin-right: 10px"
                              disabled
                              v-model="formRespons.obj4.f"
                              placeholder="请输入"
                              clearable
                            ></nd-input
                            ><span style="margin-right: 10px">分</span>
                            <nd-input
                              width="100px"
                              style="margin-right: 10px"
                              disabled
                              v-model="formRespons.obj4.m"
                              placeholder="请输入"
                              clearable
                            ></nd-input
                            ><span style="margin-right: 10px">秒</span>
                          </span>
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">每轮降价金额</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.roundPrice || "--"
                            }}<span v-if="setObj.typeObj1.roundPrice">{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span></span
                          >
                        </td>
                        <td class="title" width="150">竞价开始时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.startTime }}</span
                          >
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">限时竞价时长（秒）</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.postponeSecond }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价幅度</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.bidMargin || "--"
                            }}<span v-if="setObj.typeObj1.bidMargin">{{
                              setObj.typeObj2.floorPriceUnit_dictText
                            }}</span></span
                          >
                        </td>
                      </tr>

                      <tr>
                        <td class="title" width="150">是否挂牌公告</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{
                              setObj.typeObj1.hangFlag == 1
                                ? "是"
                                : setObj.typeObj1.busType == "3"
                                ? "--"
                                : "否"
                            }}</span
                          >
                        </td>
                        <td class="title" width="150">竞价开始时间延迟时间</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayTime || "--" }}</span
                          ><span v-if="setObj.typeObj1.delayTime">天</span>
                        </td>
                      </tr>
                      <tr>
                        <td class="title" width="150">延迟轮次</td>
                        <td>
                          <span
                            class="text1"
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            >{{ setObj.typeObj1.delayNum || "--" }}</span
                          ><span v-if="setObj.typeObj1.delayNum">轮</span>
                        </td>
                        <td class="title" width="150"></td>
                        <td>
                          <span class="text1"></span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- 竞价人信息 -->
                <div
                  v-if="
                    setObj.status == 71 ||
                    setObj.status == 74 ||
                    setObj.status == 63 ||
                    setObj.status == 73
                  "
                  style="margin-top: 15px"
                >
                  <div class="box-title">
                    <span>
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon2.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价人信息</span
                      ></span
                    >
                  </div>
                  <nd-table
                    style="height: 100%; width: 100%; margin-top: 15px"
                    :data="listData.list1"
                  >
                    <el-table-column
                      align="center"
                      prop="bidNo"
                      label="编号"
                      min-width="140"
                    />
                    <el-table-column
                      align="center"
                      prop="name"
                      label="名称"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow3 == false">{{
                          row.name
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.name2 }}</i>
                        <el-icon
                          v-if="row.nameCiphertext && row.isShow3 == false"
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.nameCiphertext && row.isShow3 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="certNo"
                      label="证件号码"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow2 == false">{{
                          row.certNo
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.certNo2 }}</i>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == false"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="contactPhone"
                      label="联系电话"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow == false">{{
                          row.contactPhone
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.contactPhone2 }}</i>
                        <el-icon
                          v-if="
                            row.contactPhoneCiphertext && row.isShow == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="
                            row.contactPhoneCiphertext && row.isShow == true
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="identityType"
                      label="竞价人类型"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn">{{ row.identityTypeName }}</i>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="priorityLevel"
                      label="优先权序号"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn">{{ row.hasPriority===1?row.priorityLevel:'无优先权人' }}</i>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column
                      align="center"
                      prop="priorityLevel"
                      label="优先权序号"
                      min-width="140"
                    /> -->
                    <!-- 10 -->
                  </nd-table>
                </div>
                <!-- 竞价结果信息 -->
                <div
                  v-if="setObj.status == 74 || setObj.status == 73"
                  style="margin-top: 15px"
                >
                  <div class="box-title">
                    <span>
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon3.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >竞价结果信息</span
                      ></span
                    >
                  </div>
                  <nd-table
                    style="height: 100%; width: 100%; margin-top: 15px"
                    :data="listData.list2"
                  >
                    <el-table-column align="center" prop="state" label="状态">
                      <template #default="{ row }">
                        <i class="operateBtn">{{ row.typeName }}</i>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="bidderNo"
                      label="编号"
                      min-width="140"
                    />
                    <el-table-column
                      align="center"
                      prop="bidderName"
                      label="名称"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow3 == false">{{
                          row.bidderName
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.bidderName2 }}</i>
                        <i
                          v-show="row.priorityFlag == 1"
                          style="
                            padding: 0px 3px;
                            display: inline-block;
                            background: red;
                            color: #fff;
                            border-radius: 5px;
                          "
                          >优先权</i
                        >
                        <el-icon
                          v-if="
                            row.bidderNameCiphertext && row.isShow3 == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick(2, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.bidderNameCiphertext && row.isShow3 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick2(2, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="certNo"
                      label="证件号码"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow2 == false">{{
                          row.certNo
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.certNo2 }}</i>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == false"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="contactPhone"
                      label="联系电话"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow == false">{{
                          row.contactPhone
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.contactPhone2 }}</i>
                        <el-icon
                          v-if="
                            row.contactPhoneCiphertext && row.isShow == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="
                            row.contactPhoneCiphertext && row.isShow == true
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="bidPrice"
                      :label="
                        '成交价格' +
                        '(' +
                        setObj.typeObj2.floorPriceUnit_dictText +
                        ')'
                      "
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i>{{ row.bidPrice || "--" }}</i>
                      </template>
                    </el-table-column>
                  </nd-table>
                </div>
                <!-- 出价记录 -->
                <div
                  v-if="setObj.status == 74 || setObj.status == 73"
                  style="margin-top: 15px"
                >
                  <div class="box-title">
                    <span>
                      <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon4.png"
                                    alt="" /> -->
                      <span
                        style="margin-left: 0; color: #606266; font-size: 14px"
                        >出价记录</span
                      ></span
                    >
                  </div>
                  <nd-table
                    style="height: 100%; width: 100%; margin-top: 15px"
                    :data="listData.list3"
                  >
                    <el-table-column
                      align="center"
                      prop="bidTime"
                      label="出价时间"
                      min-width="140"
                    />
                    <el-table-column
                      align="center"
                      prop="bidStage"
                      label="阶段"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn">{{
                          row.bidStage == "1"
                            ? "自由竞价"
                            : row.bidStage == "2"
                            ? "限时竞价"
                            : row.bidStage == "3"
                            ? "底价竞价期"
                            : ""
                        }}</i>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="bidderNo"
                      label="编号"
                      min-width="140"
                    />
                    <el-table-column
                      align="center"
                      prop="bidderName"
                      label="名称"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow3 == false">{{
                          row.bidderName
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.bidderName2 }}</i>
                        <i
                          v-show="row.priorityFlag == 1"
                          style="
                            padding: 0px 3px;
                            display: inline-block;
                            background: red;
                            color: #fff;
                            border-radius: 5px;
                          "
                          >优先权</i
                        >
                        <el-icon
                          v-if="
                            row.bidderNameCiphertext && row.isShow3 == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick(2, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.bidderNameCiphertext && row.isShow3 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="nameClick2(2, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="certNo"
                      label="证件号码"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow2 == false">{{
                          row.certNo
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.certNo2 }}</i>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == false"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick(1, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.certNoCiphertext && row.isShow2 == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="certNoClick2(1, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="bidderPhone"
                      label="联系电话"
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i class="operateBtn" v-if="row.isShow == false">{{
                          row.bidderPhone
                        }}</i>
                        <i class="operateBtn" v-else>{{ row.bidderPhone2 }}</i>
                        <el-icon
                          v-if="
                            row.bidderPhoneCiphertext && row.isShow == false
                          "
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick(2, row)"
                        >
                          <Hide />
                        </el-icon>
                        <el-icon
                          v-if="row.bidderPhoneCiphertext && row.isShow == true"
                          style="margin-left: 10px; cursor: pointer"
                          @click="telClick2(2, row)"
                        >
                          <View />
                        </el-icon>
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="bidPrice"
                      :label="
                        '出价金额' +
                        '(' +
                        setObj.typeObj2.floorPriceUnit_dictText +
                        ')'
                      "
                      min-width="140"
                    >
                      <template #default="{ row }">
                        <i>{{ row.bidPrice || "--" }}</i>
                      </template>
                    </el-table-column>
                  </nd-table>
                </div>
                <!-- 取消记录 -->
                <!-- <div v-if="setObj.status == 63" style="margin-top:15px;">
                        <div class="box-title"><span>
                                    <span style="margin-left:0;color:#606266;font-size:14px;">取消记录</span></span></div>
                        <nd-table style="height: 100%;width: 100%;margin-top:15px;" :data="listData.list4">
                            <el-table-column align="center" prop="cancelTime" label="操作时间" min-width="140" />
                            <el-table-column align="center" prop="cancelUserName" label="操作人员" min-width="140" />
                            <el-table-column align="center" prop="cancelReason" label="取消原因" min-width="140" />
                        </nd-table>
                    </div> -->
                <!-- <div v-if="setObj.terminationFlag == 1 || setObj.terminationFlag == '1'">
                        <div class="box-title"><span><img src="@/assets/images/biddingSupervisionDetailView/icon6.png"
                                    alt="" /><span>终止信息</span></span></div>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="detailsbd-tc-s">
                            <tbody>
                                <tr>
                                    <td class="title" width="150">操作人员</td>
                                    <td>
                                        <span class="text1"
                                            style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                setObj.endAnnDetailVos.userName }}</span>
                                    </td>
                                    <td class="title" width="150">操作时间</td>
                                    <td>
                                        <span class="text1"
                                            style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                setObj.endAnnDetailVos.insertTime }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title" width="150">终止原因</td>
                                    <td colspan="3">
                                        <span class="text1">{{ setObj.endAnnDetailVos.endReason }}</span>
                                    </td>
                                </tr>
                                <tr v-for="(item, index) in formRespons.uploadList3" :key="index">
                                    <td class="title" width="150">{{ item.fileName }}</td>
                                    <td colspan="3">
                                        <ndb-upload disabled v-model="item.fileList" style="margin-top: 12px;"
                                            :uploadParams="{ busId: projectInfor.id, configFileId: item.id }"></ndb-upload>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div> -->
              </div>

              <!-- 11 -->
            </div>
            <!-- <div v-if="setObj.status == 63" style="margin-top:15px;">
                        <div class="box-title"><span>
                                    <span style="margin-left:0;color:#606266;font-size:14px;">取消记录</span></span></div>
                        <nd-table style="height: 100%;width: 100%;margin-top:15px;" :data="listData.list4">
                            <el-table-column align="center" prop="cancelTime" label="操作时间" min-width="140" />
                            <el-table-column align="center" prop="cancelUserName" label="操作人员" min-width="140" />
                            <el-table-column align="center" prop="cancelReason" label="取消原因" min-width="140" />
                        </nd-table>
                    </div> -->
            <div class="form-box-title-tender" v-if="setObj.status == 63">
              <div style="margin-top: 15px">
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >取消交易</span
                >
              </div>
              <nd-table
                style="height: 100%; width: 100%; margin-top: 15px"
                :data="listData.list4"
              >
                <el-table-column
                  align="center"
                  prop="cancelTime"
                  label="操作时间"
                  min-width="140"
                />
                <el-table-column
                  align="center"
                  prop="cancelUserName"
                  label="操作人员"
                  min-width="140"
                />
                <el-table-column
                  align="center"
                  prop="cancelReason"
                  label="取消原因"
                  min-width="140"
                />
              </nd-table>
            </div>

            <div
              class="form-box-title-tender"
              v-if="information.infor3 != 'null' && information.infor3 != null"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >交易结果</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody v-if="information.infor3.tradeFlag == 1">
                    <tr>
                      <td class="title" width="150">交易结果</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.tradeFlagName }}</span
                        >
                      </td>
                      <td class="title" width="150">交易底价</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.floorPrice
                          }}{{ information.infor3.tradeUnit }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">竞价方式</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.bidTypeName }}</span
                        >
                      </td>
                      <td class="title" width="150">交易地点</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.tradeAddress }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">最终有效交易表</td>
                      <td colspan="3">
                        <nd-table
                          v-if="
                            information.infor3.dealingInfoVoList != 'null' &&
                            information.infor3.dealingInfoVoList != null
                          "
                          style="height: 100%"
                          :data="information.infor3.dealingInfoVoList"
                        >
                          <el-table-column
                            align="center"
                            type="index"
                            width="100"
                            label="序号"
                          />
                          <el-table-column
                            align="center"
                            prop="name"
                            label="流入方"
                          />
                          <!-- <el-table-column
                            align="center"
                            prop="bidPrice"
                            :label="
                              '最终有效出价' +
                              '(' +
                              information.infor3.tradeUnit +
                              ')'
                            "
                          /> -->
                          <el-table-column
                            align="center"
                            prop="bidPrice"
                            :label="
                              '最终有效出价' +
                              '(' +
                              information.infor3.tradeUnit +
                              ')'
                            "
                          >
                          <template #default="{ row }">
                        <i >
                          <!-- {{ row.bidPrice.toFixed(2) }} -->
                          {{ row.bidPrice||row.bidPrice==0?Number(row.bidPrice).toFixed(2):'--' }}
                        </i>
                      </template>
</el-table-column>
<el-table-column
                            align="center"
                            prop="upper"
                            label="大写"
                          >
                          <template #default="{ row }">
                        <i >
                          <!-- {{ row.bidPrice.toFixed(2) }} -->
                          {{ row.upper?row.upper:'--' }}
                        </i>
                      </template>
                          </el-table-column>
                          <!-- <el-table-column
                            align="center"
                            prop="upper"
                            label="大写"
                          /> -->
                          <el-table-column
                            align="center"
                            prop="order"
                            label="成交方顺序"
                          />
                        </nd-table>
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">最终成交人</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.traderPerson }}</span
                        >
                      </td>
                      <td class="title" width="150">成交价（{{information.infor3.tradeUnit}}）</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.bidPrice }} <span v-if="information.infor3.bidPrice" style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;margin-left:0px;
                          "></span> {{ information.infor3.bidPriceMax }} </span
                        >
                      </td>
                    </tr>
                     <tr>
                       <td class="title" width="150">成交总金额</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                        >
                          <!-- {{ information.infor3.tradeAmount.toFixed(2)
                          }} -->
                          {{
                            information.infor3.tradeAmount
                              ? Number(information.infor3.tradeAmount).toFixed(
                                  2
                                )
                              : "--"
                          }}
                          <span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0;
                            "
                            v-if="information.infor3.tradeAmount"
                            >元</span
                          ></span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">溢价总金额</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.overflowAmount
                          }}<span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0;
                            "
                            v-if="
                              information.infor3.overflowAmount ||
                              information.infor3.overflowAmount == 0
                            "
                            >元</span
                          ></span
                        >
                      </td>
                      <td class="title" width="150">成交日期</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.tradeTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">溢价率(%)</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.overflowScale }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">补充说明</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.remark }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">附件信息</td>
                      <td colspan="3">
                        <div
                          v-for="(item, index) in information.infor3.fileList"
                          :key="index"
                        >
                          <span>{{ item.fileTypeName }}</span>
                          <div v-if="isUpload && bidStatus">
                            <ndb-upload
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: information.infor3.id,
                                configFileId: item.configFileId,
                              }"
                              @successCallback="
                                successCallback($event, {
                                  operationTitle: `${tabName}-交易结果`,
                                  operationContent: item.fileTypeName,
                                })
                              "
                            ></ndb-upload>
                          </div>
                          <div v-else>
                            <ndb-upload
                              disabled
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                            ></ndb-upload>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>

                  <tbody v-if="information.infor3.tradeFlag == 2">
                    <tr>
                      <td class="title" width="150">交易结果</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.tradeFlagName }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">未成功原因</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{
                            information.infor3.reason == "1"
                              ? "转出方违约"
                              : information.infor3.reason == "2"
                              ? "转入方违约"
                              : information.infor3.reason == "3"
                              ? "其他"
                              : information.infor3.reason == "1,2"
                              ? "转出方违约,转入方违约"
                              : information.infor3.reason == "1,3"
                              ? "转出方违约,其他"
                              : information.infor3.reason == "2,3"
                              ? "转入方违约,其他"
                              : information.infor3.reason == "1,2,3" ||
                                information.infor3.reason == "2,1,3"
                              ? "转出方违约,转入方违约,其他"
                              : ""
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">违约人</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.defaultPerson }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">备注</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor3.remark }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- <div class="form-box-title-tender"> -->
            <div
              class="form-box-title-tender"
              v-if="information.infor4 != 'null' && information.infor4 != null"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >公示信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <!-- 第一行 -->
                    <tr>
                      <td class="title" width="150">公告状态</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor4.announcementStatus }}</span
                        >
                      </td>
                      <td class="title" width="150">公示日期</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor4.publicityDate }}</span
                        >
                      </td>
                    </tr>
                    <!-- <tr>
                                        <td class="title" width="150"> 公示期</td>
                                        <td>
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                    information.infor4.publicDuration }}</span>
                                        </td>
                                        <td class="title" width="150">结果公告文书</td>
                                        <td>
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                    information.infor4.publicityDate }}</span>
                                        </td>
                                    </tr> -->
                    <tr>
                      <td class="title" width="150">公示期</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor4.publicDuration
                          }}<span
                            style="
                              color: #444444;
                              font-size: 14px;
                              font-style: normal;
                              font-weight: normal;
                              margin-left: 0px;
                            "
                            v-if="information.infor4.publicDuration"
                            >天</span
                          ></span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">公示期时间单位</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          v-if="information.infor4.isWorkDay === 0"
                          >自然日</span
                        >
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          v-if="information.infor4.isWorkDay === 1"
                          >工作日</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        !isUpload || getDocList.arry2.length === 0 || !bidStatus
                      "
                    >
                      <td class="title" width="150">结果公告文书</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          @click="writ(2, item, '预览文书')"
                          v-for="item in getDocList.arry2"
                          :key="item"
                          style="
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            cursor: pointer;
                            margin-left: 10px;
                          "
                          >{{ item.modelName }}</span
                        >
                      </td>
                    </tr>

                    <template v-for="(item, index) in getDocList.arry2">
                      <tr
                        v-if="
                          isUpload && getDocList.arry2.length !== 0 && bidStatus
                        "
                        :key="item"
                      >
                        <td
                          v-if="index === 0"
                          class="title"
                          width="150"
                          :rowspan="getDocList.arry2.length"
                        >
                          结果公告文书
                        </td>
                        <td colspan="3">
                          <div
                            style="
                              display: flex;
                              justify-content: space-between;
                              align-items: center;
                              width: 100%;
                            "
                          >
                            <span
                              class="text1"
                              @click="writ(2, item, '预览文书')"
                              style="
                                font-size: 14px;
                                font-style: normal;
                                font-weight: normal;
                                cursor: pointer;
                                margin-left: 10px;
                              "
                              >{{ item.modelName }}</span
                            >
                            <nd-button
                              v-if="item.dtbId == null || item.dtbId == ''"
                              type="createWS"
                              @click="writ(3, item, '生成文书')"
                            ></nd-button>
                            <nd-button
                              v-else
                              type="againCreateWS"
                              @click="writ(3, item, '重新生成')"
                            ></nd-button>
                          </div>
                        </td>
                      </tr>
                    </template>

                    <tr>
                      <td class="title" width="150">操作人</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor4.createName }}</span
                        >
                      </td>
                      <td class="title" width="150">操作时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor4.createTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">公示结果</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{
                            information.infor4.queryFlag == 1
                              ? "有质疑"
                              : "无质疑"
                          }}</span
                        >
                        <span
                          style="float: right; cursor: pointer"
                          @click="
                            lookRecord(information.infor4.questionInfoList)
                          "
                          >公示质疑记录</span
                        >
                      </td>
                    </tr>
                    <!-- <tr v-if="information.infor4.queryFlag != 'null' && information.infor4.queryFlag != null"
                                        v-show="information.infor4.queryFlag == 1">
                                        <td class="title" width="150">质疑描述</td>
                                        <td colspan="3">
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                    information.infor4.questionText }}</span>
                                        </td>
                                    </tr>
                                    <tr v-if="information.infor4.queryFlag != 'null' && information.infor4.queryFlag != null"
                                        v-show="information.infor4.queryFlag == 1">
                                        <td class="title" width="150">附件材料</td>
                                        <td colspan="3">
                                            <ndb-upload disabled v-model="information.infor4.fileList"
                                                :uploadParams="{ busId: projectInfor.id }"></ndb-upload>
                                        </td>
                                    </tr>
                                    <tr v-show="information.infor4.queryFlag == 1">
                                        <td class="title" width="150">质疑人</td>
                                        <td colspan="3">
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                    information.infor4.questionUserName }}</span>
                                        </td>
                                    </tr>
                                    <tr v-show="information.infor4.queryFlag == 1">
                                        <td class="title" width="150">质疑结果</td>
                                        <td colspan="3">
                                            <span class="text1"
                                                style="color: #444444;font-size: 14px;font-style: normal;font-weight: normal;">{{
                                                    information.infor4.status }}</span>
                                        </td>
                                    </tr> -->
                  </tbody>
                </table>
              </div>
            </div>
            <div class="form-box-title-tender" v-if="information.infor5[0]">
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >合同信息</span
                >
              </div>
              <div style="margin-top: 15px">
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody style="display: flex; flex-wrap: wrap">
                    <!-- 第一行 -->
                    <tr style="width: 50%">
                      <td class="title" width="360">项目名称</td>
                      <td style="width: 75%">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor6.projectName }}</span
                        >
                      </td>
                    </tr>
                    <tr style="width: 50%">
                      <td class="title" width="360">项目编号</td>
                      <td style="width: 75%">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor6.projectCode }}</span
                        >
                      </td>
                    </tr>
                    <tr style="width: 50%">
                      <td class="title" width="360">标段编号</td>
                      <td style="width: 75%">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor6.tendersCode }}</span
                        >
                      </td>
                    </tr>
                    <tr style="width: 50%">
                      <td class="title" width="360">流转方式</td>
                      <td style="width: 75%">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor6.tradeType }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-for="(item, index) in information.infor5[0]
                        .configFormParamVoList"
                      :key="index"
                      style="width: 50%"
                    >
                      <td class="title" width="360">
                        {{ item.paramName }}
                      </td>
                      <td style="width: 75%">
                        <span
                          class="text1"
                          v-if="item.paramKey == 'circulationPeriodYear'"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ item.defaultValue }}年{{
                            item.optionSetValue
                          }}月</span
                        >
                        <span
                          class="text1"
                          v-else-if="
                            item.controlType == 11 || item.controlType == 14
                          "
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ item.defaultValue
                          }}{{ item.optionSetValue }}</span
                        >

                        <span
                          @click="mapClick(item)"
                          class="text1 operateBtn"
                          v-else-if="
                            item.paramKey == 'location' && item.defaultValue
                          "
                          style="
                            color: #0b8df1;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                            margin-left: 0px;
                            cursor: pointer;
                          "
                          >{{ item.defaultValue[2] }}</span
                        >
                        <span
                          class="text1"
                          v-else
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ item.defaultValue }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div
              class="form-box-title-tender"
              v-if="information.infor7 && information.infor7.abortPerson"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >终止信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <tr>
                      <td class="title" width="150">操作人员</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.abortPerson }}</span
                        >
                      </td>
                      <td class="title" width="150">操作时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.abortTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">终止原因</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.abortReason }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">附件信息</td>
                      <td colspan="3">
                        <div v-for="(item, index) in stopFileList" :key="index">
                          <span>{{ item.fileName }}</span>
                          <div v-if="isUpload && bidStatus">
                            <ndb-upload
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                              @successCallback="
                                successCallback($event, {
                                  operationTitle: `${tabName}-终止信息`,
                                  operationContent: item.fileName,
                                })
                              "
                            ></ndb-upload>
                          </div>
                          <div v-else>
                            <ndb-upload
                              disabled
                              v-model="item.fileList"
                              :uploadParams="{
                                busId: projectInfor.id,
                                configFileId: item.id,
                              }"
                            ></ndb-upload>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              class="form-box-title-tender"
              v-if="information.infor7 && information.infor7.retryFlag"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >项目重发信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <tr>
                      <td class="title" width="150">操作人员</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.retryOperator }}</span
                        >
                      </td>
                      <td class="title" width="150">操作时间</td>
                      <td>
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.retryTime }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              class="form-box-title-tender"
              v-if="information.infor7 && information.infor7.faiBidFlag == 0"
            >
              <div style="margin-top: 15px">
                <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                <span style="margin-left: 0; color: #606266; font-size: 14px"
                  >流标信息</span
                >
              </div>
              <div>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  class="detailsbd-tc-s"
                  style="margin-top: 15px"
                >
                  <tbody>
                    <tr>
                      <td class="title" width="150">流标时间</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.faiBidTime }}</span
                        >
                      </td>
                    </tr>
                    <tr>
                      <td class="title" width="150">流标原因</td>
                      <td colspan="3">
                        <span
                          class="text1"
                          style="
                            color: #444444;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: normal;
                          "
                          >{{ information.infor7.faiBidReason }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div style="text-align: center; margin-top: 20px" v-if="btnShow">
          <nd-button icon="back" class="iconColor" @click="goBack()"
            >返&nbsp;回</nd-button
          >
        </div>

        <!-- 新增部分 -->
        <div style="text-align: center; padding: 20px 0" v-if="checkBtn">
          <nd-button
            type="primary"
            icon="Check"
            class="iconColor"
            @click="handleCheck"
            >核查完毕</nd-button
          >
          <nd-button icon="Close" class="iconColor" @click="goBack()"
            >取消</nd-button
          >
        </div>

        <!-- <div class="posotion" v-if="btnShow">
          <nd-button icon="back" @click="goBack()">返&nbsp;回</nd-button>
        </div> -->
      </div>
      <!-- 右侧导航锚点 -->
      <!-- <el-affix :offset="100" v-if="rightShow"> -->
      <div class="project-box" v-if="rightShow">
        <div class="project-registration-right" v-if="rightShow">
          <!-- <div style="width:100%;height:50px;" v-if="lcShow"></div> -->
          <div class="project-registration-right-middle">
            <div class="project-registration-middle-item">
              <!-- <img src="@/assets/images/projectRegistration/icon1.png" alt=""> -->
              <span
                style="
                  color: #606266;
                  font-size: 14px;
                  font-weight: bold;
                  margin-left: 15px;
                "
                >项目历程</span
              >
            </div>
            <el-timeline style="padding: 10px 15px">
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size"
                :hollow="activity.hollow"
              >
                <div style="font-size: 14px; color: #444444">
                  <span v-if="activity.tenderCode">标段</span
                  >{{ activity.tenderCode }}{{ activity.content }}
                </div>
                <div style="font-size: 13px; color: #909399; margin-top: 10px">
                  {{ activity.type == 2 ? "审核人：" : "操作人："
                  }}{{ activity.operator }}
                </div>
                <div style="font-size: 13px; color: #909399; margin-top: 10px">
                  操作时间：{{ activity.timestamp }}
                </div>
                <div
                  style="font-size: 13px; color: #909399; margin-top: 10px"
                  v-if="activity.type == 2"
                >
                  审核结果：{{ activity.status }}
                </div>
                <div
                  style="
                    font-size: 13px;
                    color: #909399;
                    margin-top: 10px;
                    overflow-wrap: break-word;
                  "
                  v-if="activity.type == 2"
                >
                  审核意见：{{
                    activity.opinion == "" ? "同意" : activity.opinion
                  }}
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
      <!-- </el-affix> -->
      <!-- 文书弹框 -->
      <documentDialog
        ref="documentModifyView"
        @refresh="getDocListFun"
        @docCallback="docCallback($event)"
      />
      <!-- 地图选址 -->
      <mapRecord @getAddress="getAddress" :btnShow="false" ref="mapRecordRef"></mapRecord>
      <!-- 查看报名人 -->
      <under-apply-people
        ref="underApplyPeopleRef"
        :isUpload="isUpload"
        :tabName="tabName"
        :bidStatus="bidStatus"
      />
      <!-- 查看质疑记录 -->
      <recordDialog
        ref="recordDialogRef"
        :isUpload="isUpload"
        :tabName="tabName"
        :bidStatus="bidStatus"
      />
      <!-- 查看公告历史记录 -->
      <historyDialog
        ref="historyDialogRef"
        :isUpload="isUpload"
        :tabName="tabName"
        :bidStatus="bidStatus"
      />
    </div>
</el-scrollbar>
  </ndb-page>
</template>

<script setup>
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndbPage from "@/components/business/ndbPage2/index.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndInput from "@/components/ndInput.vue";
import ndbControlAdapter from "@/components/business/ndbControlAdapter.vue";
import ndTabs from "@/components/ndTabs.vue";
import ndTable from "@/components/ndTable.vue";
import documentDialog from "@/components/business/ndbWordDocument/index.vue";
// import mapRecord from "../../projectRegistrationDetailView/components/mapRecord.vue"; //地图选址
import mapRecord from "../../projectRegistrationEditorView/components/mapRecord.vue"; //地图选址

import ndbUpload from "@/components/business/ndbUpload2/index.vue";
import underApplyPeople from "../../transactionManageView/components/underApplyPeople.vue";
import recordDialog from "../../projectRegistrationDetailView/components/recordDialog.vue";
import historyDialog from "../../projectRegistrationDetailView/components/historyDialog.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  nextTick,
  toRaw,
  shallowRef,
} from "vue";
import { useRouter } from "vue-router";
import { Check } from "@element-plus/icons-vue";
import imgTest0 from "@/assets/images/projectRegistration/icons/vector0.png";
import imgTest1 from "@/assets/images/projectRegistration/icons/vector1.png";
import imgTest2 from "@/assets/images/projectRegistration/icons/vector2.png";
import imgTest3 from "@/assets/images/projectRegistration/icons/vector3.png";
import imgTest4 from "@/assets/images/projectRegistration/icons/vector4.png";
import imgTest5 from "@/assets/images/projectRegistration/icons/vector5.png";
import imgTest6 from "@/assets/images/projectRegistration/icons/vector6.png";
import imgTest7 from "@/assets/images/projectRegistration/icons/vector7.png";
import imgTest8 from "@/assets/images/projectRegistration/icons/vector8.png";
import ndPagination from "@/components/ndPagination.vue";

// import { set } from "ol/transform";
const props = defineProps({
  params: {
    type: Object,
    default: {},
  },
  id: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "",
  },
  tradeMode: {
    type: Number,
    default: "",
  },
  rightShow: {
    type: Boolean,
    default: true,
  },
  btnShow: {
    type: Boolean,
    default: true,
  },
  locationShow: {
    type: Boolean,
    default: true,
  },
  lcShow: {
    type: Boolean,
    default: false,
  },
  // 新增部分
  checkBtn: {
    type: Boolean,
    default: false,
  },
  // 新增部分
  isUpload: {
    type: Boolean,
    default: false,
  },
});
const img = reactive({
  arry: [
    imgTest0,
    imgTest1,
    imgTest2,
    imgTest3,
    imgTest4,
    imgTest5,
    imgTest6,
    imgTest7,
    imgTest8,
  ],
});
const router = useRouter();
const $axios = inject("$axios");
let tradeMode = ref(0);
const fileList = ref([]);
const fileList2 = ref([]);
let address = reactive({
  x: "",
  y: "",
  val: "",
});
const proForm = reactive({
    pageNo2: 1,
    pageSize2: 10,
    pageNo3: 1,
    pageSize3: 10,
})
const indexMethod = (index) => {//序号
  return (proForm.pageNo2 - 1) * proForm.pageSize2 + index + 1
  // if (index == 0) {
  //   return 1;
  // } else {
  //   return index + 1;
  // }
};
const indexMethod2= (index) => {//序号
  return (proForm.pageNo3 - 1) * proForm.pageSize3 + index + 1
  // if (index == 0) {
  //   return 1;
  // } else {
  //   return index + 1;
  // }
};

const page = reactive({
    total2: '',
    total3: '',
})

const recordDialogRef = ref(null);
function lookRecord(row) {
  if (row && row.length != 0) {
    recordDialogRef.value.open(row);
  } else {
    ElMessage.error("无质疑记录");
  }
}
const historyDialogRef = ref(null);
function historyClick(row) {
  $axios({
    url:
      "/announcement/historyAnnouncement?proId=" +
      projectInfor.id +
      "&tenderId=" +
      projectInfor.tenderId,
    method: "get",
  }).then((res) => {
    if (res.data.code === 200 && res.data.data && res.data.data.length != 0) {
      historyDialogRef.value.open(res.data.data, projectInfor.id);
    } else {
      ElMessage.error("无公告历史记录");
    }
  });
  // historyDialogRef.value.open()
}
const mapRecordRef = ref(null);
const mapClick = (items) => {
  address.x = items.defaultValue[0];
  address.y = items.defaultValue[1];
  address.val = items.defaultValue[2];
  console.log(address);
  mapRecordRef.value.open(address);
};
let existSignupFee = ref("");
let activities = ref([]);
let encryptOld = ref("");
function certNoClick(th, rowObj) {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.certNoCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow2 = true;
        rowObj.certNo2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.certNoCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow2 = true;
        rowObj.certNo2 = res.data.data;
      }
    });
  } else if (th == 3) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.certNoEncrypt);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow2 = true;
        rowObj.certNo2 = res.data.data;
      }
    });
  }
}
const certNoClick2 = (th, rowObj) => {
  rowObj.isShow2 = false;
};
function certNoClick3(val) {
  val.oldValue = val.defaultValue;
  // encryptOld.value = val.defaultValue;
  let params = new FormData();
  params.append("encryptMessage", val.encrypt);
  $axios({
    method: "post",
    url: "/manageRuleInfo/decrypt",
    data: params,
  }).then((res) => {
    if (res.data.code == 200) {
      val.isShow = true;
      val.defaultValue = res.data.data;
    }
  });
}
function certNoClick4(val) {
  val.isShow = false;
  val.defaultValue = val.oldValue;
  // val.defaultValue = encryptOld.value;
}
let phoneShow = ref(false);
let phoneShow2 = ref(false);

const phoneClick = (th, name, name2) => {
  if (th == 1) {
    phoneShow.value = true;
    let params = new FormData();
    params.append("encryptMessage", name);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        information.infor1.contactPerson2 = res.data.data;
      }
    });
  } else if (th == 2) {
    information.infor1.contactPerson = name2;
    phoneShow.value = false;
  }
  if (th == 3) {
    phoneShow2.value = true;
    let params = new FormData();
    params.append("encryptMessage", name);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        information.infor1.contactPhone2 = res.data.data;
      }
    });
  } else if (th == 4) {
    information.infor1.contactPhone = name2;
    phoneShow2.value = false;
  }
};
const nameClick = (th, rowObj) => {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.nameCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow3 = true;
        rowObj.name2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.bidderNameCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow3 = true;
        rowObj.bidderName2 = res.data.data;
      }
    });
  } else if (th == 3) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.nameEncrypt);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow3 = true;
        rowObj.bidderName2 = res.data.data;
      }
    });
  }
};
const nameClick2 = (th, rowObj) => {
  rowObj.isShow3 = false;
};
const telClick = (th, rowObj) => {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.contactPhoneCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow = true;
        rowObj.contactPhone2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.bidderPhoneCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow = true;
        rowObj.bidderPhone2 = res.data.data;
      }
    });
  }
};
const telClick2 = (th, rowObj) => {
  rowObj.isShow = false;
};
const editableTabsValue = ref("0");
const editableTabs = ref([]);
const underApplyPeopleRef = ref(null);
function lookRegister(row) {
  underApplyPeopleRef.value.open({
    id: row.id,
    projectId: row.projectId,
    unitId: projectInfor.unitId,
    childTradeVariety: getDocList.jypz,
  });
}
let tabList = ref(["项目信息"]);
let pagedDataAll=ref([])
let pagedDataAll2=ref([])
let pagedData=ref([])
let pagedData2=ref([])
let projectInfor = reactive({
  id: "", //项目id00123c2847c5492abc8c40ebf69b7d6c
  tenderId: "", //标段id
  forms: {},
  unitId: "",
  panoramaPictureUrl: "",
  panoramaVideoUrl: "",
  LZFS: "",
});
// projectInfor.id = router.currentRoute.value.query.id;
// projectInfor.id = props.id;
const emits = defineEmits(["back"]); //定义方法名
let matterFlag = ref("");
const goBack = () => {
  activities.value = [];
  stopFileList.value = [];
  activeName.value = "0";
  editableTabsValue.value = "0";
  information.infor1 = {};
  (information.infor2 = {}), (information.infor3 = {});
  information.infor4 = {};
  information.infor5 = [];
  information.formModule = [];
  information.infor6 = {};
  information.infor7 = {};
  existSignupFee.value = "";
  formRespons.bdId = [];
  formRespons.list = [];
  emits("back");
  mainBoxRef.value.scrollTop = 0;
  pageRef.value.back();
  // router.go(-1);
};
let dataId = ref("");
const writ = (type, item, title) => {
  if (type == 1) {
    documentModifyView.value.open(
      dataId.value,
      item.id,
      "4",
      type,
      title,
      props.params.projectId
    );
  } else if (type == 2) {
    if (item.dtbId) {
      documentModifyView.value.open(
        dataId.value,
        item.dtbId,
        "4",
        type,
        title,
        props.params.projectId
      );
    } else {
      ElMessage.warning("文书未生成，请生成文书！");
    }
  }
};
const activeName = ref("0"); //tab栏切换
let tradeAreaUnit = ref("");
let information = reactive({
  infor1: {},
  infor2: {},
  infor3: {},
  infor4: {},
  infor6: {},
  infor7: {},
  infor5: [],
  formModule: [],
  publicInfo: {},
});
let imgShow = reactive({
  falg1: true,
  falg2: true,
  falg3: true,
});
function showDetail(objs) {
  objs.isShow = !objs.isShow;
}
function showDetail2() {
  imgShow.falg1 = !imgShow.falg1;
}
function showDetail3() {
  imgShow.falg2 = !imgShow.falg2;
}
function showDetail4() {
  imgShow.falg3 = !imgShow.falg3;
}
const documentModifyView = ref();
const pushFlag = (th, item) => {
  //文书操作
  if (item.dtbId) {
    documentModifyView.value.open(
      props.params.projectId,
      item.dtbId,
      "0",
      2,
      "生成文书"
    );
  } else {
    ElMessage.error("文书未生成,无法查看");
  }
};
let stopFileList = ref([]);
let matterVo = ref({});
//tab栏目切换
const handleClick = (tab) => {
  console.log(tab.index);
  activeName.value = tab.props.name == 0 ? "0" : "1";
  information.infor1 = null;
  information.infor2 = null;
  information.infor3 = null;
  information.infor4 = null;
  information.infor5 = [];
  information.formModule = [];
  information.infor6 = null;
  information.infor7 = null;
  stopFileList.value = [];
  existSignupFee.value = "";
  console.log(formRespons.bdId);
  formRespons.bdId.forEach((item, index) => {
    // if (tab.props.name == index + 1) {
    if (tab.index == index + 1) {
      projectInfor.tenderId = item;
      $axios({
        url: "/archivesStore/proTenderViewV2",
        // url: "/project/proTenderView/9b54844fc59b45e2a3f87a97d41ef0f4",
        method: "get",
        params: {
          archivesId: props.params.archivesId,
          projectId: props.params.projectId,
          tenderId: props.params.tenderId,
          auditId: props.params.auditId,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          stopFileList.value = res.data.data.abortFiles;
          setObj.type = res.data.data.bidType;
          setObj.status = res.data.data.flowStatus;
          matterVo.value = res.data.data.formModule[0].matterVo;
          jjInfor(item);

          nextTick(() => {
            res.data.data.formModule.forEach((item2) => {
              if (item2.moduleType == 10) {
                item2.templateParam.forEach((item3, index) => {
                  if (item3.paramKey == "bidType" && matterFlag.value == 1) {
                    pagedDataAll2.value=item2.matterVo.tradeMatterVoList;
                    page.total3=item2.matterVo.tradeMatterVoList.length
                  handleCurrentChange3(1)
                    item3.paramCells = "2";
                    let obj = {
                      paramName: "交易标的物",
                      defaultValue: "1",
                      paramCells: "2",
                    };
                    item2.templateParam.splice(index + 1, 0, obj);
                  }
                });
              }
              if (item2.moduleType == 12) {
                item2.templateParam.forEach((item3) => {
                  item3.isShow = false;
                  if (item3.paramKey == "rentIncRule") {
                    if (item3.defaultValue) {
                      item3.optionList = JSON.parse(item3.defaultValue);
                      console.log(item3.optionList);
                    }
                  }
                });
              }
            });
            information.formModule = res.data.data.formModule;
            // if (res.data.data.abortPerson) {
            //     information.infor7 = res.data.data;
            // }
            information.infor7 = res.data.data;
            if (res.data.data.announcementData) {
              information.infor1 = res.data.data.announcementData;
            }
            if (res.data.data.signUpData) {
              information.infor2 = res.data.data.signUpData;
              information.infor2.signUpList.forEach((item) => {
                item.isShow = false;
                item.isShow2 = false;
                item.isShow3 = false;
                item.contactPhone2 = "";
                item.certNo2 = "";
                item.name2 = "";
              });
            }

            if (res.data.data.signUpData) {
              existSignupFee.value =
                res.data.data.signUpData.existSignupFee || "";
            }
            if (res.data.data.dealResultData) {
              information.infor3 = res.data.data.dealResultData;
            }
            if (res.data.data.publicityQuestionData) {
              information.infor4 = res.data.data.publicityQuestionData;
              dataId.value = res.data.data.publicityQuestionData.id;
              docObj.projectId = res.data.data.publicityQuestionData.projectId;
              docObj.varietyKey =
                res.data.data.publicityQuestionData.varietyKey;
              $axios({
                url: "/docInfo/getDocList",
                method: "post",
                data: {
                  bizId: res.data.data.publicityQuestionData.id,
                  projectId: res.data.data.publicityQuestionData.projectId,
                  flowBaseCode: "8",
                  type: "4",
                  varietyKey: res.data.data.publicityQuestionData.varietyKey,
                },
              }).then((res) => {
                if (res.data.code === 200) {
                  getDocList.arry2 = res.data.data;
                }
              });
            }

            if (res.data.data.contractInitVo) {
              information.infor5 =
                res.data.data.contractInitVo.initTemplateVo
                  .configFormModuleVoList || "";
              information.infor6 = res.data.data.contractInitVo;
            }
          });
        }
      });
    }
  });
};
// let formRespons = reactive({//初始化数据
//     list: [],
//     bdId: []
// })
let formRespons = reactive({
  //初始化数据
  list: [],
  bdId: [],
  obj4: {
    s: "",
    f: "",
    m: "",
  },
  uploadList: [],
  uploadList3: [],
});
let setObj = reactive({
  type: "",
  status: "",
  stateName: "",
  state: "",
  typeObj1: {},
  typeObj2: {},
  list1: [],
  list2: [],
  list3: [],
  list4: [],
  endAnnDetailVos: {},
});
let listData = reactive({
  list1: [],
  list2: [],
  list3: [],
  list4: [],
});

const jjInfor = (id) => {
  $axios({
    method: "get",
    url: "/tenders/getBiddingInfo?tenderId=" + id,
  }).then((res) => {
    if (res.data.code == 200) {
      setObj.status = Number(res.data.data.state);
      listData.list1 = res.data.data.bidderInformationListVos;
      console.log(listData.list1, "listData.list1listData.list1listData.list1");
      listData.list1.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.isShow3 = false;
        item.contactPhone2 = "";
        item.certNo2 = "";
        item.name2 = "";
      });
      listData.list2 = res.data.data.biddingResultInformationVos;
      listData.list2.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.contactPhone2 = "";
        item.isShow3 = false;
        item.bidderName2 = "";
        item.certNo2 = "";
      });
      listData.list3 = res.data.data.onlineBidRecordListVos;
      listData.list3.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.isShow3 = false;
        item.bidderPhone2 = "";
        item.certNo2 = "";
        item.bidderName2 = "";
      });
      listData.list4 = res.data.data.onlineBidCancelListVos;
      setObj.typeObj2 = res.data.data;
      setObj.typeObj1 = res.data.data.onlineRuleInfoVo;
      setObj.endAnnDetailVos = res.data.data.endAnnDetailVos || {};
      setObj.stateName = res.data.data.stateName;
      setObj.state = res.data.data.state;
      setObj.terminationFlag = res.data.data.terminationFlag;
      formatSeconds(setObj.typeObj1.bidPriceDownTime);
      $axios({
        url: "/archivesStore/getTheAttachmentTypeList",
        method: "post",
        data: {
          archivesId: props.params.archivesId,
          projectId: props.params.projectId,
          auditId: props.params.auditId,
          systemType: "1",
          flowBasecode: "999",
        },
      }).then((res) => {
        if (res.data.code === 200) {
          formRespons.uploadList3 = res.data.data;
        }
      });
    }
  });
};
function formatSeconds(seconds) {
  let hour = Math.floor(seconds / 3600);
  let minute = Math.floor((seconds % 3600) / 60);
  let second = Math.floor((seconds % 3600) % 60);
  formRespons.obj4.s = hour;
  formRespons.obj4.f = minute;
  formRespons.obj4.m = second;
}

function handleSizeChange2(val) {
    proForm.pageSize2 = val
    handleCurrentChange2(1)
}
function handleCurrentChange2(val) {
    proForm.pageNo2 = val
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = pagedDataAll.value.slice(startIndex, endIndex);
}
function handleSizeChange3(val) {
    proForm.pageSize3 = val
    handleCurrentChange3(1)
}
function handleCurrentChange3(val) {
    proForm.pageNo3 = val
    const startIndex = (proForm.pageNo3 - 1) * proForm.pageSize3;
    const endIndex = startIndex + proForm.pageSize3;
    pagedData2.value = pagedDataAll2.value.slice(startIndex, endIndex);
}
const getFormData = () => {
  // 初始化项目数据 ===
  tabList.value = [{ title: "项目信息", name: "0", content: "项目信息" }];
  activeName.value = "0";
  editableTabsValue.value = "0";
  information.infor1 = {};
  (information.infor2 = {}), (information.infor3 = {});
  information.infor4 = {};
  information.infor5 = [];
  information.infor6 = {};
  information.infor7 = {};

  information.formModule = [];
  formRespons.bdId = [];
  existSignupFee.value = "";
  $axios({
    url: "/archivesStore/proViewV2",
    method: "get",
    params: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
      auditId: props.params.auditId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      projectInfor.forms = res.data.data.baseProInfo;
      projectInfor.LZFS = res.data.data.baseProInfo.tradeType;
      // projectInfor.unitId=res.data.data.baseProInfo.
      getDocList.jypz = res.data.data.baseProInfo.childTradeVariety;
      getDocListFun();
      getUploadList();
      getUploadList2();
      res.data.data.proTenderList.forEach((item) => {
        let obj = {};
        obj.title = "标段" + item.code;
        obj.name = item.code;
        obj.content = "标段" + item.code;
        // tabList.value.push('标段' + item.code)
        tabList.value.push(obj);
        formRespons.bdId.push(item.id);
      });
      matterFlag.value =
        res.data.data.proTemplateInitVo.initModuleVoList[0].matterFlag;
      formRespons.list = res.data.data.proTemplateInitVo.initModuleVoList;
      formRespons.list.forEach((item, j) => {
        item.isShow = true;
        if (item.childFlag == 1) {
          item.childModuleList.forEach((item2) => {
            item2.templateModleParam[2].templateParamVoList[0].templateParam.forEach(
              (item3) => {
                if (item3.paramKey == "rentIncRule") {
                  if (item3.defaultValue) {
                    item3.optionList = JSON.parse(item3.defaultValue);
                    console.log(item3.optionList);
                  }
                }
              }
            );
          });
        }
        if (item.moduleType == 1) {
          item.templateParamVoList[0].templateParam.forEach((item2, index) => {
            if (item2.paramKey == "tradeArea") {
              tradeAreaUnit.value = item2.optionSetValue;
            }
            if (
              item2.paramKey == "groupCode" &&
              item.moduleType == 1 &&
              item.matterFlag == 1 &&
              item.matterVo
            ) {
              pagedDataAll.value=item.matterVo.tradeMatterVoList;
              page.total2=item.matterVo.tradeMatterVoList.length
              handleCurrentChange2(1)
              item2.paramCells = "2";
              let obj = {
                paramName: "交易标的物",
                defaultValue: "1",
                paramCells: "2",
              };
              item.templateParamVoList[0].templateParam.splice(
                index + 1,
                0,
                obj
              );
            }
          });
        }
      });
    } else {
      ElMessage.error(res.data.msg);
    }
  });
  $axios({
    url: "/archivesStore/projectGetAllFlow",
    method: "get",
    params: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
      auditId: props.params.auditId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      nextTick(() => {
        res.data.data.forEach((item) => {
          let obj = {};
          obj.content = item.nodeName;
          obj.timestamp = item.flowTime;
          obj.tenderCode = item.tenderCode;
          obj.opinion = item.opinion;
          obj.status = item.status;
          obj.operator = item.operator;
          obj.type = item.type;
          obj.size = "large";
          obj.icon = Check;
          obj.color = "#0B8DF1";
          activities.value.push(obj);
        });
      });
    }
  });
};

const getUploadList = () => {
  // 初始图片列表 ===
  $axios({
    url: "/archivesStore/getTheAttachmentTypeList",
    method: "post",
    data: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      auditId: props.params.auditId,
      systemType: "1",
      flowBasecode: "999",
    },
  }).then((res) => {
    if (res.data.code === 200) {
      // formRespons.configFileId1=res.data.data[0].id
      // fileUploadUrl.url1 = '/file/uploadFile?busId=' + projectInfor.id + '&configFileId=' + res.data.data[0].id;
      res.data.data[0].fileList.forEach((item) => {
        let obj = {};
        obj.name = item.fileName;
        obj.url = item.fileUrl;
        obj.id = item.id;
        fileList.value.push(obj);
      });
      formRespons.uploadList = res.data.data;
    }
  });
};
const getUploadList2 = () => {
  // 初始附件信息列表 ===
  $axios({
    url: "/archivesStore/getTheAttachmentTypeList",
    method: "post",
    data: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      auditId: props.params.auditId,
      systemType: "1",
      flowBasecode: "1",
    },
  }).then((res) => {
    if (res.data.code === 200) {
      // fileUploadUrl.url2 = '/file/uploadFile?busId=' + projectInfor.id + '&configFileId=' + res.data.data[0].id;
      res.data.data[0].fileList.forEach((item) => {
        let obj = {};
        obj.name = item.fileName;
        obj.url = item.fileUrl;
        obj.id = item.id;
        fileList2.value.push(obj);
      });
      formRespons.uploadList2 = res.data.data;
    }
  });
};
const data = reactive({
  fileList: [],
  fileList2: [],
});
let getDocList = reactive({
  arry: [],
  arry2: [],
  jypz: "",
});
const getDocListFun = () => {
  // 初始文书列表 ===
  $axios({
    url: "/archivesStore/getDocList",
    method: "post",
    data: {
      archivesId: props.params.archivesId,
      auditId: props.params.auditId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
      flowBaseCode: "1",
      type: "0",
      unitId: projectInfor.unitId,
      // varietyKey: projectInfor.forms.varietyKey
      varietyKey: getDocList.jypz,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      getDocList.arry = res.data.data;
    }
  });
};
const getUrlQuery = () => {
  // 获取全景图信息
  $axios({
    url: "/archivesStore/panoramaPictureUrlQuery",
    method: "post",
    data: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
      auditId: props.params.auditId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      projectInfor.panoramaPictureUrl = res.data.data.panoramaPictureUrl;
      projectInfor.panoramaVideoUrl = res.data.data.panoramaVideoUrl;
    }
  });
};
const pageRef = ref(null);

const getPublicDetail = () => {
  $axios({
    url: "/archivesStore/autoTrade",
    method: "get",
    params: {
      archivesId: props.params.archivesId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
      auditId: props.params.auditId,
    },
  }).then((res) => {
    if (res.data.code === 200) {
      information.publicInfo = res.data.data;
    }
  });
};
const mainBoxRef = ref(null);
const refeshFun = (id, trade) => {
   proForm.pageSize2=10;
  proForm.pageSize3=10;
  mainBoxRef.value.scrollTop = 0;
  // console.log("传递过来的id", id);
  // console.log("传递过来的trade", trade);
  tradeMode.value = trade;
  // 0 委托交易  1  自主交易
  projectInfor.id = id;
  getPublicDetail(); //项目公告公告信息
  getFormData();
  getUrlQuery();
};
const scrollTop = () => {
  mainBoxRef.value.scrollTop = 0;
};

// 新增部分(核查完毕)
const handleCheck = () => {
  console.log(projectInfor.id, "接收的id");
  ElMessageBox.confirm("是否确认核查完毕?", "警告", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      $axios({
        url: "/project/check/save/" + projectInfor.id,
        method: "get",
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success(res.data.msg);
          goBack();
        } else {
          ElMessage.error(res.data.msg);
        }
      });
    })
    .catch(() => {});
};

// 新增部分(上传回调)
const successCallback = (e, textObj) => {
  console.log(e, textObj, "成功回调");
  // 1 添加 2单个删除 3全部清空
  let operationContent = "";
  let operation = "";
  if (e == 1) {
    operation = "添加了";
  } else if (e == 2 || e == 3) {
    operation = "删除了";
  }
  operationContent = operation + "《" + textObj.operationContent + "》";
  let params = {
    proId: projectInfor.id,
    operationTitle: textObj.operationTitle,
    operationContent: operationContent,
  };
  $axios({
    url: "/project/check/recordSave",
    method: "get",
    params,
  }).then((res) => {
    console.log(res, "操作记录");
    if (res.data.code === 200) {
      // ElMessage.success(res.data.msg);
    } else {
      // ElMessage.error(res.data.msg);
    }
  });
};

const back = () => {
  pageRef.value.back();
};

onMounted(() => {
  refeshFun();
});
defineExpose({ refeshFun, scrollTop });
</script>
<style lang="scss" scoped>
i {
  font-style: normal;
}
.posotion {
  position: fixed;
  top: 68px;
  right: 0;
  :deep(.el-button) {
    border-color: transparent;
    background: transparent;
  }
}
.demo-tabs {
  :deep(.el-tabs__active-bar) {
    max-width: 100px;
  }

  :deep(.el-tabs__item) {
    max-width: 100px;
  }

  :deep(.el-tabs__header) {
    margin: 0 0 0;
  }

  :deep(.el-tabs__nav) {
    display: flex;
    justify-content: center;
  }

  :deep(.el-tabs__item.is-active) {
    font-weight: 700;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background: none;
  }
}

:deep(.nd-search-more-item-box) {
  width: 100%;
}

.ndb-page-box {
  :deep(.content) {
    background: #f7f8fa;
  }
}

.nd-search-more-box {
  :deep(.arrow) {
    display: none;
  }
}

.project-registration {
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
//   overflow-x: hidden;
//   overflow-y: auto;

  .action1 {
    width: 100%;
    margin-right: 0px;
  }

  .action2 {
    margin-right: 15px;
    padding-bottom: 15px;
    width: calc(100% - 275px);
  }

  .project-registration-left {
    // flex: 1;
    // width: calc(100% - 260px);
    height: auto;
    // margin-right: 15px;
    position: relative;

    .go-back {
      width: 44px;
      height: 42px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      padding: 10px 8px;

      background: #ffffff;

      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      font-size: 14px;
      color: #0b8df1;
      position: absolute;
      top: 100px;
      right: 40px;
      cursor: pointer;
    }

    .form-box {
      width: 100%;
      height: auto;
      padding: 15px 15px;
      background: #fff;

      .border-card-content {
        :deep(.el-tabs--border-card) {
          width: 96%;
          margin: 0 auto;
          margin-bottom: 12px;
          // border: none;
        }

        :deep(.el-tabs--border-card > .el-tabs__header) {
          // background: none;
          background: #f9f9f9;
        }
        :deep(.el-tabs--border-card > .el-tabs__header) {
          border-bottom: none;
        }

        :deep(.el-tabs__nav) {
          // border: 1px solid #eeeeee;
          // background: #fafafa;
          // left: 15px;
          background: #f9f9f9;
        }

        :deep(.el-tabs__item) {
          // border-top: 1px solid #eeeeee;
        }

        :deep(
            .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active
          ) {
          font-weight: bold;
          font-size: 14px;
        }
      }

      .upload-box {
        width: 100%;
        margin: 0 auto;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        padding: 10px 10px 0px 10px;
        // padding: 10px 10px;

        .upload-box-title {
          font-size: 12px;
          color: #0b8df1;
          font-weight: bold;
          // margin-top: 15px;
        }
      }

      .upload-box-img-box img {
        width: 100px;
        height: 100px;
        margin: 10px 10px;
      }
      .detailsbd-tc-s2 {
        margin-top: 15px;
      }

      .detailsbd-tc-s {
        border-left: 1px solid #eeeeee;
        border-top: 1px solid #eeeeee;
        // margin-top: 15px;

        .width1 {
          width: calc(100% - 180px);
        }

        .width2 {
          width: calc(100% - 180px);
        }

        .width3 {
          width: 180px;
        }

        .width4 {
          width: 180px;
        }

        td {
          border-right: 1px solid #eeeeee;
          border-bottom: 1px solid #eeeeee;
          padding: 7px 8px;
          text-align: left;
          font-size: 14px;
          color: #444444;
          background: #fff;

          .redStar {
            color: red;
          }
        }

        .title {
          color: #606266;
          text-align: right;
          background-color: #f9f9f9;
          font-size: 14px;
        }
      }

      .form-box-title {
        display: flex;
        flex-direction: column;
        .bdw span {
          color: #606266;
          font-size: 14px;
        }

        .form-box-title-tender {
          background-color: #fff;
          margin-top: 15px;
          padding: 0px 15px 15px 15px;
        }

        // flex-direction: row;
        // justify-content: space-between;
        // align-items: center;

        img {
          width: 17px;
          height: 15px;
        }

        span {
          font-size: 16px;
          color: #0b8df1;
          margin-left: 15px;
          font-weight: bold;
        }
      }
    }
  }

  .project-box {
    width: 260px;
    height: 100%;
    // margin-right: 15px;
    position: relative;
    top: 0;
    right: 0;

    .project-registration-right {
      position: absolute;
      width: 260px;
      height: 100%;
      // position: fixed;
      // left: 0px;
      // top: 0px;
      // width: 260px;
      // height: 100%;
      // margin-right: 15px;
      // // margin-top: 15px;
      // background: #fff;

      .project-registration-right-top {
        width: 100%;
        height: 42px;
        display: flex;
        flex-direction: row;
        justify-content: left;
        align-items: center;
        background: linear-gradient(to bottom, #ffffff, #eef8ff);
        border: 1px solid #c6e2ff;
        cursor: pointer;
        margin-top: 15px;

        img {
          width: 14px;
          height: 13px;
          margin-left: 15px;
          margin-right: 15px;
        }

        span {
          color: #0b8df1;
          font-size: 14px;
        }
      }

      .project-registration-right-middle {
        width: 100%;
        height: auto;
        // height: 100%;
        // margin-top: 15px;
        display: flex;
        flex-direction: column;
        justify-content: left;
        padding-bottom: 50px;
        background: #fff;

        // align-items: center;
        // background: linear-gradient(to bottom, #FFFFFF, #EEF8FF);
        // border: 1px solid #C6E2FF;
        // :deep(.el-timeline){
        //     overflow: hidden;
        // }
        // :deep(.el-timeline):hover {
        // overflow: auto; /* 鼠标 hover 时显示滚动条 */
        // }
        .project-registration-middle-item {
          width: 100%;
          height: 42px;
          display: flex;
          flex-direction: row;
          justify-content: left;
          align-items: center;
          cursor: pointer;

          img {
            width: 14px;
            height: 13px;
            margin-left: 15px;
            margin-right: 15px;
          }

          span {
            color: #0b8df1;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>