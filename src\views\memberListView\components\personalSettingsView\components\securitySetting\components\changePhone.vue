<template>
  <ndDialog ref="changePassword" title="换绑手机号" align-center width="454px" :before-close="close">
    <div class="change-password">
      <div class="item">
        <div class="title">
          <span style="color: red; margin-right: 5px">*</span> 新手机号
        </div>
        <div class="centent">
          <ndInput v-model="pageData.telephone" style="width: 100%" placeholder="请输入" @blur="phoneChanges" />
        </div>
      </div>
      <div class="item" style="margin-bottom: 6px">
        <div class="title">
          <span style="color: red; margin-right: 5px">*</span>图形验证码
        </div>
        <div class="centent" style="display: flex; align-items: center">
          <ndInput v-model="pageData.imgCode" @input="changeImgCode" style="width: 100%; margin-right: 10px"
            placeholder="请输入" />
          <div style="
                min-width: 102px;
                height: 30px;
                border-radius: 4px;
                cursor: pointer;
                overflow: hidden;
              " @click="getImgCode">
            <img :src="pageData.imgCodeUrl" alt="" style="width: 100%; height: 100%" />
          </div>
        </div>
      </div>
      <div class="item" style="margin-bottom: 6px">
        <div class="title">
          <span style="color: red; margin-right: 5px">*</span>短信验证码
        </div>
        <div class="centent" style="display: flex; align-items: center">
          <ndInput v-model="pageData.msgCode" style="width: 100%; margin-right: 10px" placeholder="请输入" />
          <el-button style="width: 140px" @click="getMsgCode" v-if="!pageData.isShowLodin">获取验证码</el-button>
          <el-button style="width: 140px" disabled v-if="pageData.isShowLodin">
            <el-icon>
              <Loading />
            </el-icon>
            {{ pageData.time }}秒后重新发送</el-button>
        </div>
      </div>
      <div class="item">
        <div class="title"></div>
        <div class="centent" style="color: #F56C6C; font-size: 12px">
          手机号更换后您需要使用新手机号才能登录
        </div>
      </div>
    </div>
    <template #footer>
      <ndButton type="primary" @click="confirmSubmission">
        <el-icon style="margin-right: 6px">
          <Check />
        </el-icon>
        确&nbsp;&nbsp;认
      </ndButton>
      <ndButton @click="close">
        <el-icon style="margin-right: 6px">
          <CircleClose />
        </el-icon>
        取&nbsp;&nbsp;消
      </ndButton>
    </template>
  </ndDialog>
</template>
    
<script setup>
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndButton from "@/components/ndButton.vue";
import { inject, onMounted, reactive, ref } from "vue";
import {
  phoneChange,
  randomString,
} from "./Tool.js";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const axios = inject("$axios");
const memberData = inject("memberData")
import {
  encryptByAESarr,
  encryptByAES,
} from "@/components/js/encryptedData.js";
const router = useRouter();
let pageData = reactive({
  showMessage: true,
  telephone: "",
  codeId: "",
  imgCodeUrl: "",
  msgCode: "",
  imgCode: "",
  isShowLodin: false,
  time: 120,
});

onMounted(() => {
  // getImgCode()
});

const emit = defineEmits(["changeData","refreshData"])

// 获取图片验证码
function getImgCode() {
  pageData.codeId = randomString(10);
  // pageData.imgCodeUrl = ipConfig.cqUrl.base + "/manage/memberList/imgCode?codeId=" + pageData.codeId;
  axios({
    url: '/memberList/imgCode',
    method: 'get',
    responseType: 'blob',
    params: {
      codeId: pageData.codeId
    }
  }).then(res => {
    console.log(res, "图形验证码");
    pageData.imgCodeUrl = window.URL.createObjectURL(res.data)
  })
}

// 确认提交
async function confirmSubmission() {
  if (!pageData.telephone) {
    ElMessage.warning("新手机号不能为空！");
    return;
  }
  if (!pageData.imgCode) {
    ElMessage.warning("图形验证码不能为空！");
    return;
  }

  if (!pageData.msgCode) {
    ElMessage.warning("短信验证码不能为空！");
    return;
  }
  let id = memberData._value.id;
  let phone = await encryptByAES(pageData.telephone);
  let params = {
    id: id,
    codeId: pageData.codeId,
    imgCode: pageData.imgCode,
    msgCode: pageData.msgCode,
    telephone: phone.encrypted,
    redisKey: phone.redisKey,

  };
  axios({
    url: "/memberList/telephoneEdit",
    method: "post",
    data: params,
  }).then((r) => {
    if (r.data.code === 200) {
      emit("changeData", r.data.data.telephone)
      emit("refreshData")
      ElMessage.success("手机号换绑成功！")
      close();
    } else {
      ElMessage.error(r.data.msg);
    }
  });
}

// 校验手机号是否正确
async function phoneChanges() {
  if (!phoneChange(pageData.telephone)) {
    pageData.telephone = "";
    ElMessage.warning("请输入正确的手机号！");
  } else {
    // 校验手机号和原来是否是一致
    let phone = await encryptByAES(pageData.telephone);
    axios({
      url: '/memberList/checkTelephone',
      method: 'get',
      params: {
        telephone: phone.encrypted,
        redisKey: phone.redisKey,
        userId: memberData._value.id,
      }
    }).then(res => {
      console.log(res, "手机号校验结果");
      if (res.data.code !== 200) {
        pageData.telephone = ""
        ElMessage.error(res.data.msg);
      }
    })
  }
}

// 校验图形验证码
function changeImgCode(e) {
  if (e.length >= 4) {
    axios({
      url: "/memberList/checkImgCode",
      method: "post",
      data: {
        codeId: pageData.codeId,
        imgCode: pageData.imgCode,
      },
    }).then((r) => {
      if (r.data.code === 200) {
      } else {
        ElMessage.error(r.data.msg);
        pageData.imgCode = "";
        getImgCode();
      }
    });
  }
}



// 获取短信验证码
 function getMsgCode() {
  if (pageData.telephone) {
    if (pageData.imgCode) {
      axios({
        url: "/memberList/checkImgCode",
        method: "post",
        data: {
          codeId: pageData.codeId,
          imgCode: pageData.imgCode,
        },
      }).then(async (res) => {
        console.log(res, "图形码校验结果");
        if (res.data.code == 200) {
          let phone = await encryptByAES(pageData.telephone);
          let params = {
            funType: 5,
            mobile: phone.encrypted,
            redisKey: phone.redisKey,
          };
          axios({
            url: "/valicode/sendValiCode",
            method: "get",
            data: params,
          }).then((res) => {
            console.log(res, "短信验证码");
            if (res.data.code == 200) {
              pageData.isShowLodin = true;
              ElMessage({
                type: 'success',
                message: res.data.msg,
              })
              let lodingButton = setInterval(() => {
                if (pageData.time <= 0) {
                  pageData.time = 120;
                  pageData.isShowLodin = false;
                  clearInterval(lodingButton);
                }
                pageData.time--;
              }, 1000);
            } else {
              ElMessage({
                type: 'error',
                message: res.data.msg,
              })
            }
          });
        } else {
          ElMessage.error(res.data.msg);
        }
      })
    } else {
      ElMessage.warning("图形验证码不能为空！");
    }
  } else {
    ElMessage.warning("手机号不能为空！");
  }
}

const changePassword = ref();
function open() {
  changePassword.value.open();
  getImgCode()
}

function close() {
  pageData.showMessage = true;
  pageData.telephone = "";
  pageData.codeId = "";
  pageData.imgCodeUrl = "";
  pageData.msgCode = "";
  pageData.imgCode = "";
  pageData.isShowLodin = false;
  pageData.time = 120;
  changePassword.value.close();
}

defineExpose({
  open,
  close,
});
</script>
    
<style lang="scss" scoped>
.change-password {
  padding: 0 30px;

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      text-align: right;
      width: 110px;
      margin-right: 10px;
    }

    .centent {
      flex: 1;
    }
  }
}
</style>