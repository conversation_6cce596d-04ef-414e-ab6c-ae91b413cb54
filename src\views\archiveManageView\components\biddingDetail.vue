<template>
  <!-- 项目登记 -->
  <!-- <ndb-page ref="pageRef"> -->
  <div class="project-registration2">
    <div class="bg" style="padding-top: 0">
      <!-- tab切换 -->
      <div class="bg2">
        <div class="box">
          <div v-if="nodata" style="text-align: center">
            <img class="empty-image" src="@/assets/images/empty2.png" alt="" />
            <div class="empty-data">暂无数据信息</div>
          </div>
          <!-- 自由竞价设置 -->
          <!-- <div v-if="setObj.type == 1 && (setObj.status == 71 || setObj.status == 74 || setObj.status == 63 || setObj.status == 73 || setObj.status == 101)"> -->
          <div v-if="setObj.type == 1 || setObj.type == 3">
            <div class="box-title">
              <span>
                <!-- <img src="@/assets/images/biddingSupervisionDetailView/icon1.png"
                                    alt="" /> -->
                <span>竞价设置</span></span
              >
              <nd-button>{{ setObj.stateName }}</nd-button>
            </div>
            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
            >
              <tbody>
                <!-- 第一行 -->
                <tr>
                  <td class="title" width="13%">竞价方式</td>
                  <td width="37%">
                    <span class="text1">{{ setObj.typeObj1.typeName }}</span>
                    <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                  </td>
                  <td class="title" width="150">出价方式</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.type == 1 || setObj.typeObj1.type == 2
                        ? "正向"
                        : setObj.typeObj1.type == 3 || setObj.typeObj1.type == 4
                        ? "反向"
                        : "--"
                    }}</span>
                  </td>
                </tr>
                <!-- 第二行 -->
                <tr>
                  <td class="title" width="150">交易底价</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.floorPrice || "--"
                      }}{{ setObj.typeObj2.floorPriceUnit_dictText }}</span
                    >
                  </td>
                  <td class="title" width="150">出价次数</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.oneOfferFlag == "0"
                        ? "多次"
                        : setObj.typeObj1.oneOfferFlag == "1"
                        ? "一次"
                        : "--"
                    }}</span>
                  </td>
                </tr>
                <tr v-show="setObj.typeObj1.oneOfferFlag == '0'">
                  <td class="title" width="150">限时竞价时长（秒）</td>
                  <td>
                    <span class="text1">
                      {{ setObj.typeObj1.postponeSecond || "--" }}
                    </span>
                  </td>
                  <td class="title" width="150"></td>
                  <td></td>
                </tr>
                <tr>
                  <td class="title" width="150">竞价开始时间</td>
                  <td>
                    <span class="text1">{{ setObj.typeObj1.startTime }}</span>
                  </td>
                  <td class="title" width="150">竞价结束时间</td>
                  <td>
                    <span class="text1">{{ setObj.typeObj1.endTime }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">
                    <span
                      v-show="
                        setObj.typeObj1.type == 1 || setObj.typeObj1.type == 2
                      "
                      >最高限价</span
                    >
                  </td>
                  <td>
                    <span
                      class="text1"
                      v-show="
                        setObj.typeObj1.type == 1 || setObj.typeObj1.type == 2
                      "
                      >{{ setObj.typeObj1.limitPrice || "--"
                      }}<span v-if="setObj.typeObj1.limitPrice">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                  <td class="title" width="150">幅度</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.bidMargin || "--"
                      }}<span v-if="setObj.typeObj1.bidMargin">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                </tr>
                <tr v-if="setObj.typeObj1.priorityType == 1">
                  <td class="title" width="150">优先权行权</td>
                  <td>
                    <span class="text1">
                      {{ setObj.typeObj1.priorityTypeName || "--" }}
                    </span>
                  </td>
                  <td class="title" width="150"><span>是否挂牌公告</span></td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.hangFlag == 1
                        ? "是"
                        : setObj.typeObj1.busType == "3"
                        ? "--"
                        : "否"
                    }}</span>
                  </td>
                </tr>
                <tr v-if="setObj.typeObj1.priorityType != 1">
                  <td class="title" width="150">优先权行权</td>
                  <td>
                    <span class="text1">
                      {{ setObj.typeObj1.priorityTypeName || "--" }}
                    </span>
                  </td>
                  <td class="title" width="150">
                    <span v-show="setObj.typeObj1.priorityType != 1"
                      >优先权角色</span
                    >
                  </td>
                  <td>
                    <span class="text1">
                      {{ setObj.typeObj1.priorityRuleName || "--" }}
                    </span>
                  </td>
                </tr>
                <tr v-if="setObj.typeObj1.priorityType != 1">
                  <td class="title" width="150">
                    <span v-show="setObj.typeObj1.priorityType != 1"
                      >优先权等级</span
                    >
                  </td>
                  <td>
                    <span class="text1">
                      {{ setObj.typeObj1.priorityLevelName || "--" }}
                    </span>
                  </td>
                  <td class="title" width="150">是否挂牌公告</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.hangFlag == 1
                        ? "是"
                        : setObj.typeObj1.busType == "3"
                        ? "--"
                        : "否"
                    }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">竞价开始时间延迟时间</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayTime || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayTime">天</span>
                  </td>
                  <td class="title" width="150">延迟轮次</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayNum || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayNum">轮</span>
                  </td>
                </tr>
                <!-- <tr>
                                    <td class="title" width="150">每次延期天数</td>
                                    <td>
                                        <span class="text1">{{ setObj.typeObj1.type }}</span>
                                    </td>
                                    <td class="title" width="150"></td>
                                    <td>
                                        <span class="text1"></span>
                                    </td>
                                </tr> -->
              </tbody>
            </table>
          </div>
          <!-- 在线多轮竞价设置 -->
          <div v-if="setObj.type == 2 || setObj.type == 4">
            <div class="box-title">
              <span> <span>竞价设置</span></span
              ><nd-button>{{ setObj.stateName }}</nd-button>
            </div>
            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
            >
              <tbody>
                <!-- 第一行 -->
                <tr>
                  <td class="title" width="13%">竞价方式</td>
                  <td width="37%">
                    <span class="text1">{{ setObj.typeObj1.typeName }}</span>
                    <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                  </td>
                  <td class="title" width="150">出价方式</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.type == 1 || setObj.typeObj1.type == 2
                        ? "正向"
                        : setObj.typeObj1.type == 3 || setObj.typeObj1.type == 4
                        ? "反向"
                        : ""
                    }}</span>
                  </td>
                </tr>
                <!-- 第二行 -->
                <tr>
                  <td class="title" width="150">交易底价</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.floorPrice
                      }}{{ setObj.typeObj2.floorPriceUnit_dictText }}</span
                    >
                  </td>
                  <td class="title" width="150">竞价开始时间</td>
                  <td>
                    <span class="text1">{{ setObj.typeObj1.startTime }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">竞价轮数</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.roundNum || "--"
                    }}</span>
                  </td>
                  <td class="title" width="150">每轮时长（秒）</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.roundSecond || "--"
                    }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">
                    本轮未出价者是否可参加下一轮出价
                  </td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.nextRoundFlag == 0
                        ? "否"
                        : setObj.typeObj1.nextRoundFlag == 1
                        ? "是"
                        : "--"
                    }}</span>
                  </td>
                  <!-- <td class="title" width="150"><span v-show="setObj.typeObj1.type==1||setObj.typeObj1.type==2">最高限价</span></td> -->
                  <td class="title" width="150"><span>最高限价</span></td>
                  <td>
                    <span
                      class="text1"
                      v-show="
                        setObj.typeObj1.type != 1 && setObj.typeObj1.type != 2
                      "
                    >
                      --
                    </span>
                    <span
                      class="text1"
                      v-show="
                        setObj.typeObj1.type == 1 || setObj.typeObj1.type == 2
                      "
                      >{{ setObj.typeObj1.limitPrice || "--"
                      }}<span v-if="setObj.typeObj1.limitPrice">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                </tr>

                <tr>
                  <td class="title" width="150">是否挂牌公告</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.hangFlag == 1
                        ? "是"
                        : setObj.typeObj1.busType == "3"
                        ? "--"
                        : "否"
                    }}</span>
                  </td>
                  <td class="title" width="150">竞价开始时间延迟时间</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayTime || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayTime">天</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">延迟轮次</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayNum || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayNum">轮</span>
                  </td>
                  <td class="title" width="150"></td>
                  <td>
                    <span class="text1"></span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- 在线抽签设置 -->
          <div v-if="setObj.type == 5">
            <div class="box-title">
              <span><span>竞价设置</span></span
              ><nd-button>{{ setObj.stateName }}</nd-button>
            </div>

            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
            >
              <tbody>
                <!-- 第一行 -->
                <tr>
                  <td class="title" width="13%">竞价方式</td>
                  <td width="37%">
                    <span class="text1">{{ setObj.typeObj1.typeName }}</span>
                    <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                  </td>
                  <td class="title" width="150">成交价格</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.floorPrice
                      }}{{ setObj.typeObj2.floorPriceUnit_dictText }}</span
                    >
                  </td>
                </tr>
                <!-- 第二行 -->
                <tr>
                  <td class="title" width="150">在线抽签时间</td>
                  <td>
                    <span class="text1">{{ setObj.typeObj1.startTime }}</span>
                  </td>
                  <td class="title" width="150">延迟轮次</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayNum || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayNum">轮</span>
                  </td>
                </tr>

                <tr>
                  <td class="title" width="150">是否挂牌公告</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.hangFlag == 1
                        ? "是"
                        : setObj.typeObj1.busType == "3"
                        ? "--"
                        : "否"
                    }}</span>
                  </td>
                  <td class="title" width="150">竞价开始时间延迟时间</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayTime || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayTime">天</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- 荷兰式竞价(V型竞价) -->
          <div v-if="setObj.type == 10">
            <div class="box-title">
              <span><span>竞价设置</span></span
              ><nd-button>{{ setObj.stateName }}</nd-button>
            </div>
            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
            >
              <tbody>
                <!-- 第一行 -->
                <tr>
                  <td class="title" width="13%">竞价方式</td>
                  <td width="37%">
                    <span class="text1">{{ setObj.typeObj1.typeName }}</span>
                    <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                  </td>
                  <td class="title" width="150">底价</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.floorPrice || "--"
                      }}<span v-if="setObj.typeObj1.floorPrice">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                </tr>
                <!-- 第二行 -->
                <tr>
                  <td class="title" width="150">保留价格</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.reducePrice || "--"
                      }}<span v-if="setObj.typeObj1.reducePrice">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                  <td class="title" width="150">底价降价周期</td>
                  <td>
                    <!-- <span class="text1">{{ setObj.typeObj1.bidPriceDownTime }}</span> -->
                    <span
                      class="text1"
                      style="
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                      "
                    >
                      <!-- {{ setObj.typeObj1.bidPriceDownTime }} -->
                      <nd-input
                        width="100px"
                        style="margin-right: 10px"
                        disabled
                        v-model="formRespons.obj4.s"
                        placeholder="请输入"
                        clearable
                      ></nd-input
                      ><span style="margin-right: 10px">小时</span>
                      <nd-input
                        width="100px"
                        style="margin-right: 10px"
                        disabled
                        v-model="formRespons.obj4.f"
                        placeholder="请输入"
                        clearable
                      ></nd-input
                      ><span style="margin-right: 10px">分</span>
                      <nd-input
                        width="100px"
                        style="margin-right: 10px"
                        disabled
                        v-model="formRespons.obj4.m"
                        placeholder="请输入"
                        clearable
                      ></nd-input
                      ><span style="margin-right: 10px">秒</span>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">每轮降价金额</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.roundPrice || "--"
                      }}<span v-if="setObj.typeObj1.roundPrice">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                  <td class="title" width="150">竞价开始时间</td>
                  <td>
                    <span class="text1">{{ setObj.typeObj1.startTime }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">限时竞价时长（秒）</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.postponeSecond
                    }}</span>
                  </td>
                  <td class="title" width="150">竞价幅度</td>
                  <td>
                    <span class="text1"
                      >{{ setObj.typeObj1.bidMargin || "--"
                      }}<span v-if="setObj.typeObj1.bidMargin">{{
                        setObj.typeObj2.floorPriceUnit_dictText
                      }}</span></span
                    >
                  </td>
                </tr>

                <tr>
                  <td class="title" width="150">是否挂牌公告</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.hangFlag == 1
                        ? "是"
                        : setObj.typeObj1.busType == "3"
                        ? "--"
                        : "否"
                    }}</span>
                  </td>
                  <td class="title" width="150">竞价开始时间延迟时间</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayTime || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayTime">天</span>
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">延迟轮次</td>
                  <td>
                    <span class="text1">{{
                      setObj.typeObj1.delayNum || "--"
                    }}</span
                    ><span v-if="setObj.typeObj1.delayNum">轮</span>
                  </td>
                  <td class="title" width="150"></td>
                  <td>
                    <span class="text1"></span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- 竞价人信息 -->
          <div
            v-if="
              setObj.status == 71 ||
              setObj.status == 74 ||
              setObj.status == 63 ||
              setObj.status == 73
            "
            style="margin-top: 15px"
          >
            <div class="box-title">
              <span><span>竞价人信息</span></span>
            </div>
            <nd-table
              style="height: 100%; width: 100%; margin-top: 15px"
              :data="listData.list1"
            >
              <el-table-column
                align="center"
                prop="bidNo"
                label="编号"
                min-width="140"
              />
              <!-- <el-table-column align="center" prop="name" label="名称" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="name"
                label="名称"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow3 == false">{{
                    row.name
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.name2 }}</span>
                  <el-icon
                    v-if="row.nameCiphertext && row.isShow3 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.nameCiphertext && row.isShow3 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="certNo" label="证件号码" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="certNo"
                label="证件号码"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow2 == false">{{
                    row.certNo
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.certNo2 }}</span>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="contactPhone" label="联系电话" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="contactPhone"
                label="联系电话"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow == false">{{
                    row.contactPhone
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.contactPhone2 }}</span>
                  <el-icon
                    v-if="row.contactPhoneCiphertext && row.isShow == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.contactPhoneCiphertext && row.isShow == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="identityType" label="竞价人类型" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="identityType"
                label="竞价人类型"
              >
                <template #default="{ row }">
                  <span class="operateBtn">{{ row.identityTypeName }}</span>
                  <!-- <span class="operateBtn">{{ row.identityType == '10' ? '原承租方':row.identityType == '01' ? '组织成员':row.identityType == '11' ? '原承租方，组织成员':row.identityType == '00' ? '普通竞价人': '' 
                                }}</span> -->
                </template>
              </el-table-column>
              <el-table-column
                      align="center"
                      prop="priorityLevel"
                      label="优先权序号"
                    >
                      <template #default="{ row }">
                        <span class="operateBtn">{{ row.hasPriority===1?row.priorityLevel:'无优先权人' }}</span>
                      </template>
                    </el-table-column>
              <!-- 10 -->
            </nd-table>
          </div>
          <!-- 竞价结果信息 -->
          <div
            v-if="setObj.status == 74 || setObj.status == 73"
            style="margin-top: 15px"
          >
            <div class="box-title">
              <span><span>竞价结果信息</span></span>
            </div>
            <nd-table
              style="height: 100%; width: 100%; margin-top: 15px"
              :data="listData.list2"
            >
              <!-- <el-table-column align="center" prop="state" label="状态" min-width="140" /> -->
              <!-- <el-table-column  align="center" prop="transactionNo" label="状态" v-if="setObj.type == 5">
                            <template #default="{ row }">
                                <span class="operateBtn">{{ row.transactionNo == '1' ? '第一成交人':row.transactionNo == '2' ? '第二成交人':row.transactionNo == '3' ? '第三成交人': '出局' 
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column  align="center" prop="state" label="状态" v-else>
                            <template #default="{ row }">
                                <span class="operateBtn">{{ row.transactionNo == '1' ? '成交': '出局' 
                                }}</span>
                            </template>
                        </el-table-column> -->
              <el-table-column align="center" prop="state" label="状态">
                <template #default="{ row }">
                  <span class="operateBtn">{{ row.typeName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="bidderNo"
                label="编号"
                min-width="140"
              />
              <!-- <el-table-column align="center" prop="bidderName" label="名称" min-width="140" /> -->
              <!-- <el-table-column  align="center" prop="bidderName" label="名称">
                            <template #default="{ row }">
                                <span>{{ row.bidderName }}</span>
                                <span v-show="row.priorityFlag==1" style="padding:0px 3px;display:inline-block;background:red;color:#fff;border-radius:5px;">优先权</span>
                            </template>
                        </el-table-column> -->
              <el-table-column
                align="center"
                prop="bidderName"
                label="名称"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow3 == false">{{
                    row.bidderName
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.bidderName2 }}</span>
                  <span
                    v-show="row.priorityFlag == 1"
                    style="
                      padding: 0px 3px;
                      display: inline-block;
                      background: red;
                      color: #fff;
                      border-radius: 5px;
                    "
                    >优先权</span
                  >
                  <el-icon
                    v-if="row.bidderNameCiphertext && row.isShow3 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick(2, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.bidderNameCiphertext && row.isShow3 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick2(2, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="certNo" label="证件号码" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="certNo"
                label="证件号码"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow2 == false">{{
                    row.certNo
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.certNo2 }}</span>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="contactPhone" label="联系电话" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="contactPhone"
                label="联系电话"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow == false">{{
                    row.contactPhone
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.contactPhone2 }}</span>
                  <el-icon
                    v-if="row.contactPhoneCiphertext && row.isShow == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.contactPhoneCiphertext && row.isShow == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="bidPrice"  :label="'成交价格'+'('+setObj.typeObj2.floorPriceUnit_dictText+')'" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="bidPrice"
                :label="
                  '成交价格' +
                  '(' +
                  setObj.typeObj2.floorPriceUnit_dictText +
                  ')'
                "
                min-width="140"
              >
                <template #default="{ row }">
                  <span>{{ row.bidPrice || "--" }}</span>
                </template>
              </el-table-column>
            </nd-table>
          </div>
          <!-- 出价记录 -->
          <div
            v-if="setObj.status == 74 || setObj.status == 73"
            style="margin-top: 15px"
          >
            <div class="box-title">
              <span><span>出价记录</span></span>
            </div>
            <nd-table
              style="height: 100%; width: 100%; margin-top: 15px"
              :data="listData.list3"
            >
              <el-table-column
                align="center"
                prop="bidTime"
                label="出价时间"
                min-width="140"
              />
              <!-- <el-table-column align="center" prop="bidStage" label="阶段" min-width="140" /> -->
              <el-table-column align="center" prop="bidStage" label="阶段">
                <template #default="{ row }">
                  <span class="operateBtn">{{
                    row.bidStage == "1"
                      ? "自由竞价"
                      : row.bidStage == "2"
                      ? "限时竞价"
                      : row.bidStage == "3"
                      ? "底价竞价期"
                      : ""
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="bidderNo"
                label="编号"
                min-width="140"
              />
              <!-- <el-table-column align="center" prop="bidderName" label="名称" min-width="140" /> -->
              <!-- <el-table-column  align="center" prop="bidderName" label="名称">
                            <template #default="{ row }">
                                <span>{{ row.bidderName }}</span>
                                <span v-show="row.priorityFlag==1" style="padding:0px 3px;display:inline-block;background:red;color:#fff;border-radius:5px;">优先权</span>
                            </template>
                        </el-table-column> -->
              <el-table-column
                align="center"
                prop="bidderName"
                label="名称"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow3 == false">{{
                    row.bidderName
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.bidderName2 }}</span>
                  <span
                    v-show="row.priorityFlag == 1"
                    style="
                      padding: 0px 3px;
                      display: inline-block;
                      background: red;
                      color: #fff;
                      border-radius: 5px;
                    "
                    >优先权</span
                  >
                  <el-icon
                    v-if="row.bidderNameCiphertext && row.isShow3 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick(2, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.bidderNameCiphertext && row.isShow3 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="nameClick2(2, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="certNo" label="证件号码" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="certNo"
                label="证件号码"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow2 == false">{{
                    row.certNo
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.certNo2 }}</span>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick(1, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.certNoCiphertext && row.isShow2 == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="certNoClick2(1, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="bidderPhone" label="联系电话" min-width="140" /> -->
              <el-table-column
                align="center"
                prop="bidderPhone"
                label="联系电话"
                min-width="140"
              >
                <template #default="{ row }">
                  <span class="operateBtn" v-if="row.isShow == false">{{
                    row.bidderPhone
                  }}</span>
                  <span class="operateBtn" v-else>{{ row.bidderPhone2 }}</span>
                  <el-icon
                    v-if="row.bidderPhoneCiphertext && row.isShow == false"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick(2, row)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-if="row.bidderPhoneCiphertext && row.isShow == true"
                    style="margin-left: 10px; cursor: pointer"
                    @click="telClick2(2, row)"
                  >
                    <View />
                  </el-icon>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="bidPrice"
                :label="
                  '出价金额' +
                  '(' +
                  setObj.typeObj2.floorPriceUnit_dictText +
                  ')'
                "
                min-width="140"
              >
                <template #default="{ row }">
                  <span>{{ row.bidPrice || "--" }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" prop="bidPrice" :label="'出价金额'+'('+setObj.typeObj2.floorPriceUnit_dictText+')'" min-width="140" /> -->
            </nd-table>
          </div>
          <!-- 取消记录 -->
          <div v-if="setObj.status == 63" style="margin-top: 15px">
            <div class="box-title">
              <span><span>取消记录</span></span>
            </div>
            <nd-table
              style="height: 100%; width: 100%; margin-top: 15px"
              :data="listData.list4"
            >
              <el-table-column
                align="center"
                prop="cancelTime"
                label="操作时间"
                min-width="140"
              />
              <el-table-column
                align="center"
                prop="cancelUserName"
                label="操作人员"
                min-width="140"
              />
              <el-table-column
                align="center"
                prop="cancelReason"
                label="取消原因"
                min-width="140"
              />
            </nd-table>
          </div>
          <!-- <div v-if="setObj.state==101"> -->
          <div
            v-if="setObj.terminationFlag == 1 || setObj.terminationFlag == '1'"
          >
            <div class="box-title">
              <span><span>终止信息</span></span>
            </div>
            <table
              width="100%"
              border="0"
              cellspacing="0"
              cellpadding="0"
              class="detailsbd-tc-s"
            >
              <tbody>
                <!-- 第一行 -->
                <tr>
                  <td class="title" width="150">操作人员</td>
                  <td>
                    <span
                      class="text1"
                      style="
                        color: #444444;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: normal;
                      "
                      >{{ setObj.endAnnDetailVos.userName }}</span
                    >
                    <!-- <nd-input placeholder="" class="inputBox" v-model="form.projectCode" :width="'100%'"></nd-input> -->
                  </td>
                  <td class="title" width="150">操作时间</td>
                  <td>
                    <span
                      class="text1"
                      style="
                        color: #444444;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: normal;
                      "
                      >{{ setObj.endAnnDetailVos.insertTime }}</span
                    >
                  </td>
                </tr>
                <tr>
                  <td class="title" width="150">终止原因</td>
                  <td colspan="3">
                    <span class="text1">{{
                      setObj.endAnnDetailVos.endReason
                    }}</span>
                  </td>
                </tr>
                <tr
                  v-for="(item, index) in formRespons.uploadList"
                  :key="index"
                >
                  <td class="title" width="150">{{ item.fileName }}</td>
                  <td colspan="3">
                    <ndb-upload
                      disabled
                      v-model="item.fileList"
                      style="margin-top: 12px"
                      :uploadParams="{
                        busId: projectInfor.id,
                        configFileId: item.id,
                      }"
                    ></ndb-upload>
                    <!-- <span class="text1">{{ projectInfor.forms.endReason }}</span> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- <div style="text-align: center;margin-top: 50px;">
                        <ndButton icon="back" @click="goBack()">
                            返回
                        </ndButton>
                    </div>
                    <div  class="position">
                        <ndButton icon="back" @click="goBack()">
                            返回
                        </ndButton>
                    </div> -->
        </div>
      </div>
      <!-- <div style="text-align: center;margin-top: 50px;">
                    <ndButton type="primary" style="background-color: #0B8DF1;" @click="submit()">
                        提交
                    </ndButton>
                </div> -->
    </div>
  </div>
  <!-- </ndb-page> -->
</template>

<script setup>
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndbPage from "@/components/business/ndbPage/index.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndInput from "@/components/ndInput.vue";
import ndbControlAdapter from "@/components/business/ndbControlAdapter.vue";
import ndTabs from "@/components/ndTabs.vue";
import ndTable from "@/components/ndTable.vue";
import projectRegistrationDetailView from "@/views/projectRegistrationDetailView/components/detail.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndbUpload from "@/components/business/ndbUpload2/index.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  nextTick,
  toRaw,
  shallowRef,
} from "vue";
import { useRouter } from "vue-router";
import { CircleCheck } from "@element-plus/icons-vue";
// import { set } from "ol/transform";

const props = defineProps({
  params: {
    type: Object,
    default: {},
  },
});
const value = ref("");
const value2 = ref("");
const value3 = ref("");
const options = [
  {
    value: "1",
    label: "一次",
  },
  {
    value: "0",
    label: "多次",
  },
];
const options2 = [
  {
    value: "1",
    label: "是",
  },
  {
    value: "0",
    label: "否",
  },
];
const router = useRouter();
const $axios = inject("$axios");
let show = reactive({
  listShow1: true,
  listShow2: true,
  listShow3: true,
  listShow4: true,
});
let setObj = reactive({
  type: "",
  status: "",
  stateName: "",
  state: "",
  typeObj1: {},
  typeObj2: {},
  list1: [],
  list2: [],
  list3: [],
  list4: [],
  endAnnDetailVos: {},
});
let projectInfor = reactive({
  id: "", //项目id00123c2847c5492abc8c40ebf69b7d6c
  forms: {},
});
let listData = reactive({
  list1: [],
  list2: [],
  list3: [],
  list4: [],
});
const emits = defineEmits(["back"]); //定义方法名
const goBack = () => {
  emits("back");
  // router.go(-1);
};

onMounted(() => {
  refeshFun();
});

const back = () => {
  goBack();
  // router.go(-1);
};
let page = reactive({
  isProjectDetail: false, // 仅仅是项目详情
  isProjectDetail2: true, // 仅仅是项目详情
});
let formRespons = reactive({
  //初始化数据
  list: [],
  bdId: [],
  obj4: {
    s: "",
    f: "",
    m: "",
  },
  uploadList: [],
});
function submit() {
  $axios({
    method: "post",
    url: "/manageRuleInfo/saveOrUpdateRule",
  }).then((res) => {
    if (res.data.code == 200) {
    }
  });
}
let primary = reactive({
  val1: "primary",
  val2: "",
});
function search(th) {
  if (th == 1) {
    primary.val1 = "primary";
    primary.val2 = "";
    page.isProjectDetail2 = true;
    page.isProjectDetail = false;
  } else {
    primary.val1 = "";
    primary.val2 = "primary";
    page.isProjectDetail2 = false;
    page.isProjectDetail = true;
  }
}
const handleClick = (tab) => {
  if (tab.index == 0) {
    page.isProjectDetail2 = true;
    page.isProjectDetail = false;
  } else {
    page.isProjectDetail2 = false;
    page.isProjectDetail = true;
  }
  activeName.value = tab.index;
  //   if (tab.index == '1') projectResg.value.refeshFun(rowObject.data.projectId)
};
const projectRegistration2 = ref(null);
function formatSeconds(seconds) {
  let hour = Math.floor(seconds / 3600);
  let minute = Math.floor((seconds % 3600) / 60);
  let second = Math.floor((seconds % 3600) % 60);
  formRespons.obj4.s = hour;
  formRespons.obj4.f = minute;
  formRespons.obj4.m = second;
}
const telClick = (th, rowObj) => {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.contactPhoneCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow = true;
        rowObj.contactPhone2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.bidderPhoneCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow = true;
        rowObj.bidderPhone2 = res.data.data;
      }
    });
  }
};
const telClick2 = (th, rowObj) => {
  rowObj.isShow = false;
};
const certNoClick = (th, rowObj) => {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.certNoCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow2 = true;
        rowObj.certNo2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.certNoCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow2 = true;
        rowObj.certNo2 = res.data.data;
      }
    });
  }
};
const certNoClick2 = (th, rowObj) => {
  rowObj.isShow2 = false;
};
const nameClick = (th, rowObj) => {
  if (th == 1) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.nameCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow3 = true;
        rowObj.name2 = res.data.data;
      }
    });
  } else if (th == 2) {
    let params = new FormData();
    params.append("encryptMessage", rowObj.bidderNameCiphertext);
    $axios({
      method: "post",
      url: "/manageRuleInfo/decrypt",
      data: params,
    }).then((res) => {
      if (res.data.code == 200) {
        rowObj.isShow3 = true;
        rowObj.bidderName2 = res.data.data;
      }
    });
  }
};
const nameClick2 = (th, rowObj) => {
  rowObj.isShow3 = false;
};

const nodata = ref(false);

const refeshFun = () => {
  primary.val1 = "primary";
  primary.val2 = "";
  // setObj.type = props.params.bidType;
  // setObj.status = props.params.flowStatus;
  $axios({
    method: "get",
    url: "/archivesStore/getRuleDetail",
    params: {
      archivesId: props.params.archivesId,
      auditId: props.params.auditId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
    },
  }).then((res) => {
    if (res.data.code == 200) {
      if (!res.data.data) {
        nodata.value = true;
        return;
      }

      listData.list1 = res.data.data.bidderInformationListVos;
      listData.list1.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.isShow3 = false;
        item.contactPhone2 = "";
        item.certNo2 = "";
        item.name2 = "";
      });
      listData.list2 = res.data.data.biddingResultInformationVos;
      listData.list2.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.contactPhone2 = "";
        item.isShow3 = false;
        item.bidderName2 = "";
        item.certNo2 = "";
      });
      listData.list3 = res.data.data.onlineBidRecordListVos;
      listData.list3.forEach((item) => {
        item.isShow = false;
        item.isShow2 = false;
        item.isShow3 = false;
        item.bidderPhone2 = "";
        item.certNo2 = "";
        item.bidderName2 = "";
      });
      listData.list4 = res.data.data.onlineBidCancelListVos;
      setObj.typeObj2 = res.data.data;
      setObj.typeObj1 = res.data.data.onlineRuleInfoVo;
      setObj.endAnnDetailVos = res.data.data.endAnnDetailVos || {};
      setObj.stateName = res.data.data.stateName;
      setObj.state = res.data.data.state;
      setObj.status = res.data.data.state;
      setObj.type = res.data.data.onlineRuleInfoVo.type;
      setObj.terminationFlag = res.data.data.terminationFlag;

      formatSeconds(setObj.typeObj1.bidPriceDownTime);
      // formRespons.obj4.s=setObj.typeObj1.bidPriceDownTime/60/60
      //  formRespons.obj4.s = parseInt(setObj.typeObj1.bidPriceDownTime / 60 / 60 % 24)
      //  formRespons.obj4.s = formRespons.obj4.s < 10 ? '0' + formRespons.obj4.s : formRespons.obj4.s
      //  formRespons.obj4.f = parseInt(setObj.typeObj1.bidPriceDownTime / 60 % 60)
      //  formRespons.obj4.f = formRespons.obj4.f < 10 ? '0' + formRespons.obj4.f : formRespons.obj4.f
      //  formRespons.obj4.m = parseInt(setObj.typeObj1.bidPriceDownTime / 60 % 60)
      //  formRespons.obj4.m = formRespons.obj4.m < 10 ? '0' + formRespons.obj4.m : formRespons.obj4.m

      $axios({
        url: "/file/getTheAttachmentTypeList",
        method: "post",
        data: {
          busId: setObj.endAnnDetailVos.dataId,
          flowBasecode: "9",
          projectId: setObj.endAnnDetailVos.projectId,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          // res.data.data[0].fileList.forEach(item => {
          //     let obj = {};
          //     obj.name = item.fileName;
          //     obj.url = item.fileUrl;
          //     obj.id = item.id;
          //     fileList2.value.push(obj)
          // })
          formRespons.uploadList = res.data.data;
        }
      });
      
    }
  });
  // getFormData();
  // getUploadList();
  // getUploadList2();
};
defineExpose({ refeshFun });
onMounted(() => {
  // setTimeout(function () {
  //     pageRef.value.addBreadcrumb("产权交易");
  //     pageRef.value.addBreadcrumb("项目登记详情");
  // }, 2000)
});
</script>

<style lang="scss" scoped>
.project-registration2 {
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .bg {
    width: 100%;
    height: 100%;
    background-color: #f7f8fa;
    // padding: 12px 15px 15px 15px;
    padding: 12px 0px 15px 0px;
    border-radius: 5px;
    margin-bottom: 10px;
    overflow-y: auto;

    .bg2 {
      // padding: 12px 15px 15px 15px;
    }

    .box {
      width: 100%;
      margin: 0 auto;
      margin-top: 10px;
      background-color: #fff;
      padding: 12px 15px 15px 15px;

      .box-title {
        // border-left: 3px solid #0b8df1;
        // padding-left: 10px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;

        // margin-top: 10px;
        span {
          display: flex;
          display: inline-block;
          justify-content: space-between;
          margin-top: 10px;
          align-content: center;
          align-items: center;
          color: rgb(96, 98, 102);
          font-size: 14px;
          font-weight: 700;

          img {
            width: 18px;
            height: 18px;
            margin-right: 15px;
            position: relative;
            top: 3px;
          }
        }
      }

      .detailsbd-tc-s {
        border-left: 1px solid #eeeeee;
        border-top: 1px solid #eeeeee;
        margin-top: 15px;

        td {
          border-right: 1px solid #eeeeee;
          border-bottom: 1px solid #eeeeee;
          padding: 7px 8px;
          text-align: left;
          font-size: 14px;
          color: #444444;
          background: #fff;

          .redStar {
            color: red;
          }
        }

        .title {
          color: #606266;
          text-align: right;
          background-color: #f9f9f9;
          font-size: 14px;
        }
      }
    }
  }
}
.position {
  position: fixed;
  top: 68px;
  right: 0;
  :deep(.el-button) {
    border: transparent;
    background: transparent;
  }
}
.empty-image {
  width: 270px;
  height: 190px;
}

.empty-data {
  font-size: 16px;
  font-weight: bold;
  color: #0b8df1;
  margin-top: 10px;
}
</style>