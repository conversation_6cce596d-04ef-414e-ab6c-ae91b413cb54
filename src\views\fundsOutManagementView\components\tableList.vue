<template>
  <!-- <ndb-page> -->
  <div class="main-box xz-box">
    <ndb-page-list>
      <template #tab>
        <nd-tabs :tabList="optionsBySearchData.expenseTypeOpt?.map((item) => item.dataValue)" @tab-click="handleTabClick" />
      </template>
      <template #tag>
        <el-badge v-for="item in tabTagsMap" :key="item.key" :value="tabCheck === item.key ? pager.total : ''" :max="99" type="primary">
          <nd-tag :checked="item.key == tabCheck" @change="tabCheckOnChange(item.key)" style="margin-right: 10px">{{ item.name }}</nd-tag>
        </el-badge>
      </template>
      <template #search>
        <nd-search-more arrowMarginLeft="220px">
          <nd-search-more-item title="项目查询" width="120px">
            <ndInput placeholder="请输入项目名称/项目编号" v-model.trim="searchData.searchValue" clearable />
          </nd-search-more-item>
          <nd-search-more-item title="标段编号" width="120px">
            <ndInput placeholder="请输入" v-model.trim="searchData.tenderCode" clearable />
          </nd-search-more-item>
          <nd-search-more-item title="业务类型" width="120px">
            <nd-select v-model="searchData.busType">
              <el-option v-for="item in optionsBySearchData.businessTypeOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
          </nd-search-more-item>
          <nd-search-more-item title="出金状态" width="120px">
            <nd-select v-model="searchData.outStatus">
              <el-option v-for="item in optionsBySearchData.outStatusOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
          </nd-search-more-item>
          <nd-search-more-item title="项目余额" width="120px">
            <nd-select v-model="searchData.balanceStatus">
              <el-option label="全部" value="" />
              <el-option label="余额为0" :value="0" />
              <el-option label="余额不为0" :value="1" />
            </nd-select>
          </nd-search-more-item>
          <nd-search-more-item title="交易品种" width="120px">
            <!-- <nd-select v-model="searchData.tradeKind">
                                <el-option v-for="item in optionsBySearchData.tradingInstrumentOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
                            </nd-select> -->
            <ndCascader
              v-model="searchData.tradeKind"
              :options="optionsBySearchData.tradingInstrumentOpt"
              :props="{ label: 'dataValue', value: 'dataKey', children: 'childList', checkStrictly: true }"
              style="width: 100%"
              clearable
            ></ndCascader>
          </nd-search-more-item>
          <nd-search-more-item title="缴款人" width="120px">
            <ndInput placeholder="请输入" v-model.trim="searchData.signupName" clearable />
          </nd-search-more-item>
          <nd-search-more-item title="是否成交" width="120px">
            <nd-select v-model="searchData.tradeFlag">
              <el-option label="全部" value="" />
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </nd-select>
          </nd-search-more-item>
          <nd-search-more-item title="交易方式" width="120px">
            <nd-select v-model="searchData.tradeType">
              <el-option v-for="item in optionsBySearchData.transactionMethodOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
          </nd-search-more-item>
          <nd-search-more-item title="地区筛选" width="120px">
            <el-tree-select
              style="width: 100%"
              v-model="searchData.areaId"
              lazy
              :load="isLoad"
              :props="{ label: 'name', children: 'children', isLeaf: 'isLeaf' }"
              @node-click="handleNodeClick"
              node-key="id"
              accordion
              check-strictly
              clearable
            />
          </nd-search-more-item>
          <nd-search-more-item title="项目状态" width="120px">
            <nd-select v-model="searchData.flowStatus">
              <el-option v-for="item in optionsBySearchData.flowStatusStatusOpt" :key="item.value" :label="item.value" :value="item.key" />
            </nd-select>
          </nd-search-more-item>
          <!-- <nd-search-more-item title="项目状态" width="120px">
                            <nd-select v-model="searchData.proStatus">
                                <el-option v-for="item in optionsBySearchData.projectStatusOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
                            </nd-select>
                        </nd-search-more-item> -->
          <template #footer>
            <!-- <nd-button type="primary" @click="handleTableSearch">查询</nd-button>
                            <nd-button @click="resetSearch">重置</nd-button> -->
            <ndb-button-search @click="handleTableSearch"></ndb-button-search>
            <ndb-button-reset @click="resetSearch"></ndb-button-reset>
          </template>
        </nd-search-more>
      </template>
      <template #statistics>
        <div class="total">
          <div class="total-detail">
            <div class="left">出金待申请（元）：</div>
            <div class="right">{{ staticData.dcjAmountStr || 0 }}</div>
          </div>
          <div class="total-detail">
            <div class="left">出金审核中（元）：</div>
            <div class="right">{{ staticData.shzAmountStr || 0 }}</div>
          </div>
          <div class="total-detail">
            <div class="left">出金待处理（元）：</div>
            <div class="right">{{ staticData.dclAmountStr || 0 }}</div>
          </div>
          <div class="total-detail">
            <div class="left">已出金（万元）：</div>
            <div class="right">{{ staticData.ycjAmountStr || 0 }}</div>
          </div>
        </div>
      </template>
      <!-- 主体表格 -->
      <template #table>
        <!-- <div class="tip">
          <el-icon style="margin-right: 10px">
            <WarningFilled />
          </el-icon>
          由于各个银行退款周期不同，退款无法实时到账，请耐心等待。如若超过7天退款没到账，请及时联系产权交易中心！
        </div> -->
        <nd-table style="height: calc(100%); width: 100%" :data="listData.list">
          <el-table-column align="center" type="index" min-width="60" label="序号" :index="(index) => (pager.pageNo - 1) * pager.pageSize + index + 1" fixed />
          <el-table-column header-align="center" prop="proCode" show-overflow-tooltip label="项目编号" min-width="170" fixed />
          <el-table-column header-align="center" prop="proName" label="项目名称" show-overflow-tooltip min-width="200"  />
          <el-table-column align="center" prop="tenderCode" label="标段" min-width="60" />
          <el-table-column align="center" prop="busType" label="业务类型" min-width="140" v-if="!pageIpConfig.busType">
            <template #default="{ row }">
              {{ optionsBySearchData.businessTypeOpt.find((item) => item.dataKey == row.busType)?.dataValue }}
            </template>
          </el-table-column>

          <el-table-column align="center" prop="signupName" show-overflow-tooltip label="缴款人" min-width="80" />
          <el-table-column align="center" prop="identityName" label="缴款人身份" width="95" show-overflow-tooltip />
          <el-table-column align="center" prop="tradeFlagName" label="是否成交" min-width="75" />
          <el-table-column align="center" prop="tbAmount" label="投标保证金（元）" min-width="190" v-if="tableColShow('3')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.tbExpendExamineStatus, row.tbExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.tbExpendExamineStatus, row.tbExpendStatus, row.tbFeeId, 3, row)">
                {{ rowDetail.statusTxt }}
                <span> （{{ row.tbAmount }}）</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="bmAmount" label="材料费（元）" min-width="190" v-if="tableColShow('1')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.bmExpendExamineStatus, row.bmExpendStatus)) }}
              </span>
              <span @click="handlePay(row.bmExpendExamineStatus, row.bmExpendStatus, row.bmFeeId, 1, row)" :style="{ color: rowDetail.color, cursor: rowDetail.cursor }">
                {{ rowDetail.statusTxt }}
                <span> （{{ row.bmAmount }}）
                </span>
              </span>

            </template>
          </el-table-column>

          <el-table-column align="center" prop="fwAmount" label="服务费（元）" min-width="190" v-if="tableColShow('2')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.fwExpendExamineStatus, row.fwExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.fwExpendExamineStatus, row.fwExpendStatus, row.fwFeeId, 2, row)">
                {{ rowDetail.statusTxt }}
                <span> （{{ row.fwAmount }}）
                </span>
              </span>

            </template>
          </el-table-column>

          <!-- bsfAmount bsfExpendExamineStatus bsfExpendStatus bsfFeeId -->
          <el-table-column align="center" prop="bsfAmount" label="标书（元）" min-width="190" v-if="tableColShow('8')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.bsfExpendExamineStatus, row.bsfExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.bsfExpendExamineStatus, row.bsfExpendStatus, row.bsfFeeId, 8, row)">
                {{ rowDetail.statusTxt }}
                            <span>（ {{ row.bsfAmount
                                    }}）
                                </span>
              </span>
  
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sqAmount" label="合同首期款（元）" min-width="190" v-if="tableColShow('4')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.sqExpendExamineStatus, row.sqExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.sqExpendExamineStatus, row.sqExpendStatus, row.sqFeeId, 4, row)">
                {{ rowDetail.statusTxt }}
                <span> （{{ row.sqAmount }}）
                </span>
              </span>

            </template>
          </el-table-column>
          <el-table-column align="center" prop="lyAmount" label="履约保证金（元）" min-width="190" v-if="tableColShow('5')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.lyExpendExamineStatus, row.lyExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.lyExpendExamineStatus, row.lyExpendStatus, row.lyFeeId, 5, row)">
                {{ rowDetail.statusTxt }}
                <span>（{{ row.lyAmount }}）
                </span>
              </span>

            </template>
          </el-table-column>
          <el-table-column align="center" prop="wkAmount" label="合同尾款（元）" min-width="190" v-if="tableColShow('6')">
            <template #default="{ row }">
              <span v-show="false">
                {{ (rowDetail = moneyStatus(row.wkExpendExamineStatus, row.wkExpendStatus)) }}
              </span>
              <span :style="{ color: rowDetail.color, cursor: rowDetail.cursor }" @click="handlePay(row.wkExpendExamineStatus, row.wkExpendStatus, row.wkFeeId, 6, row)">
                {{ rowDetail.statusTxt }}
                <span>（ {{ row.wkAmount }}）
                </span>
              </span>
      
            </template>
          </el-table-column>
          <el-table-column align="center" prop="tradeKindName" label="交易品种" show-overflow-tooltip min-width="180">
            <!-- <template #default="{ row }">
                                {{ optionsBySearchData.tradingInstrumentOpt.find((item) => item.dataKey == row.tradeKind)?.dataValue }}
                            </template> -->
          </el-table-column>
          <el-table-column align="center" prop="tradeTypeName" label="交易方式" width="100">
            <template #default="{ row }">
              <span>{{ row.tradeTypeName || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="countyName" label="县（市、区）" min-width="140" v-if="!pageIpConfig.countyName" />
          <el-table-column align="center" prop="townName" label="乡镇（街道）" min-width="140" v-if="!pageIpConfig.townName" />
          <el-table-column align="center" prop="villageName" label="村（社区）" min-width="140" v-if="!pageIpConfig.villageName" />
          <el-table-column align="center" prop="flowStatusName" label="项目状态" min-width="140">
            <template #default="{ row }">
              <el-tag v-if="row.flowStatusName" :type="setStatusTagColor(row.flowStatusName)">{{ row.flowStatusName }}</el-tag>
              <span v-else :type="setStatusTagColor(row.flowStatusName)">--</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="150">
            <template #default="{ row, $index }">
              <!-- <div style="display: flex; align-items: center; justify-content: space-around; height: 100%">
                                    <span class="operateBtn" @click="handleView(row)" title="查看" style="display: flex; align-items: center; justify-content: space-around; height: 100%">
                                        <el-icon size="16"> <Document /> </el-icon
                                    ></span>
                                   
                                </div> -->
              <nd-button type="chujin" @click="handleView(row)" v-if="isShowChujin(row, $index)" />
              <nd-button authKey="cost:out:list" type="chakan" @click="handleView(row)" />
            </template>
          </el-table-column>
        </nd-table>
      </template>
      <!-- 底部分页器 -->
      <template #page>
        <nd-pagination
          v-model:current-page="pager.pageNo"
          v-model:page-size="pager.pageSize"
          :total="pager.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </ndb-page-list>
  </div>

  <fundsOutApplicationDialog @refreshData="getListData" ref="fundsOutApplicationDialogRef" />
  <fundsOutAuditDialog @refreshData="getListData" ref="fundsOutAuditDialogRef" />
  <fundsOutHandleDialog @refreshData="getListData" ref="fundsOutHandleDialogRef" />
  <!-- </ndb-page> -->
</template>

<script setup>
import { onMounted, reactive, ref, inject, watch, h, onActivated } from "vue";

import ndButton from "@/components/ndButton.vue";
import ndBadge from "@/components/ndBadge.vue";
import ndTag from "@/components/ndTag.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndAutocomplete from "@/components/ndAutocomplete.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndSearchMore from "@/components/ndSearchMore.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndbPage from "@/components/business/ndbPage/index.vue";
import ndbPageList from "@/components/business/ndbPageList2/index.vue";
import ndbButtonSearch from "@/components/business/ndbButtonSearch.vue";
import ndbButtonReset from "@/components/business/ndbButtonReset.vue";
import ndTabs from "@/components/ndTabs.vue";

import fundsOutApplicationDialog from "./fundsOutApplicationDialog.vue";
import fundsOutAuditDialog from "./fundsOutAuditDialog.vue";
import fundsOutHandleDialog from "./fundsOutHandleDialog.vue";
import ndCascader from "@/components/ndCascader.vue";

const $axios = inject("$axios");

import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const $router = useRouter();
const route = useRoute();

const emits = defineEmits(["setComponents"]);
const pageIpConfig = ref({});
onMounted(() => {
  pageIpConfig.value = window.ipConfig.viewSetup.fundsOutManagementView;
});

const handleView = (row) => {
  emits("setComponents", 2, row);
};

// 操作栏是否显示出金按钮
function isShowChujin(row, index) {
  // 当前选中的费用类型
  const currentFeeType = searchData.feeType;
  
  // 如果没选特定费用类型(全部)，检查所有费用类型
  if (!currentFeeType) {
    let bmStatus = moneyStatus(row.bmExpendExamineStatus, row.bmExpendStatus);
    let fwStatus = moneyStatus(row.fwExpendExamineStatus, row.fwExpendStatus);
    let tbStatus = moneyStatus(row.tbExpendExamineStatus, row.tbExpendStatus);
    let sqStatus = moneyStatus(row.sqExpendExamineStatus, row.sqExpendStatus);
    let lyStatus = moneyStatus(row.lyExpendExamineStatus, row.lyExpendStatus);
    let wkStatus = moneyStatus(row.wkExpendExamineStatus, row.wkExpendStatus);
    let bsfStatus = moneyStatus(row.bsfExpendExamineStatus, row.bsfExpendStatus);
    
    let arr = [bmStatus.statusTxt, fwStatus.statusTxt, tbStatus.statusTxt, sqStatus.statusTxt, lyStatus.statusTxt, wkStatus.statusTxt, bsfStatus.statusTxt];
    return arr.includes("出金申请") || arr.includes("出金审核") || arr.includes("出金处理");
  } else {
    // 根据选中的费用类型检查对应状态
    switch(currentFeeType) {
      case "1": // 材料费
        return checkNeedShow(moneyStatus(row.bmExpendExamineStatus, row.bmExpendStatus));
      case "2": // 服务费
        return checkNeedShow(moneyStatus(row.fwExpendExamineStatus, row.fwExpendStatus));
      case "3": // 投标保证金
        return checkNeedShow(moneyStatus(row.tbExpendExamineStatus, row.tbExpendStatus));
      case "4": // 合同首期款
        return checkNeedShow(moneyStatus(row.sqExpendExamineStatus, row.sqExpendStatus));
      case "5": // 履约保证金
        return checkNeedShow(moneyStatus(row.lyExpendExamineStatus, row.lyExpendStatus));
      case "6": // 合同尾款
        return checkNeedShow(moneyStatus(row.wkExpendExamineStatus, row.wkExpendStatus));
      case "8": // 标书
        return checkNeedShow(moneyStatus(row.bsfExpendExamineStatus, row.bsfExpendStatus));
      default:
        return false;
    }
  }
}

function checkNeedShow(status) {
  return ["出金申请", "出金审核", "出金处理"].includes(status.statusTxt);
}

function setStatusTagColor(status) {
  if (!status) return "";
  if (status.includes("已") || status.includes("成功")) return "success";
  if (status.includes("不") || status.includes("失败")) return "danger";
  if (status.includes("中")) return "warning";
  if (status.includes("待")) return "";
  return "";
}

onActivated(() => {
  console.log("route.query", route.query);
  if (route.query.allOutStatus) tabCheck.value = route.query.allOutStatus;
  getListData();
});

/**
 * @description: 判断当前费用状态
 * @param {*} examineStatus 审核状态
 * @param {*} expendStatus  出金状态
 * @return {*}
 */
function moneyStatus(examineStatus, expendStatus) {
  if (examineStatus == 1 || examineStatus == 4) {
    return { statusTxt: "出金申请", color: "#0B8DF1", cursor: "pointer" };
  }
  if (examineStatus == 2) {
    return { statusTxt: "出金审核", color: "#F56C6C", cursor: "pointer" };
  }
  if ([0, 3].includes(examineStatus) && [1, 10].includes(expendStatus)) {
    return { statusTxt: "出金处理", color: "#F56C6C", cursor: "pointer" };
  }
  if (expendStatus == 2) {
    return { statusTxt: "出金处理中", color: "#444" };
  }
  if (expendStatus == 9) {
    return { statusTxt: "已出金", color: "#444" };
  }
  if (expendStatus == 0) {
    return { statusTxt: "无需出金", color: "#444" };
  }
  return { statusTxt: "--", color: "#444" };
}

const fundsOutApplicationDialogRef = ref();
const fundsOutAuditDialogRef = ref();
const fundsOutHandleDialogRef = ref();

function handlePay(examineStatus, expendStatus, feeId, feeType, row) {
  if (examineStatus == 1 || examineStatus == 4) fundsOutApplicationDialogRef.value.open({ feeId, feeType, proId: row.proId });
  if (examineStatus == 2) fundsOutAuditDialogRef.value.open({ feeId, feeType, proId: row.proId });
  if ([0, 3].includes(examineStatus) && [1, 10].includes(expendStatus)) fundsOutHandleDialogRef.value.open({ feeId, feeType, proId: row.proId });
}

function handleNodeClick(node) {
  searchData.areaId = node.id;
  searchData.name = node.name;
}

// 列表数据
var listData = reactive({
  list: [],
});

// 添加统计数据对象
const staticData = reactive({
  dcjAmountStr:0,
  shzAmountStr:0,
  dclAmountStr:0,
  ycjAmountStr:0
})

// 获取统计数据
function getStaticData() {
    let params = JSON.parse(JSON.stringify(searchData))
    params.allOutStatus = tabCheck.value; // 添加tab状态参数，与列表保持一致
    $axios({
        url: '/bankExpend/collect',
        method: 'get',
        data: { ...params },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        Object.assign(staticData, res.data.data)
    })
}

// tab页签切换
let tabCheck = ref("");
function tabCheckOnChange(val) {
  clearSearch();
  tabCheck.value = val;
  getListData();
}

// 搜索条件
let searchData = reactive({
  searchValue: "",
  tenderCode: "",
  feeType: "3",
  busType: "",
  balanceStatus: "",
  tradeKind: [""],
  signupName: "",
  tradeFlag: "",
  tradeType: "",
  areaId: "",
  proStatus: "",
  flowStatus: "",
  outStatus: "",
});

let pager = reactive({
  // current: 1, // 当前页数
  // size: 30,
  // pages: 1, // 当前页数
  total: 100, // 总条目数
  pageNo: 1,
  pageSize: 10,
});

// 全部
// 待出金4
// 已出金5

let tabTagsMap = reactive([
  { name: "全部", key: "" },
  { name: "待出金", key: "1" },
  { name: "已出金", key: "2" },
]);

// 重置入参
function getListResetParams(replaceKeyObj, params) {
  let keys = Object.keys(replaceKeyObj);
  keys.forEach((item) => {
    params[item] = replaceKeyObj[item];
  });
}

function getListData(replaceKeyObj) {
  let params = JSON.parse(JSON.stringify(searchData));
  params.allOutStatus = tabCheck.value;

  // if (replaceKeyObj) getListResetParams(replaceKeyObj, params)

  $axios({
    url: "/bankExpend/listPage",
    method: "get",
    data: { ...pager, ...params },
    headers: {
      // showStyle: 1,
    },
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    pager.pageNo = res.data.data.current;
    pager.pageSize = res.data.data.size;
    pager.total = res.data.data.total;

    let records = res.data.data.records;

    // listData.list = addChildren(processArray(records) || []);
    listData.list =processArray(records) || [];
    getStaticData()
  });
}

// getListData();

// 纯前端处理列表：把列表变成树形结构
// 给父子级同时添加uuid作为唯一key
// 给父级添加children属性，有两个值：1.缴费类型 2.  缴费状态
function addChildren(list) {
  let treeList = JSON.parse(JSON.stringify(list));

  treeList.forEach((item, index) => {
    let child1 = {
      id: generateUUID(),
      isChild: true,
      childrRow: 1,
      feeChild: item.feeChild,
      proCode: "材料费（元）",
      proName: "服务费（元）",
      tenderCode: "投标保证金（元）",
      tradeKindName: "标书（元）",
      signupName: "合同首期款（元）",
      tradeFlagName: "履约保证金（元）",
      tradeTypeName: "合同尾款（元）",
    };
    let child2 = JSON.parse(JSON.stringify(item));
    child2.id = generateUUID();
    child2.isChild = true;
    child2.childrRow = 2;

    item.id = generateUUID();
    item.parentIndex = (pager.pageNo - 1) * pager.pageSize + index + 1;
    item.children = [child1, child2];
  });

  return treeList;
}

// 生成uuid
function generateUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

const optionsBySearchData = reactive({
  businessTypeOpt: [],
  tradingInstrumentOpt: [],
  transactionMethodOpt: [],
  projectStatusOpt: [],
  flowStatusStatusOpt: [],
  regionFilterOpt: [],
  outStatusOpt: [],
});

// 字典数据
function getBaseCode() {
  let defaultItem = {
    dataValue: "全部",
    dataKey: "",
  };

  // 业务类型 YWLX
  $axios({
    url: "/basecode/getBaseCodeInfo",
    method: "get",
    data: { baseType: "YWLX" },
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    optionsBySearchData.businessTypeOpt = [defaultItem, ...res.data.data];
  });

  // // 交易品种 JYPZ
  // $axios({
  //     url: '/basecode/findAll',
  //     method: 'get',
  //     data: { baseType: 'JYPZ', dataValue6: 3 },
  // }).then((res) => {
  //     if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
  //     optionsBySearchData.tradingInstrumentOpt = [defaultItem, ...res.data.data]
  // })
  // 交易品种
  $axios({
    url: "/project/getVariety?projectType=",
    method: "get",
  }).then((res) => {
    if (res.data.code === 200) {
      optionsBySearchData.tradingInstrumentOpt = [defaultItem, ...res.data.data];
      // optionsBySearchData.tradingInstrumentOpt = [defaultItem]
    }
  });

  // 交易方式 LZFS
  $axios({
    url: "/basecode/getBaseCodeInfo",
    method: "get",
    data: { baseType: "LZFS" },
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    optionsBySearchData.transactionMethodOpt = [defaultItem, ...res.data.data];
  });

  // 项目状态 XMZT_Q
  $axios({
    url: "/basecode/getBaseCodeInfo",
    method: "get",
    data: { baseType: "XMZT_Q" },
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    optionsBySearchData.projectStatusOpt = [defaultItem, ...res.data.data];
  });

  // 标段状态
  $axios({
    url: "/tenders/statusList",
    method: "get",
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    let defaultItem = { value: "全部", key: "" };
    optionsBySearchData.flowStatusStatusOpt = [defaultItem, ...res.data.data];
  });

  // 费用类型 FYLX
  $axios({
    // url: '/basecode/getBaseCodeInfo',
    url: "/bank/getFeeListByArea",
    method: "get",
    data: { baseType: "FYLX" },
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    optionsBySearchData.expenseTypeOpt = [...res.data.data, defaultItem];
  });

  // 出金状态 CJZT_Q
  $axios({
    url: "/basecode/getBaseCodeInfo",
    method: "get",
    data: { baseType: "CJZT_Q" },
  }).then((res) => {
    if (res.data.code !== 200) return ElMessage.warning(res.data.msg);
    optionsBySearchData.outStatusOpt = [defaultItem, ...res.data.data];
  });
}

getBaseCode();

// 地区树
async function isLoad(node, resolve) {
  loadchildnode(node, resolve);
}

// 加载节点
async function loadchildnode(node, resolve) {
  let params = {};
  if (node.level == 0) params = { useToken: 1 };
  else {
    params = {
      areaId: node.data.id,
    };
  }
  let res = await getNextAreaTree(params);

  return resolve(res);
}

function getNextAreaTree(params) {
  return $axios({
    method: "get",
    data: params,
    url: "/area/lazyLoadRegionTree",
  }).then((res) => {
    if (res.data.code == 200) {
      return res.data.data || [];
    }
  });
  return [];
}

// 页面
var page = reactive({
  showSearchMore: false, // 是否显示更多查询条件
});
function showSearchMore() {
  page.showSearchMore = !page.showSearchMore;
}

function handleTableSearch() {
  tabCheck.value = "";
  getListData();
}

/**
 * @description: 给数组中对象属性设置默认值'--'
 * @param {*} array
 * @return {array}
 */
function processArray(array) {
  let includesKey = ["tenderCode", "villageName","identityName"];

  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (includesKey.includes(key)) {
          if (!obj[key] && obj[key] !== 0) {
            obj[key] = "--";
          }
        }
      }
    }
  }
  return array;
}

// 清空查询条件
function clearSearch() {
  searchData.searchValue = "";
  searchData.tenderCode = "";
  searchData.busType = "";
  searchData.balanceStatus = "";
  searchData.tradeKind = [""];
  searchData.signupName = "";
  searchData.tradeFlag = "";
  searchData.tradeType = "";
  searchData.areaId = "";
  searchData.proStatus = "";
  searchData.flowStatus = "";
  searchData.outStatus = "";
  searchData.name = "";
  searchData.moneyStatus = "";
}

// tab切换
const handleTabClick = (tab) => {
  searchData.feeType = optionsBySearchData.expenseTypeOpt[tab.index].dataKey;
  getListData();
};

// 投标保证金 3
// 材料费 1
// 服务费 2
// 合同首期款 4
// 履约保证金 5
// 合同尾款 6
// 标书费 8

// 判断表头是否显示
function tableColShow(dataKey) {
  // 如果费用类型不在选项中，则不显示
  if (
    !optionsBySearchData.expenseTypeOpt
      ?.map((item) => item.dataKey)
      .filter((item) => item)
      .includes(dataKey)
  )
    return false;
  // 如果feeType为空（即选择全部）或者包含当前dataKey，则显示
  if (!searchData.feeType || searchData.feeType === dataKey) return true;
  return false;
}

// 重置查询条件
function resetSearch() {
  clearSearch();
  getListData();
}

const handleSizeChange = (e) => {
  pager.pageSize = e;
  getListData();
};

const handleCurrentChange = (e) => {
  pager.pageNo = e;
  // pager.pages = e
  getListData();
};
</script>
<style lang="scss" scoped>
:deep(.el-message-box) {
    max-width: 580px !important;
}

:deep(.el-select-dropdown__item.hover) {
    background-color: #fff !important;
}

:deep(.el-select-dropdown__item:hover) {
    background-color: #fff !important;
}

:deep(.el-table__row--level-1 td.el-table__cell) {
    border-bottom: none !important;
}

:deep(.el-table__row--level-1) {
    height: 20px !important;
}

/* 偶数 */
:deep(.el-table__row--level-1:nth-of-type(3n+2) .el-table__cell) {
    padding-bottom: 0 !important;
    background: #F9F9F9 !important;
}

/* 奇数 */
:deep(.el-table__row--level-1:nth-of-type(3n) .el-table__cell) {
    padding-top: 0 !important;
    background: #F9F9F9 !important;
}
</style>
<style lang="scss" scoped>
:deep(.el-badge__content.is-fixed) {
  right: calc(20px + var(--el-badge-size) / 1.5);
}

.total {
  color: #606266;
  font-family: MicrosoftYaHeiUI-Bold;
  font-size: 14px;
  margin-bottom: 8px;
  display: flex;
  font-weight: bold;

  .total-detail {
    display: flex;
    margin-right: 40px;

    .right {
      color: #0b8df1;
    }
  }
}

.main-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // margin-top: 15px;
  // padding: 0 15px;
  // padding-left: 10px;
  padding: 15px;
  // background-color: red;
  border: 1px solid #eeeeee;
  border-right: none;

  .search-box {
    display: flex;
    justify-content: space-between;
    height: auto;
    min-height: 40px;
    // margin-top: 10px;
  }

  .search-more-box {
    width: 100%;
    // height: 200px;
    background-color: #31b7f2;
    margin-bottom: 10px;
    // box-sizing: border-box;
    // overflow: hidden;
  }

  .footer-box {
    height: 40px;
    background-color: #f9f9f9;
    padding-right: 10px;
    border: 1px solid #eeeeee;
    // margin-top: 11px;
  }

  .operateBtn {
    color: #0b8df1;
    cursor: pointer;
  }
}

.tip {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #fff3e5;
  display: flex;
  align-items: center;
  padding: 0 16px;

  color: #ff8a00;
  font-family: Microsoft YaHei UI;
  font-size: 14px;
  margin-bottom: 10px;
}
</style>
