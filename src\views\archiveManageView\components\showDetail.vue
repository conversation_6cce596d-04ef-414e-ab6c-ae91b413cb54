<!-- 登记 -->
<template>
  <div class="check-page">
    <div class="details" v-loading="page.loading">
      <el-form ref="FormRef" :model="form" class="nd-add-point-form">
        <!-- 签约设置 -->
        <div class="bg">
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>签约设置</span>
            </div>
          </div>
          <table
            width="100%"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
          >
            <tbody>
              <!-- 第一行 -->
              <tr v-if="form.supportSignType">
                <td class="title" style="width: 13%">签约方式</td>
                <td style="width: 37%">
                  <span class="text1" v-if="form.signType == 1">线上签约</span>
                  <span class="text1" v-if="form.signType == 2">线下签约</span>
                </td>
                <td class="title" style="width: 13%">交易中心是否需要签约</td>
                <td style="width: 37%">
                  <span class="text1" v-if="form.signFlag == 1">是</span>
                  <span class="text1" v-if="form.signFlag === '0'">否</span>
                </td>
              </tr>
              <tr v-else>
                <td class="title" style="width: 13%">签约方式</td>
                <td style="width: 87%">
                  <span class="text1" v-if="form.signType == 1">线上签约</span>
                  <span class="text1" v-if="form.signType == 2">线下签约</span>
                </td>
              </tr>
              <tr v-for="(item, index) in form.outflowList" :key="index">
                <template v-for="(item2, index2) in item" :key="index2">
                  <td class="title" style="width: 13%">
                    {{ item2.paramName }}
                  </td>
                  <td style="width: 37%">
                    <span class="text1" v-if="item2.defaultValue === '1'"
                      >是</span
                    >
                    <span class="text1" v-if="item2.defaultValue === '0'"
                      >否</span
                    >
                  </td>
                </template>
              </tr>
            </tbody>
          </table>
        </div>
        <!--合同信息 -->
        <div class="bg" v-for="item1 in form.initTemplateVo" :key="item1.id">
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>{{ item1.moduleName }}</span>
            </div>
          </div>
          <table
            width="100%"
            ref="tableRef"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
          >
            <tbody>
              <tr>
                <td class="title" style="width: 13%">项目名称</td>
                <td style="width: 37%">
                  <span class="text1">{{ form.projectName }}</span>
                </td>
                <td class="title" style="width: 13%">项目编号</td>
                <td style="width: 37%">
                  <span class="text1">{{ form.projectCode }}</span>
                </td>
              </tr>
              <tr>
                <td class="title" style="width: 13%">标段编号</td>
                <td style="width: 37%">
                  <span class="text1">{{ form.tendersCode }}</span>
                </td>
                <td class="title" style="width: 13%">流转方式</td>
                <td style="width: 37%">
                  <span class="text1">{{ form.tradeType }}</span>
                </td>
              </tr>
              <!-- <ndEye :modelValue="resData?.inflowPartyCard" :secretKey="resData?.inflowPartyCardEncrypt" /> -->
              <template
                v-for="(item, index) in form.initTemplateVo2"
                :key="index"
              >
                <tr v-if="item.length == 2">
                  <template v-for="(item2, index2) in item" :key="index2">
                    <template v-if="item2.paramName">
                      <td class="title" style="width: 13%">
                        {{ item2.paramName }}
                      </td>
                      <td style="width: 37%">
                        <!-- 项目地区 特殊处理 -->
                        <span
                          class="text2"
                          v-if="item2.paramName == '项目位置'"
                          style="color: #0b8df1; cursor: pointer"
                          @click="mapClick"
                          >{{ item2.defaultValue }}</span
                        >
                        <!-- 脱敏处理 -->
                        <ndEye
                          v-else-if="item2.desensitizationFlag"
                          :modelValue="item2.defaultValue"
                          :secretKey="item2.encrypt"
                        />
                        <!-- 无需脱敏 -->
                        <span class="text1" v-else>{{
                          item2.defaultValue
                        }}</span>
                      </td>
                    </template>
                  </template>
                </tr>
                <tr v-else>
                  <template v-for="(item2, index2) in item" :key="index2">
                    <td class="title" style="width: 13%">
                      {{ item2.paramName }}
                    </td>
                    <td style="width: 86.9%" colspan="3">
                      <!-- 项目地区 特殊处理 -->
                      <span
                        class="text2"
                        v-if="item2.paramName == '项目位置'"
                        style="color: 0b8df1; cursor: pointer"
                        @click="mapClick"
                        >{{ item2.defaultValue }}</span
                      >
                      <!-- 脱敏处理 -->
                      <ndEye
                        v-else-if="item2.desensitizationFlag"
                        :modelValue="item2.defaultValue"
                        :secretKey="item2.encrypt"
                      />
                      <!-- 无需脱敏 -->
                      <span class="text1" v-else>{{ item2.defaultValue }}</span>
                    </td>
                  </template>
                </tr>
                <tr v-if="item1.matterFlag === 1 && jybdw === index + 1">
                  <td class="title" style="width: 13%">交易标的物</td>
                  <td style="width: 87%" colspan="3">
                    <div
                      style="
                        margin-bottom: 5px;
                        font-size: 14px;
                        color: #909399;
                      "
                    >
                      本次交易标的数量合计:<span style="color: #606266">{{
                        item1.matterVo.tradeMatterVoList.length
                      }}</span
                      >个，交易面积/数量合计：<span style="color: #606266">{{
                        item1.matterVo.matterArea
                      }}</span
                      >{{ item1.matterVo.matterAreaUnit }}
                    </div>
                    <nd-table
                      style="height: 100%; width: 99.9%"
                      :data="pager.showList"
                      :header-cell-style="{ 'text-align': 'center' }"
                    >
                      <el-table-column
                        align="center"
                        min-width="40"
                        label="序号"
                      >
                        <template #default="{ row }">
                          <div>
                            {{ Number(row.serialNo) + 1 }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        align="left"
                        prop="name"
                        label="标的物名称"
                        v-empty
                      />
                      <el-table-column
                        align="center"
                        prop="code"
                        label="标的物编号"
                        v-empty
                      />
                      <el-table-column
                        align="center"
                        prop="type"
                        label="标的物类型"
                        v-empty
                      />
                      <el-table-column
                        align="right"
                        prop="matterArea"
                        label="交易面积/数量"
                        v-empty
                      />
                      <el-table-column
                        align="center"
                        prop="matterAreaUnit"
                        v-empty
                        label="单位"
                      />
                      <el-table-column
                              align="center"
                              prop="matterPrice"
                              label="交易底价"
                            >
                              <template #default="{ row }">
                                <!-- <div>{{ Number(row.matterPrice)?.toFixed(2) || "--" }}</div> -->
                                <div>
                                  {{
                                    row.matterPrice
                                      ? Number(row.matterPrice).toFixed(2)
                                      : "--"
                                  }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column
                              align="center"
                              prop="matterPriceUnitName"
                              label="价格单位"
                            >
                              <template #default="{ row }">
                                <div>{{ row.matterPriceUnitName || "--" }}</div>
                              </template>
                            </el-table-column>
                      <!-- <el-table-column
                        align="right"
                        prop="matterPrice"
                        label="预期价格(元)"
                        v-empty
                      /> -->
                    </nd-table>
                    <!-- 底部分页器 -->
                    <div style="margin-top: 10px" v-if="pager.total >= 10">
                      <nd-pagination
                        v-model:current-page="pager.pageNo"
                        v-model:page-size="pager.pageSize"
                        :total="pager.total"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                      />
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
        <!--合同付款计划 -->
        <div class="bg">
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>合同付款计划</span>
            </div>
            <!-- <div class="right">
              <nd-button type="primary" size='small' @click="addPlan(1)">新增计划</nd-button>
            </div> -->
          </div>
          <div class="main-box2">
            <nd-table style="height: 100%" :data="form.paymentPlanDtoList">
              <el-table-column
                align="center"
                type="index"
                min-width="40"
                label="序号"
              />
              <el-table-column
                align="center"
                prop="needPayAmount"
                label="金额（元）"
              />
              <el-table-column
                align="center"
                prop="payTime"
                label="计划付款日期"
              />
              <el-table-column
                align="center"
                prop="latePayTime"
                label="付款截止日期"
              />
              <el-table-column align="center" prop="payRemarks" label="说明" />
              <!-- <el-table-column align="center" label="操作">
                <template #default="{row}">
                  <div>
                    <span class="operateBtn" @click="delatePlan(row)">清除</span>
                    <span class="operateBtn" @click="addPlan(2,row)">编辑</span>
                  </div>
                </template>
              </el-table-column> -->
            </nd-table>
          </div>
        </div>
        <!--合同文书 -->
        <div class="bg">
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>合同文书</span>
            </div>
          </div>
          <div class="main-box2">
            <nd-table style="height: 100%" :data="form.wxList">
              <el-table-column
                align="center"
                type="index"
                min-width="40"
                label="序号"
              />
              <el-table-column align="center" prop="modelName" label="文书名称">
                <template #default="{ row }">
                  <div>
                    <span
                      style="cursor: pointer; color: #0098ff; font-size: 14px"
                      @click="writ(2, row, '预览文书')"
                      >{{ row.modelName }}</span
                    >
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="pushStatus"
                label="文书状态"
              >
                <template #default="{ row }">
                  <span v-if="row.dtbId">已生成</span>
                  <span v-else>待生成</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="是否必须生成文书">
                <template #default="{ row }">
                  <span v-if="row.genFlag">是</span>
                  <span v-else>否</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="推送状态">
                <template #default="{ row }">
                  <!-- <span v-if="row.pushFlag == 0">--</span> -->
                  <span v-if="row.pushFlag == 1 && row.pushStatus == 1"
                    >已推送</span
                  >
                  <span v-else-if="row.pushFlag == 1">未推送</span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <!-- <el-table-column align="center" label="操作">
                <template #default="{row}">
                  <div>
                    <span style="cursor: pointer; color: #0098FF;font-size:14px" @click="writ(1,row, '生成文书')" v-if="row.dtbId==null ||row.dtbId==''">生成文书</span>
                    <span style="cursor: pointer; color: #0098FF;font-size:14px" @click="writ(1,row, '重新生成')" v-else>重新生成</span>

                    <span style="cursor: pointer; color: #0098FF;font-size:14px" v-if="row.dtbId && row.pushFlag === 0">推送文书</span>
                    <span style="cursor: pointer; color: #0098FF;font-size:14px" v-if="row.dtbId && row.pushFlag === 1">重新推送</span>
                  </div>
                </template>
              </el-table-column> -->
            </nd-table>
          </div>
        </div>
        <div class="bg">
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>合同签约日期</span>
            </div>
          </div>
          <table
            width="100%"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
          >
            <tbody>
              <!-- 第一行 -->
              <tr class="row" >
                <td class="title" width="150">
                  合同签约日期
                </td>
                <td colspan="3">
                  {{ form.signCompletionTime }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!--合同附件 -->
        <div
          class="bg"
          v-if="
            form.signType == 2 ||
            form.signType == 1 ||
            rowObject.data.status == 7
          "
        >
          <!-- <div class="bg"> -->
          <div class="apply-change-property">
            <div class="left">
              <el-icon>
                <Postcard />
              </el-icon>
              <span>合同附件</span>
            </div>
          </div>
          <table
            width="100%"
            border="0"
            cellspacing="0"
            cellpadding="0"
            class="detailsbd-tc-s"
          >
            <tbody>
              <!-- 第一行 -->
              <tr class="row" v-for="fit in form.fitsList" :key="fit.id">
                <td class="title" width="150">
                  <span class="red" v-if="fit.needChoose == 1">*</span
                  >{{ fit.fileName }}
                </td>
                <td colspan="3">
                  <ndb-upload disabled v-model="fit.fileList"></ndb-upload>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 提交 -->
        <!-- <div class="dialog-footer-box">
          <nd-button class="back" icon="Back" @click="cancelForm"
            >返&nbsp;回</nd-button
          >
        </div> 
        <div class="dialog-footer-box2">
          <nd-button class="back" icon="Back" @click="cancelForm"
            >返&nbsp;回</nd-button
          >
        </div>-->
      </el-form>
    </div>

    <!-- 文书弹框 -->
    <documentDialog ref="documentModifyView" @refresh="getDocList" />

    <!-- 地图选址 -->
    <mapRecord @getAddress="getAddress" :btnShow="false" ref="mapRecordRef"></mapRecord>
  </div>
</template>

<script setup>
// 导入 ================================================================
// 导入公共组件
import ndDialog from "@/components/ndDialog.vue";
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndButton from "@/components/ndButton.vue";
import ndTabs from "@/components/ndTabs.vue";
import ndTable from "@/components/ndTable.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
// import checkInformation from "./checkInformation.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndbUpload from "@/components/business/ndbUpload2/index.vue";
import documentDialog from "@/components/business/ndbWordDocument/index.vue";
import ndPagination from "@/components/ndPagination.vue";
import ndEye from "@/components/ndEye.vue";
// import mapRecord from "@/views/projectRegistrationDetailView/components/mapRecord.vue"; //地图选址
import mapRecord from "../../projectRegistrationEditorView/components/mapRecord.vue"; //地图选址


// 导入element-plus方法
import { ElMessage, ElMessageBox } from "element-plus";
// 定义axios
const $axios = inject("$axios");
import {
  ref,
  inject,
  watch,
  reactive,
  nextTick,
  onUpdated,
  onMounted,
} from "vue";
// 引入处理方法
import { handleDate, handleCheck } from "./handle.js";
const emits = defineEmits(["close", "refresh"]);
const rowObject = inject("rowObject"); // 接受父组件传递过来的数据
const page = reactive({
  loading: false,
});

const props = defineProps({
  params: {
    type: Object,
    default: {},
  },
});

// 定义当前组件的变量 ====================================================
// 定义表单数据
const FormRef = ref(null);
const form = reactive({
  // 签约设置
  supportSignType: 1, // 是否支持线上签约（0-不支持；1-支持）
  signType: "1", // 签约方式（1-线上签约； 2-线下签约）
  signFlag: "1", // 交易中心是否需要签约（0 否； 1 是）
  outflowList: [], // 甲方签约
  // 合同信息
  projectName: "", // 项目名称
  projectCode: "", // 项目编号
  tendersCode: "", // 标段编号
  tradeType: "", //流转编号
  initTemplateVo: [],
  initTemplateVo2: [],
  // 合同付款计划
  paymentPlanDtoList: [],
  // 合同文书
  wxList: [],
  // 合同附件
  fitsList: [],
  // 合同重发
  reason: "",
});
// 项目地区
let address = reactive({
  x: "",
  y: "",
  val: "",
});
// 标的物分页 数据
const pager = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0, // 总条目数
  tradeMatterVoList: [], // 所有数组
  showList: [], // 展示的数组
});
// 分页大小切换
const handleSizeChange = (e) => {
  pager.pageSize = e;
  pager.pageNo = 1;
  pager.showList = pager.tradeMatterVoList.slice(
    (pager.pageNo - 1) * e,
    (pager.pageNo - 1) * e + e
  );
};
// 分页页码切换
const handleCurrentChange = (e) => {
  pager.pageNo = e;
  pager.showList = pager.tradeMatterVoList.slice(
    (e - 1) * pager.pageSize,
    (e - 1) * pager.pageSize + pager.pageSize
  );
};
// 初始化
onMounted(() => {
  // 获取 编辑页初始化参数
  getEditParams();
  // 获取 附件列表
  // getTheAttachmentTypeList();
  // 获取付款计划列表
  getPaymentPlanDtoList();
  // 获取 文书
  getDocList();
});
// /contract/paymentPlanList/{id}

// 获取页面初始化展示数据
const getEditParams = () => {
  page.loading = true;
  $axios({
    url: `/archivesStore/contract`,
    method: "get",
    params: {
      archivesId: props.params.archivesId,
      auditId: props.params.auditId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
    },
  }).then((res) => {
    // console.log(res.data);
    if (res.data.code == 200) {
      console.log("登记-初始化展示数据", res.data.data);
      let {
        signType,
        signFlag,
        initTemplateVo,
        paymentPlanDtoList,
        projectName,
        projectCode,
        tendersCode,
        tradeType,
        resendReason,
        supportSignType,
        outflowList,
        signCompletionTime,
      } = res.data.data;
      if (signType === 1 || signType === 2) {
        form.signType = signType.toString();
      }
      if (signFlag === 0 || signFlag === 1) {
        form.signFlag = signFlag.toString();
      }

      // 对于“不支持线上签约的处理”
      form.supportSignType = supportSignType;

      form.initTemplateVo = initTemplateVo.configFormModuleVoList;

      let outItem = [];
      outflowList.forEach((item) => {
        outItem.push(item);
        if (outItem.length == 2) {
          form.outflowList.push(outItem);
          outItem = [];
        }
      });
      if (outItem.length) form.outflowList.push(outItem);

      form.projectName = projectName;
      form.projectCode = projectCode;
      form.tendersCode = tendersCode;
      form.resendReason = resendReason;
      form.tradeType = tradeType;
      form.signCompletionTime = signCompletionTime ? signCompletionTime : "";
      form.initTemplateVo[0].configFormParamVoList.forEach((item) => {
        if (item.paramKey === "circulationPeriodYear") {
          if (item.defaultValue && item.optionSetValue)
            item.defaultValue =
              item.defaultValue + "年" + item.optionSetValue + "月";
          else if (item.defaultValue)
            item.defaultValue = item.defaultValue + "年";
          else if (item.optionSetValue)
            item.defaultValue = item.optionSetValue + "月";
        } else if (item.controlType == 12 && item.defaultValue)
          item.defaultValue = item.defaultValue + " 至 " + item.optionSetValue;
        // else if (item.controlType == 14 && item.paramKey == 'wanderPrice' && item.optionSetValue) item.defaultValue = item.defaultValue + item.optionSetValue;
        if (item.paramKey == "location" && item.defaultValue) {
          address.x = item.defaultValue[0];
          address.y = item.defaultValue[1];
          address.val = item.defaultValue[2];
          item.defaultValue = item.defaultValue[2];
        }
        // 自定义日期
        if (item.controlType == 3 && item.defaultValue == "sysTime")
          item.defaultValue = getDate();
        // 自定义单位
        if (item.paramUnit)
          item.paramName = item.paramName + "(" + item.paramUnit + ")";
      });

      // 处理标的物数据，分页
      if (
        initTemplateVo.configFormModuleVoList[0].matterVo?.tradeMatterVoList
      ) {
        pager.tradeMatterVoList =
          initTemplateVo.configFormModuleVoList[0].matterVo.tradeMatterVoList;
        pager.total =
          initTemplateVo.configFormModuleVoList[0].matterVo.tradeMatterVoList.length;
        if (pager.total >= 10) {
          pager.showList = pager.tradeMatterVoList.slice(0, pager.pageSize);
        } else {
          pager.showList = pager.tradeMatterVoList;
        }
      }

      // console.log('最后', form.initTemplateVo[0].configFormParamVoList);
    } else {
      ElMessage({
        message: res.data.msg,
        type: "warning",
      });
    }
    page.loading = false;
    changeDom();
    // 获取 附件列表
    getTheAttachmentTypeList();
  });
};
// 获取付款计划列表
const getPaymentPlanDtoList = () => {
  $axios({
    url: `/archivesStore/contractPaymentPlanList`,
    method: "get",
    params: {
      archivesId: props.params.archivesId,
      auditId: props.params.auditId,
      projectId: props.params.projectId,
      tenderId: props.params.tenderId,
    },
  }).then((res) => {
    // console.log(res.data);
    if (res.data.code == 200) {
      console.log("登记-获取付款计划列表", res.data.data);
      form.paymentPlanDtoList = res.data.data;
    } else {
      ElMessage({
        message: res.data.msg,
        type: "warning",
      });
    }
    // page.loading = false;
  });
};
// 获取 查询附件类型列表
const getTheAttachmentTypeList = () => {
  // 线上
  if (form.signType == 1) {
    $axios({
      url: "/archivesStore/getTheAttachmentTypeList",
      method: "post",
      data: {
        archivesId: props.params.archivesId,
        projectId: props.params.projectId,
        auditId: props.params.auditId,
        systemType: "1",
        flowBasecode: "5",
      },
    }).then((res) => {
      console.log("查询线上附件类型列表", res);
      if (res.data.code == 200) {
        form.fitsList = res.data.data;
      }
    });
  }
  // 线下
  if (form.signType == 2) {
    $axios({
      url: "/archivesStore/getTheAttachmentTypeList",
      method: "post",
      data: {
        archivesId: props.params.archivesId,
        projectId: props.params.projectId,
        auditId: props.params.auditId,
        systemType: "1",
        flowBasecode: "5",
      },
    }).then((res) => {
      console.log("查询线下附件类型列表", res);
      if (res.data.code == 200) {
        form.fitsList = res.data.data;
      }
    });
  }
};
// 获取 文书列表
const getDocList = () => {
  let params = {
    archivesId: props.params.archivesId,
    auditId: props.params.auditId,
    projectId: props.params.projectId,
    tenderId: props.params.tenderId,
    flowBaseCode: "5",
    type: "2",
  };
  // console.log('params', params);
  $axios({
    url: "/archivesStore/getDocList",
    method: "post",
    data: params,
  }).then((res) => {
    console.log("获取 文书列表123", res.data);
    if (res.data.code == 200) {
      form.wxList = res.data.data;
    } else {
      ElMessage.warning(res.data.msg);
    }
  });
};
// 文书操作
// 文书操作
const documentModifyView = ref();
const writ = (type, item, title) => {
  console.log(type, item, title);
  // let id = item.dtbId ? item.dtbId : item.id
  if (type == 1) {
    documentModifyView.value.open("", item.id, "2", type, title);
  } else if (type == 2) {
    if (item.dtbId) {
      documentModifyView.value.open("", item.dtbId, "2", type, title);
    } else {
      ElMessage.warning("文书未生成，请生成文书！");
    }
  }
};

// 修改金额大小
const changePriceUpper = (value) => {
  // console.log(form.signType);
  // console.log('修改金额大小', toChies(value));

  form.initTemplateVo[0].configFormParamVoList.forEach((item) => {
    if (item.paramKey === "totalTransationPriceUpper")
      item.defaultValue = toChies(value);
  });
  // console.log('initTemplateVo', form.initTemplateVo[0].configFormParamVoList);
};
// 大写数字过滤器
function toChies(amount) {
  // 汉字的数字
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  const cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  const cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  const cnDecUnits = ["角", "分"];
  // 整数金额时后面跟的字符
  const cnInteger = "整";
  // 整型完以后的单位
  const cnIntLast = "元";
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = "";
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === "") {
    return "";
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return "";
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf(".") === -1) {
    integerNum = amount;

    decimalNum = "";
  } else {
    parts = amount.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== "") {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === "") {
    chineseStr += cnInteger;
  }
  return chineseStr;
}

// 用于 交易标的物 放的位置
let jybdw = ref(null);
// 动态渲染表格
const changeDom = () => {
  let allList = form.initTemplateVo[0].configFormParamVoList;
  let arr = [];
  let arr2 = [];
  for (let index = 0; index < allList.length; index++) {
    if (
      allList[index].paramKey.includes("outflowId") &&
      !allList[index].paramKey.includes("outflowIdType")
    )
      continue;
    if (arr2.length == 2) {
      arr.push(arr2);
      arr2 = [];
    }

    // 如果 本行需要单行显示 那么上一行就要push
    if (!allList[index].paramCells) allList[index].paramCells = 2;
    if (allList[index].paramCells != 1 && arr2.length > 0) {
      if (arr2.length == 1) arr2.push({});
      arr.push(arr2);
      arr2 = [];
    }

    // if (allList[index].controlType == 3 && allList[index].defaultValue != null) {
    //   arr2.push({ paramName: allList[index].paramName, defaultValue: handleDate(allList[index].defaultValue) });
    // }
    // else
    if (allList[index].controlType == 11 && allList[index].defaultValue) {
      arr2.push({
        paramName: allList[index].paramName,
        defaultValue:
          allList[index].defaultValue +
          handleCheck(allList[index].optionSetValue, allList[index].optionList),
      });
    } else if (
      allList[index].controlType == 9 ||
      allList[index].controlType == 8 ||
      allList[index].dictType
    ) {
      arr2.push({
        paramName: allList[index].paramName,
        defaultValue: handleCheck(
          allList[index].defaultValue,
          allList[index].optionList
        ),
      });
    } else if (
      allList[index].controlType == 14 &&
      allList[index].defaultValue &&
      allList[index].paramKey == "wanderPrice"
    ) {
      arr2.push({
        paramName: allList[index].paramName,
        defaultValue:
          allList[index].defaultValue +
          handleCheck(allList[index].optionSetValue, allList[index].optionList),
      });
    } else arr2.push(allList[index]);

    // // 判断单行还是一行
    if (allList[index].paramCells != 1) {
      arr.push(arr2);
      arr2 = [];
    }
  }
  if (arr2.length > 0) arr.push(arr2);
  // 解决 最后一个半行展示问题 添加标识
  if (arr[arr.length - 1].length === 1) arr[arr.length - 1][0].row = 1;
  // console.log('123arr2', arr[arr.length - 1]);
  form.initTemplateVo2 = arr;

  // 用于 交易标的物 放的位置
  for (let index = 0; index < arr.length; index++) {
    const element = arr[index];
    for (let index2 = 0; index2 < element.length; index2++) {
      const element2 = element[index2];
      if (element2.paramName === "流转总价款") {
        jybdw.value = index;
        console.log("index", index);
      }
    }
  }
};

// 提交 =====================================================================
const submitForm = async () => {
  // let arr = [];
  // if (!form.signType) arr.push("签约方式")
  // if (!form.signFlag) arr.push("交易中心是否需要签约")
  // if (!form.projectName) arr.push("项目名称")
  // if (!form.projectCode) arr.push("项目编号")
  // if (!form.tendersCode) arr.push("标段编号")
  // if (!form.tradeType) arr.push("流转编号")

  // form.initTemplateVo.forEach(item => {
  //   item.configFormParamVoList.forEach(item2 => {
  //     if (item2.requireFlag == 1 && !item2.defaultValue && item2.paramKey !== 'code') arr.push(item2.paramName)
  //   })
  // })

  // if (arr.length === 0) submitAndSaveRequire();
  // else ElMessage.error(arr.join(", ") + " 字段为必填项！")
  // submitAndSaveRequire();

  if (fjFlag().length > 0)
    ElMessage.error(fjFlag().join(", ") + " 字段为必填项！");
  else submitAndSaveRequire();
};
// 判断附件是否都必填
const fjFlag = () => {
  if (form.fitsList.length === 0) return [];

  let newList = [];
  form.fitsList.forEach((item) => {
    // console.log(item);
    if (item.needChoose == 1 && item.fileList.length == 0)
      newList.push(item.fileName);
  });
  return newList;
  // console.log(newList);
};
// 表单 提交保存 请求接口
const submitAndSaveRequire = () => {
  let params = {
    id: rowObject.data.id,
    signFlag: form.signFlag,
    signType: form.signType,
    outflowList: [],
    resendFlag: 2,
    resendReason: form.resendReason,
  };
  let object = {};
  // 处理数据
  form.initTemplateVo.forEach((item) => {
    item.configFormParamVoList.forEach((item2) => {
      if (item2.paramKey.includes("outflow"))
        object[item2.paramKey] = item2.defaultValue;
      else params[item2.paramKey] = item2.defaultValue;
    });
  });
  for (const key in object) {
    let index = key.substring(key.length - 1);

    if (params.outflowList[index - 1]) {
      params.outflowList[index - 1][key.substring(0, key.length - 1)] =
        object[key];
    } else {
      params.outflowList[index - 1] = {};
      params.outflowList[index - 1][key.substring(0, key.length - 1)] =
        object[key];
    }
  }
  // 校验
  // let result = params.outflowList.some(item => {
  //   // console.log('12222', item.outflowSignFlag);
  //   return item.outflowSignFlag == 1;
  // })
  // if (!result) {
  //   ElMessage.error("甲方确认签约人")
  //   return;
  // }
  console.log("合同上传 - 提交参数", params);

  // let url = '';
  // if (rowObject.data.flag === 1) url = "/contract/add";
  // if (rowObject.data.flag === 2) url = `/contract/repeat/${rowObject.data.id}`;
  $axios({
    url: "/contract/add", // 1登记 2合同重发
    method: "post",
    data: params,
  }).then((res) => {
    console.log(`合同上传 返回结果res`, res.data);
    if (res.data.code == 200) {
      ElMessage.success(`上传成功！`);
      close(); // 关闭编辑页面
      emits("refresh");
      // if (rowObject.data.flag == 2) {
      // }
    } else {
      ElMessage.warning(res.data.msg);
    }
  });
};
// 取消
const cancelForm = () => {
  // ElMessageBox.confirm(
  //   '您确定要关闭当前界面吗，已填写内容将无法保存。',
  //   '提示',
  //   {
  //     confirmButtonText: '确认',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //   }
  // )
  //   .then(() => {
  close();
  // })
};

// 删除计划
const delatePlan = (row) => {
  $axios({
    url: `/contract/paymentPlanDelete/${row.id}`,
    method: "get",
  }).then((res) => {
    console.log(`返回结果res`, res.data);
    if (res.data.code == 200) {
      ElMessage.success(`删除成功！`);
      getPaymentPlanDtoList();
    }
  });
};

// 项目地区
const mapRecordRef = ref(null);
const mapClick = () => {
  mapRecordRef.value.open(address);
};

// 关闭页面
const close = () => {
  emits("close");
};
</script>

<style lang="scss" scoped>
.content:hover {
  cursor: not-allowed;
}

::v-depp .el-tooltip__trigger {
  width: 100% !important;
}

.el-form-item {
  margin-bottom: 0 !important;
}

.check-page {
  width: 100%;
  // border: 1px solid red;
  // padding: 0 15px 15px;

  .red {
    color: red;
    margin-right: 5px;
  }

  overflow: auto;
  overflow-x: hidden;
  // 暂无数据

  .bg {
    background-color: #ffffff;
    padding: 12px 15px 15px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border: 1px solid #eee;

    .main-box {
      width: 100%;
      // height: 100px;
      // background-color: red;
      // padding: 10px 209px;
      padding: 10px 13%;

      .row {
        display: flex;
        // height: 32px;
        margin-bottom: 16px;

        .col {
          // flex-grow: 1;
          width: 50%;
          // border: 1px solid red;
          display: flex;
          align-items: center;
          // margin-right: 10px;

          .title {
            width: 140px;
            margin-right: 10px;
            text-align: right;
          }
        }

        .fileName {
          margin-right: 20px;
          font-size: 12px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #4092f2;
          font-style: normal;
          word-break: break-all;
          cursor: pointer;
        }
      }

      .content {
        flex-grow: 1;
        // background-color: #0B8DF1;
        border: 1px solid #dcdfe6;
        border-radius: 5px;
        height: 30px;
        color: #a8a9ab;
        padding: 0 0 0 10px;
        font-size: 14px;
        line-height: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .main-box2 {
      width: 100%;
      padding: 10px 0;
      // background-color: #0b8df1;

      .operateBtn {
        cursor: pointer;
        color: #31b7f2;
        margin: 0 10px;
      }
    }
    .main-box3 {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;
      padding: 10px 13%;
    }

    .detailsbd-tc-s {
      // width: 100%;
      // padding: 10px 0;
      margin-top: 10px;
      border-left: 1px solid #eeeeee;
      border-top: 1px solid #eeeeee;

      td {
        border-right: 1px solid #eeeeee;
        border-bottom: 1px solid #eeeeee;
        padding: 7px 8px;
        // line-height: 20px;
        text-align: left;
        font-size: 14px;
        color: #444444;
        background: #fff;

        .redStar {
          color: red;
        }
      }

      .title {
        color: #606266;
        text-align: right;
        background-color: #f9f9f9;
        // min-width: 125px;
        // min-height: 34px;
        font-size: 14px;
      }
    }
  }

  .apply-change-property {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // height: 24px;
    // background-color: red;

    .left {
      display: flex;
      align-items: center;
      // width: 90px;
      height: 24px;
      // background-color: wheat;
      font-size: 16px;
      font-weight: bold;
      color: #0b8df1;

      span {
        margin-left: 8.75px;
        font-size: 16px;
        font-weight: bold;
        color: #0b8df1;
      }
    }
  }

  .text {
    font-size: 12px;
    color: #a9a9a9;
    margin-right: 10px;
  }
}

.dialog-footer-box {
  // position: absolute;
  // left: 0;
  // bottom: 0;
  width: 100%;
  height: 60px;
  // background-color: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  // border-top: 1px solid #e6e6e6;
}
.dialog-footer-box2 {
  position: fixed;
  top: 68px;
  right: 0;
  :deep(.el-button) {
    border: transparent;
    background: transparent;
  }
}

// .iconColor {
//   :deep(.el-icon) {
//     color: red !important;
//   }
// }

.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style>