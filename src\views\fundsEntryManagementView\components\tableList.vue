<!-- fundsEntryManagementView 入金管理 -->
<template>
    <!-- <ndb-page> -->
    <div class="main-box xz-box">
        <ndb-page-list>
            <template #tab>
                <nd-tabs :tabList="optionsBySearchData.expenseTypeOpt?.map(item => item.dataValue)"
                    @tab-click="handleTabClick" />
            </template>
            <template #tag>
                <el-badge v-for="item in tabTagsMap" :key="item.key" type="primary"
                    :value="tabCheck === item.key ? pager.total : ''" :max="99" :offset="[-10, 5]">
                    <nd-tag style="margin-right: 10px" :checked="item.key == tabCheck"
                        @change="tabCheckOnChange(item.key)">{{ item.name }}</nd-tag>
                </el-badge>
            </template>
            <template #search>
                <nd-search-more arrowMarginLeft="296px">
                    <nd-search-more-item title="项目查询" width="120px">
                        <ndInput placeholder="请输入项目名称/项目编号" v-model.trim="searchData.searchValue" clearable />
                    </nd-search-more-item>
                    <nd-search-more-item title="标段编号" width="120px">
                        <ndInput placeholder="请输入" v-model.trim="searchData.tenderCode" clearable />
                    </nd-search-more-item>
                    <nd-search-more-item title="业务类型" width="120px">
                        <nd-select v-model="searchData.busType">
                            <el-option v-for="item in optionsBySearchData.businessTypeOpt" :key="item.value"
                                :label="item.dataValue" :value="item.dataKey" />
                        </nd-select>
                    </nd-search-more-item>
                    <!-- <nd-search-more-item title="费用类型" width="120px">
                        <nd-select v-model="searchData.feeType">
                            <el-option v-for="item in optionsBySearchData.expenseTypeOpt" :key="item.value"
                                :label="item.dataValue" :value="item.dataKey" />
                        </nd-select>
                    </nd-search-more-item> -->
                    <nd-search-more-item title="金额状态" width="120px">
                        <nd-select v-model="searchData.moneyStatus">
                            <el-option v-for="item in optionsBySearchData.amountStatusOpt" :key="item.value"
                                :label="item.dataValue" :value="item.dataKey" />
                        </nd-select>
                    </nd-search-more-item>
                    <nd-search-more-item title="交易品种" width="120px">
                        <!-- <nd-select v-model="searchData.tradeKind">
                                <el-option v-for="item in optionsBySearchData.tradingInstrumentOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
                            </nd-select> -->
                        <ndCascader v-model="searchData.tradeKind" :options="optionsBySearchData.tradingInstrumentOpt"
                            :props="{ label: 'dataValue', value: 'dataKey', children: 'childList', checkStrictly: true }"
                            style="width: 100%" clearable></ndCascader>
                    </nd-search-more-item>
                    <nd-search-more-item title="缴款人" width="120px">
                        <ndInput placeholder="请输入" v-model.trim="searchData.signupName" clearable />
                    </nd-search-more-item>
                    <nd-search-more-item title="是否成交" width="120px">
                        <nd-select v-model="searchData.tradeFlag">
                            <el-option label="全部" value="" />
                            <el-option label="是" :value="1" />
                            <el-option label="否" :value="0" />
                        </nd-select>
                    </nd-search-more-item>
                    <nd-search-more-item title="交易方式" width="120px">
                        <nd-select v-model="searchData.tradeType">
                            <el-option v-for="item in optionsBySearchData.transactionMethodOpt" :key="item.value"
                                :label="item.dataValue" :value="item.dataKey" />
                        </nd-select>
                    </nd-search-more-item>
                    <nd-search-more-item title="地区筛选" width="120px">
                        <el-tree-select style="width: 100%" v-model="searchData.areaId" lazy :load="isLoad"
                            :props="{ label: 'name', children: 'children', isLeaf: 'isLeaf' }"
                            @node-click="handleNodeClick" node-key="id" accordion check-strictly clearable />
                    </nd-search-more-item>
                    <nd-search-more-item title="项目状态" width="120px">
                        <nd-select v-model="searchData.flowStatus">
                            <el-option v-for="item in optionsBySearchData.flowStatusStatusOpt" :key="item.value"
                                :label="item.value" :value="item.key" />
                        </nd-select>
                    </nd-search-more-item>
                    <!-- <nd-search-more-item title="项目状态" width="120px">
                            <nd-select v-model="searchData.proStatus">
                                <el-option v-for="item in optionsBySearchData.projectStatusOpt" :key="item.value" :label="item.dataValue" :value="item.dataKey" />
                            </nd-select>
                        </nd-search-more-item> -->
                    <template #footer>
                        <!-- <nd-button type="primary" @click="handleTableSearch">查询</nd-button>
                            <nd-button @click="resetSearch">重置</nd-button> -->
                        <ndb-button-search @click="handleTableSearch"></ndb-button-search>
                        <ndb-button-reset @click="resetSearch"></ndb-button-reset>
                    </template>
                </nd-search-more>
            </template>
            <template #statistics>
                <div class="total">
                    <div class="total-detail">
                        <div class="left">待缴款（元）：</div>
                        <div class="right">{{ staticData.djkAmountStr || 0 }}</div>
                    </div>
                    <div class="total-detail">
                        <div class="left">缴款入账中（元）：</div>
                        <div class="right">{{ staticData.rzzAmountStr || 0 }}</div>
                    </div>
                    <div class="total-detail">
                        <div class="left">已缴款待确认（元）：</div>
                        <div class="right">{{ staticData.dqrAmountStr || 0 }}</div>
                    </div>
                    <div class="total-detail">
                        <div class="left">已缴款（万元）：</div>
                        <div class="right">{{ staticData.yjkAmountStr || 0 }}</div>
                    </div>
                    <div class="total-detail">
                        <div class="left">过期未缴款（万元）：</div>
                        <div class="right">{{ staticData.gqAmountStr || 0 }}</div>
                    </div>
                </div>
            </template>
            <!-- 主体表格 -->
            <template #table>
                <nd-table style="height: 100%; width: 100%" :data="listData.list">
                    <el-table-column align="center" type="index" width="60" label="序号"
                        :index="(index) => (pager.pageNo - 1) * pager.pageSize + index + 1" fixed />
                    <el-table-column header-align="center" prop="proCode" label="项目编号" width="170" show-overflow-tooltip
                        fixed />
                    <el-table-column header-align="center" prop="proName" label="项目名称" show-overflow-tooltip
                        min-width="180" />
                    <el-table-column align="center" prop="tenderCode" label="标段" width="60" />
                    <el-table-column align="center" prop="busTypeName" label="业务类型" width="140"
                        v-if="!pageIpConfig.busTypeName" />

                    <el-table-column align="center" prop="signupName" label="缴款人" width="80" show-overflow-tooltip />
                    <el-table-column align="center" prop="identityName" label="缴款人身份" width="95"
                        show-overflow-tooltip />
                    <el-table-column align="center" prop="tradeFlagName" label="是否成交" min-width="75" />

                    <!-- 表头费用列显示有两个条件：tab选中的费用，和tab包含的费用 -->
                    <el-table-column align="center" prop="tbAmount" label="投标保证金（元）" min-width="190"
                        show-overflow-tooltip v-if="tableColShow('3')">
                        <template #default="{ row }">
                            <span @click="handlePay(row.tbPayStatus, row.tbFeeId, row)"
                                :style="{ color: fwPayStatusColorMap[row.tbPayStatus], cursor: payStatusPoniterMap[row.tbPayStatus] }">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.tbPayStatus] }}
                                </span>
                                <!-- <span v-if="parseFloat(row.tbAmount + ' '.replace(/,/g, '')) > 0"> （{{ row.tbAmount }}） -->
                                <span> （{{ row.tbAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="fwAmount" label="材料费（元）" min-width="190" show-overflow-tooltip
                        v-if="tableColShow('1')">
                        <template #default="{ row }">
                            <span class="money-td"
                                :style="{ color: fwPayStatusColorMap[row.bmPayStatus], cursor: payStatusPoniterMap[row.bmPayStatus] }"
                                @click="handlePay(row.bmPayStatus, row.bmFeeId, row)">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.bmPayStatus] }}
                                </span>
                                <!-- <span v-if="parseFloat(row.bmAmount + ''.replace(/,/g, '')) > 0"> （{{ row.bmAmount }}） -->
                                <span> （{{ row.bmAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="fwAmount" label="服务费（元）" min-width="190" show-overflow-tooltip
                        v-if="tableColShow('2')">
                        <template #default="{ row }">
                            <span
                                :style="{ color: fwPayStatusColorMap[row.fwPayStatus], cursor: payStatusPoniterMap[row.fwPayStatus] }"
                                @click="handlePay(row.fwPayStatus, row.fwFeeId, row)">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.fwPayStatus] }}
                                </span>
                                <!-- <span v-if="parseFloat(row.fwAmount + ' '.replace(/,/g, '')) > 0"> （{{ row.fwAmount }}） -->
                                <span> （{{ row.fwAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="sqAmount" label="合同首期款（元）" min-width="190"
                        show-overflow-tooltip v-if="tableColShow('4')">
                        <template #default="{ row }">
                            <span @click="handlePay(row.sqPayStatus, row.sqFeeId, row)"
                                :style="{ color: fwPayStatusColorMap[row.sqPayStatus], cursor: payStatusPoniterMap[row.sqPayStatus] }">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.sqPayStatus] }}
                                </span>
                                <!-- <span v-if="parseFloat(row.sqAmount + ' '.replace(/,/g, '')) > 0"> （{{ row.sqAmount }}） -->
                                <span> （{{ row.sqAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="lyAmount" label="履约保证金（元）" min-width="190"
                        show-overflow-tooltip v-if="tableColShow('5')">
                        <template #default="{ row }">
                            <span @click="handlePay(row.lyPayStatus, row.lyFeeId, row)"
                                :style="{ color: fwPayStatusColorMap[row.lyPayStatus], cursor: payStatusPoniterMap[row.lyPayStatus] }">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.lyPayStatus] }}
                                </span>
                                <!-- <span v-if="parseFloat(row.lyAmount + ' '.replace(/,/g, '')) > 0">（{{ row.lyAmount }}） -->
                                <span>（{{ row.lyAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="wkAmount" label="合同尾款（元）" min-width="190"
                        show-overflow-tooltip v-if="tableColShow('6')">
                        <template #default="{ row }">
                            <span @click="handlePay(row.wkPayStatus, row.wkFeeId, row)"
                                :style="{ color: fwPayStatusColorMap[row.wkPayStatus], cursor: payStatusPoniterMap[row.wkPayStatus] }">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.wkPayStatus] }}
                                </span>
                                <span>（ {{ row.wkAmount }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="wkAmount" label="标书（元）" min-width="190" show-overflow-tooltip
                        v-if="tableColShow('8')">
                        <template #default="{ row }">
                            <span @click="handlePay(row.bsfPayStatus, row.bsfFeeId, row)"
                                :style="{ color: fwPayStatusColorMap[row.bsfPayStatus], cursor: payStatusPoniterMap[row.bsfPayStatus] }">
                                <span style="margin-right: 5px">
                                    {{ fwPayStatusMap[row.bsfPayStatus] }}
                                </span>
                                <span>（ {{ row.bsfAmount
                                    }}）
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="tradeKindName" label="交易品种" width="200"
                    show-overflow-tooltip />
                    <el-table-column align="center" prop="tradeTypeName" label="交易方式" width="100">
                        <template #default="{ row }">
                            <span>{{ row.tradeTypeName || '--' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="countyName" label="县（市、区）" show-overflow-tooltip width="120"
                        v-if="!pageIpConfig.countyName" />
                    <el-table-column align="center" prop="townName" label="乡镇（街道）" show-overflow-tooltip width="120"
                        v-if="!pageIpConfig.townName" />
                    <el-table-column align="center" prop="villageName" label="村（社区）" show-overflow-tooltip width="160"
                        v-if="!pageIpConfig.villageName" />
                    <el-table-column align="center" prop="flowStatusName" label="项目状态" width="180">
                        <template #default="{ row }">
                            <el-tag v-if="row.flowStatusName" :type="setStatusTagColor(row.flowStatusName)">{{
                                row.flowStatusName }}</el-tag>
                            <span v-else :type="setStatusTagColor(row.flowStatusName)">--</span>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" align="center" prop="message10" label="操作" width="150">
                        <template #default="{ row }">
                            <nd-button v-if="isShowPayBtn(row)" @click="handkeGroupPay(row)" authKey="cost:in:order"
                                type="payIn"></nd-button>
                            <nd-button @click="handleView(row)" type="chakan"></nd-button>
                            <!-- <div style="display: flex; align-items: center; justify-content: space-around; height: 100%"> -->
                            <!-- <span
                                        title="缴款"
                                        v-if="isShowPayBtn(row)"
                                        class="operateBtn"
                                        style="display: flex; align-items: center; justify-content: space-around; height: 100%"
                                        @click="handkeGroupPay(row)"
                                    >
                                        <el-icon size="16">
                                            <Wallet />
                                        </el-icon>
                                    </span> -->

                            <!-- <span class="operateBtn" @click="handleView(row)" style="display: flex; align-items: center; justify-content: space-around; height: 100%" title="查看">
                                        <el-icon size="16"> <Document /> </el-icon
                                    ></span> -->
                            <!-- </div> -->
                        </template>
                    </el-table-column>
                </nd-table>
            </template>
            <!-- 底部分页器 -->
            <template #page>
                <nd-pagination v-model:current-page="pager.pageNo" v-model:page-size="pager.pageSize"
                    :total="pager.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </template>
        </ndb-page-list>
    </div>
    <payZzhkDialog @refreshData="getListData" ref="payZzhkDialogRef" />
    <yjkDialog @refreshData="getListData" ref="yjkDialogRef" />
    <!-- </ndb-page> -->
</template>

<script setup>
import { onMounted, reactive, ref, inject, watch, h, onActivated, computed } from 'vue'

import ndButton from '@/components/ndButton.vue'
import ndBadge from '@/components/ndBadge.vue'
import ndTag from '@/components/ndTag.vue'
import ndSelect from '@/components/ndSelect.vue'
import ndInput from '@/components/ndInput.vue'
import ndTable from '@/components/ndTable.vue'
import ndAutocomplete from '@/components/ndAutocomplete.vue'
import ndDatePicker from '@/components/ndDatePicker.vue'
import ndPagination from '@/components/ndPagination.vue'
import ndSearchMore from '@/components/ndSearchMore.vue'
import ndSearchMoreItem from '@/components/ndSearchMoreItem.vue'
import ndbPage from '@/components/business/ndbPage/index.vue'
import ndbPageList from '@/components/business/ndbPageList2/index.vue'
import ndbButtonSearch from '@/components/business/ndbButtonSearch.vue'
import ndbButtonReset from '@/components/business/ndbButtonReset.vue'
import ndCascader from '@/components/ndCascader.vue'
import ndTabs from "@/components/ndTabs.vue";

import payZzhkDialog from './payZzhkDialog.vue'
import yjkDialog from './yjkDialog.vue'

const $axios = inject('$axios')
const pageIpConfig = ref({})
onMounted(() => {
    pageIpConfig.value = window.ipConfig.viewSetup.fundsEntryManagementView

});
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from "vue-router";
const $router = useRouter();
const route = useRoute();

const emits = defineEmits(['setComponents'])

const handleView = (row) => {
    emits('setComponents', 2, row)
}

onActivated(() => {
    console.log('route.query', route.query);
    if (route.query.moneyStatus) tabCheck.value = searchData.moneyStatus = route.query.moneyStatus;
    getListData()
})

function setStatusTagColor(status) {
    if (!status) return ''
    if (status.includes('已') || status.includes('成功')) return 'success'
    if (status.includes('不') || status.includes('失败')) return 'danger'
    if (status.includes('中')) return 'warning'
    if (status.includes('待')) return ''
    return ''
}
const fwPayStatusMap = {
    0: '无需缴款',
    1: '待缴款',
    2: '缴款入账中',
    9: '已缴款',
    10: '过期未缴款',
    _dqr: '已缴款待确认',
    _yqr: '已缴款',
}
// 带缴款和待确认状态需要高亮显示
const fwPayStatusColorMap = {
    0: '#444444',
    1: '#F56C6C',
    2: '#444444',
    9: '#444444',
    10: '#444444',
    _dqr: '#67C23A',
    _yqr: '#444444',
}
const payStatusPoniterMap = {
    1: 'pointer',
    _dqr: 'pointer',
    // _yqr: "pointer",
}

// 判断是否是待确认状态:缴款状态是9  入金确认状态是2的时候，显示待确认
function checkDqrStatus(row) {
    // 报名费  bmPayStatus:9   bmIncomeExamineStatus:2
    // 服务费  fwPayStatus     fwIncomeExamineStatus
    // 履约保证金 lyPayStatus  lyIncomeExamineStatus
    // 首期 sqPayStatus        sqIncomeExamineStatus
    // 投标保证金 tbPayStatus  tbIncomeExamineStatus
    // 尾款 wkPayStatus        wkIncomeExamineStatus
    if (row.bmPayStatus == 9 && row.bmIncomeExamineStatus == 2) row.bmPayStatus = '_dqr'
    if (row.fwPayStatus == 9 && row.fwIncomeExamineStatus == 2) row.fwPayStatus = '_dqr'
    if (row.lyPayStatus == 9 && row.lyIncomeExamineStatus == 2) row.lyPayStatus = '_dqr'
    if (row.sqPayStatus == 9 && row.sqIncomeExamineStatus == 2) row.sqPayStatus = '_dqr'
    if (row.tbPayStatus == 9 && row.tbIncomeExamineStatus == 2) row.tbPayStatus = '_dqr'
    if (row.wkPayStatus == 9 && row.wkIncomeExamineStatus == 2) row.wkPayStatus = '_dqr'

    // 已确认
    if (row.bmPayStatus == 9 && row.bmIncomeExamineStatus == 3) row.bmPayStatus = '_yqr'
    if (row.fwPayStatus == 9 && row.fwIncomeExamineStatus == 3) row.fwPayStatus = '_yqr'
    if (row.lyPayStatus == 9 && row.lyIncomeExamineStatus == 3) row.lyPayStatus = '_yqr'
    if (row.sqPayStatus == 9 && row.sqIncomeExamineStatus == 3) row.sqPayStatus = '_yqr'
    if (row.tbPayStatus == 9 && row.tbIncomeExamineStatus == 3) row.tbPayStatus = '_yqr'
    if (row.wkPayStatus == 9 && row.wkIncomeExamineStatus == 3) row.wkPayStatus = '_yqr'

    return row
}
// 判断是否显示缴款按钮
function isShowPayBtn(row) {
    // 只有当费用类型选择"全部"时(feeType为空字符串，tab为全部)才显示缴款按钮
    if (searchData.feeType !== '') return false
    
    // 判断是否有任何一项需要缴款
    if (row.bmPayStatus == 1) return true
    if (row.fwPayStatus == 1) return true
    if (row.lyPayStatus == 1) return true
    if (row.sqPayStatus == 1) return true
    if (row.tbPayStatus == 1) return true
    if (row.wkPayStatus == 1) return true
    if (row.bsfPayStatus == 1) return true

    return false
}

const payZzhkDialogRef = ref()
const yjkDialogRef = ref()
function handlePay(satatus, feeId, row) {
    console.log(satatus, feeId, row)
    row.feeId = feeId
    if (satatus == '1') {
        payZzhkDialogRef.value.open(row)
    }
    if (satatus == '_dqr') {
        //已缴款待确认
        yjkDialogRef.value.open(row)
    }
}

// 批量缴款
function handkeGroupPay({ proId, signupId, tenderId }) {
    let params = {
        proId: proId || '',
        signupId: signupId || '',
        tenderId: tenderId || '',
    }
    $axios({
        url: '/bankIncome/costPayMoreDetail',
        method: 'get',
        data: params,
    }).then((res) => {
        if (res.data.code == 200) {
            // 判断是否需要提醒弹窗
            if (!res.data.data.needTx) return payZzhkDialogRef.value.open(res.data.data, 2)
            ElMessageBox({
                title: '温馨提示',
                // message: h('p', { style: 'width:580px' }, [
                //     h('span', { style: 'color:#000' }, '因为资金配置里【缴款过期未到账】配置的是入账时作为异常打款，原路退回。'),
                //     h('br', null, ''),
                //     h('span', { style: 'color:#000' }, '所以会按照所选费用类型，最近的截止缴款时间'),
                //     h('span', { style: 'color:#000' }, '去控制缴款，否则所有费用全部原路退回。'),
                //     h('div', { style: 'color:#000' }, '请确认是否使用批量缴款?'),
                // ]),
                message: h('p', { style: 'width:580px' }, [
                    h('span', { style: 'color:#000' }, '批量缴款订单中，如果有一个费用过了缴款截止时间，该笔订单则作废，该费用缴款状态'),
                    h('br', null, ''),
                    h('span', { style: 'color:#000' }, '变为过期未缴款，剩下未到缴款截止时间的费用则变为待缴款，可继续操作去缴款。'),
                    h('div', { style: 'color:#000' }, '请确认是否使用批量缴款?'),
                ]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                appendTo: '.xz-box',
            }).then(() => {
                payZzhkDialogRef.value.open(res.data.data, 2)
            })
        } else {
            ElMessageBox.alert(res.data.msg, '温馨提示', { confirmButtonText: '知道了', center: true })
        }
    })
}

// 列表数据
const listData = reactive({
    list: [],
})

// 添加统计数据对象
const staticData = reactive({
    djkAmountStr: '0', // 待缴款
    rzzAmountStr: '0', // 缴款入账中
    dqrAmountStr: '0', // 已缴款待确认
    yjkAmountStr: '0', // 已缴款
    gqAmountStr: '0',  // 过期未缴款
})

// 获取统计数据
function getStaticData() {
    let params = JSON.parse(JSON.stringify(searchData))
    $axios({
        url: '/bankIncome/collect',
        method: 'get',
        data: { ...params },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        Object.assign(staticData, res.data.data)
    })
}

// tab页签切换
let tabCheck = ref('')

let tabTagsMap = reactive([
    { name: '全部', key: '' },
    { name: '待缴款', key: '1' },
    { name: '待确认', key: '99' },
    { name: '已缴款', key: '9' },
])

function tabCheckOnChange(val) {
    clearSearch()
    tabCheck.value = searchData.moneyStatus = val
    getListData()
}

// 搜索条件
let searchData = reactive({
    searchValue: '',
    tenderCode: '',
    busType: '',
    feeType: '3',//默认投标保证金
    moneyStatus: tabCheck.value,
    tradeKind: [''],
    signupName: '',
    tradeFlag: '',
    tradeType: '',
    areaId: '',
    proStatus: '',
    flowStatus: '',
})

let pager = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 100, // 总条目数
})

// 重置入参
// function getListResetParams(replaceKeyObj, params) {
//     let keys = Object.keys(replaceKeyObj)
//     keys.forEach((item) => {
//         params[item] = replaceKeyObj[item]
//     })
// }

function getListData() {
    let params = JSON.parse(JSON.stringify(searchData))

    // if (replaceKeyObj) getListResetParams(replaceKeyObj, params)

    $axios({
        url: '/bankIncome/listPage',
        method: 'get',
        // headers: {
        //     showStyle: 1,
        // },
        data: { ...pager, ...params },
    }).then(async (res) => {
        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        pager.pageNo = res.data.data.current
        pager.pageSize = res.data.data.size
        pager.total = res.data.data.total

        let records = res.data.data.records

        records.forEach((item) => {
            item = checkDqrStatus(item)
        })
        // listData.list = addChildren(processArray(records) || [])
        listData.list = processArray(records) || []
        // 获取统计数据
        getStaticData()
        // console.log(listData.list, 'listData.list')
    })
}

// getListData()


// 纯前端处理列表：把列表变成树形结构
// 给父子级同时添加uuid作为唯一key
// 给父级添加children属性，有两个值：1.缴费类型 2.  缴费状态
function addChildren(list) {
    let treeList = JSON.parse(JSON.stringify(list))

    treeList.forEach((item, index) => {
        let child1 = {
            'id': generateUUID(),
            'isChild': true,
            'childrRow': 1,
            "feeChild": item.feeChild,
            'proCode': '材料费（元）',
            'proName': '服务费（元）',
            'tenderCode': '投标保证金（元）',
            'tradeKindName': '标书（元）',
            'signupName': '合同首期款（元）',
            'tradeFlagName': '履约保证金（元）',
            'tradeTypeName': '合同尾款（元）',
        }
        let child2 = JSON.parse(JSON.stringify(item))
        child2.id = generateUUID()
        child2.isChild = true
        child2.childrRow = 2


        item.id = generateUUID()
        item.parentIndex = (pager.pageNo - 1) * pager.pageSize + index + 1
        item.children = [child1, child2]
    })

    return treeList
}

// 生成uuid
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
    })
}

const optionsBySearchData = reactive({
    businessTypeOpt: [],
    expenseTypeOpt: [],
    amountStatusOpt: [],
    tradingInstrumentOpt: [],
    transactionMethodOpt: [],
    projectStatusOpt: [],
    flowStatusStatusOpt: [],
    regionFilterOpt: [],
})

// 字典数据
function getBaseCode() {
    let defaultItem = {
        dataValue: '全部',
        dataKey: '',
    }

    // 业务类型 YWLX
    $axios({
        url: '/basecode/getBaseCodeInfo',
        method: 'get',
        data: { baseType: 'YWLX' },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        optionsBySearchData.businessTypeOpt = [defaultItem, ...res.data.data]
    })

    // 费用类型 FYLX
    $axios({
        // url: '/basecode/getBaseCodeInfo',
        url: '/bank/getFeeListByArea',
        method: 'get',
        data: { baseType: 'FYLX' },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        optionsBySearchData.expenseTypeOpt = [...res.data.data, defaultItem]
    })

    // 交易品种 JYPZ
    // $axios({
    //     url: '/basecode/findAll',
    //     method: 'get',
    //     data: { baseType: 'JYPZ', dataValue6: 3 },
    // }).then((res) => {
    //     if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
    //     optionsBySearchData.tradingInstrumentOpt = [defaultItem, ...res.data.data]
    // })

    // 交易品种
    $axios({
        url: '/project/getVariety?projectType=',
        method: 'get',
    }).then((res) => {
        if (res.data.code === 200) {
            optionsBySearchData.tradingInstrumentOpt = [defaultItem, ...res.data.data]
        }
    })

    // 交易方式 LZFS
    $axios({
        url: '/basecode/getBaseCodeInfo',
        method: 'get',
        data: { baseType: 'LZFS' },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        optionsBySearchData.transactionMethodOpt = [defaultItem, ...res.data.data]
    })

    // 项目状态 XMZT_Q
    $axios({
        url: '/basecode/getBaseCodeInfo',
        method: 'get',
        data: { baseType: 'XMZT_Q' },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        optionsBySearchData.projectStatusOpt = [defaultItem, ...res.data.data]
    })
    // 标段状态
    $axios({
        url: '/tenders/statusList',
        method: 'get',
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        let defaultItem = { value: '全部', key: '' }
        optionsBySearchData.flowStatusStatusOpt = [defaultItem, ...res.data.data]
    })

    // 金额状态 JEZT_Q
    $axios({
        url: '/basecode/getBaseCodeInfo',
        method: 'get',
        data: { baseType: 'JEZT_Q' },
    }).then((res) => {
        if (res.data.code !== 200) return ElMessage.warning(res.data.msg)
        optionsBySearchData.amountStatusOpt = [defaultItem, ...res.data.data]
    })
}

getBaseCode()

// 地区树
async function isLoad(node, resolve) {
    loadchildnode(node, resolve)
}

// 加载节点
async function loadchildnode(node, resolve) {
    let params = {}
    if (node.level == 0) params = { useToken: 1 }
    else {
        params = {
            areaId: node.data.id,
            // useToken: 1,
        }
    }
    let res = await getNextAreaTree(params)

    return resolve(res)
}

function getNextAreaTree(params) {
    return $axios({
        method: 'get',
        data: params,
        url: '/area/lazyLoadRegionTree',
    }).then((res) => {
        if (res.data.code == 200) {
            return res.data.data || []
        }
    })
    return []
}

function handleNodeClick(node) {
    searchData.areaId = node.id
    // searchData.name = node.name
    // searchData.areaLevel = node.level
    console.log(searchData.areaId)
}

// 页面
var page = reactive({
    showSearchMore: false, // 是否显示更多查询条件
})
function showSearchMore() {
    page.showSearchMore = !page.showSearchMore
}

function handleTableSearch() {
    //点击查询,tab页签重置
    tabCheck.value = ''
    getListData()
}

/**
 * @description: 给数组中对象属性设置默认值'--'
 * @param {*} array
 * @return {array}
 */
function processArray(array) {
    let includesKey = ['tenderCode', 'villageName', 'identityName']

    for (let i = 0; i < array.length; i++) {
        const obj = array[i]
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (includesKey.includes(key)) {
                    if (!obj[key] && obj[key] !== 0) {
                        obj[key] = '--'
                    }
                }
            }
        }
    }
    return array
}

function clearSearch() {
    searchData.searchValue = ''
    searchData.tenderCode = ''
    searchData.busType = ''
    // searchData.feeType = '' //费用类型变为tab与查询条件互不关联
    searchData.moneyStatus = ''
    searchData.tradeKind = ['']
    searchData.signupName = ''
    searchData.tradeFlag = ''
    searchData.tradeType = ''
    searchData.areaId = ''
    searchData.proStatus = ''
    searchData.flowStatus = ''
    searchData.name = ''
}

// tab切换
const handleTabClick = (tab) => {
    searchData.feeType = optionsBySearchData.expenseTypeOpt[tab.index].dataKey
    getListData()
};
// 投标保证金 3
// 材料费 1
// 服务费 2
// 合同首期款 4
// 履约保证金 5
// 合同尾款 6
// 标书费 8

// 判断表头是否显示
function tableColShow(dataKey) {
    // 如果费用类型不在选项中，则不显示
    if (!optionsBySearchData.expenseTypeOpt?.map(item => item.dataKey).filter(item => item).includes(dataKey)) return false
    // 如果feeType为空（即选择全部）或者包含当前dataKey，则显示
    if (!searchData.feeType || searchData.feeType === dataKey) return true
    return false
}

// 重置查询条件
function resetSearch() {
    clearSearch()
    getListData()
}

const handleSizeChange = (e) => {
    pager.pageSize = e
    getListData()
}

const handleCurrentChange = (e) => {
    pager.pageNo = e
    getListData()
}
</script>
<style lang="scss" scoped>
:deep(.el-message-box) {
    max-width: 580px !important;
}

:deep(.el-select-dropdown__item.hover) {
    background-color: #fff !important;
}

:deep(.el-select-dropdown__item:hover) {
    background-color: #fff !important;
}

:deep(.el-table__row--level-1 td.el-table__cell) {
    border-bottom: none !important;
}

:deep(.el-table__row--level-1) {
    height: 20px !important;
}

/* 偶数 */
:deep(.el-table__row--level-1:nth-of-type(3n+2) .el-table__cell) {
    padding-bottom: 0 !important;
    background: #F9F9F9 !important;
}

/* 奇数 */
:deep(.el-table__row--level-1:nth-of-type(3n) .el-table__cell) {
    padding-top: 0 !important;
    background: #F9F9F9 !important;
}
</style>




<style lang="scss" scoped>
:deep(.el-badge__content.is-fixed) {
    right: calc(20px + var(--el-badge-size) / 1.5);
}

.total {
    color: #606266;
    font-family: MicrosoftYaHeiUI-Bold;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    font-weight: bold;

    .total-detail {
        display: flex;
        margin-right: 40px;

        .right {
            color: #0b8df1;
        }
    }
}

.profill {
    // position: relative;
    // left: -4px;
    word-spacing: -4px
}

.money-td {
    // display: block;
    // display: flex;
    // justify-content: center;
}

.main-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // margin-top: 15px;
    // padding: 0 15px;
    // padding-left: 10px;
    padding: 15px;
    // background-color: red;
    border: 1px solid #eeeeee;
    border-right: none;

    .search-box {
        display: flex;
        justify-content: space-between;
        height: auto;
        min-height: 40px;
        // margin-top: 10px;
    }

    .search-more-box {
        width: 100%;
        // height: 200px;
        background-color: #31b7f2;
        margin-bottom: 10px;
        // box-sizing: border-box;
        // overflow: hidden;
    }

    .footer-box {
        height: 40px;
        background-color: #f9f9f9;
        padding-right: 10px;
        border: 1px solid #eeeeee;
        // margin-top: 11px;
    }

    .operateBtn {
        color: #0b8df1;
        cursor: pointer;
    }
}
</style>
